"""
Main FastAPI application for the Trend Platform
Integrates all modules and provides unified API endpoints
"""
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request, Response, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse, PlainTextResponse
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
import time
import uuid

# Import shared components
from shared.config import settings
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger, set_correlation_id, configure_logging
from monitoring.metrics import app_metrics, get_metrics
from security.middleware import SecurityMiddleware
from security.auth import get_current_user, User
from database.connection import init_database, close_database

# Import module routers
from api.routers import (
    trends, content, deployments, dns, analytics, admin,
    templates, ai_services, assets, deployment, pipeline, dashboard, auth
)

logger = get_logger('api.main')


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Starting Trend Platform API")
    
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized")
        
        # Configure logging
        configure_logging()
        logger.info("Logging configured")
        
        # Initialize other services
        logger.info("All services initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize application: {str(e)}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down Trend Platform API")
        
        try:
            await close_database()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")


# Create FastAPI application
app = FastAPI(
    title="Trend Platform API",
    description="AI-powered trending content platform with automated deployment",
    version=settings.app_version,
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.debug else [settings.dashboard_url],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add security middleware
app.add_middleware(SecurityMiddleware)


# Request middleware for correlation ID and metrics
@app.middleware("http")
async def request_middleware(request: Request, call_next):
    """Add correlation ID and record metrics for all requests"""
    # Set correlation ID
    correlation_id = request.headers.get("X-Correlation-ID") or str(uuid.uuid4())
    set_correlation_id(correlation_id)
    
    # Record request start time
    start_time = time.time()
    
    # Process request
    response = await call_next(request)
    
    # Calculate duration
    duration = time.time() - start_time
    
    # Record metrics
    app_metrics.record_http_request(
        method=request.method,
        endpoint=request.url.path,
        status_code=response.status_code,
        duration=duration
    )
    
    # Add correlation ID to response headers
    response.headers["X-Correlation-ID"] = correlation_id
    
    # Log request
    logger.log_api_request(
        method=request.method,
        path=request.url.path,
        status_code=response.status_code,
        duration=duration,
        correlation_id=correlation_id
    )
    
    return response


# Exception handlers
@app.exception_handler(TrendPlatformException)
async def platform_exception_handler(request: Request, exc: TrendPlatformException):
    """Handle platform-specific exceptions"""
    logger.error(
        f"Platform exception: {exc.message}",
        error_code=exc.error_code,
        details=exc.details
    )
    
    return JSONResponse(
        status_code=400,
        content={
            "error": exc.error_code,
            "message": exc.message,
            "details": exc.details,
            "correlation_id": request.headers.get("X-Correlation-ID")
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {str(exc)}", error=str(exc))
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "internal_server_error",
            "message": "An internal server error occurred",
            "correlation_id": request.headers.get("X-Correlation-ID")
        }
    )


# Health check endpoints
@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "version": settings.app_version,
            "environment": settings.environment,
            "services": {}
        }

        # Check database connectivity
        try:
            from database.connection import get_db_manager
            db = await get_db_manager()
            db_healthy = await db.health_check()
            health_status["services"]["database"] = "healthy" if db_healthy else "unhealthy"
        except Exception as e:
            health_status["services"]["database"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"

        # Check AI services
        try:
            from generator.ai.ai_service_factory import get_ai_client
            ai_client = get_ai_client()
            ai_health = await ai_client.health_check()
            health_status["services"]["ai_service"] = ai_health.get("status", "unknown")
        except Exception as e:
            health_status["services"]["ai_service"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"

        # Check content pipeline
        try:
            from generator.content_pipeline import content_pipeline
            pipeline_health = await content_pipeline.health_check()
            health_status["services"]["content_pipeline"] = pipeline_health.get("status", "unknown")
        except Exception as e:
            health_status["services"]["content_pipeline"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"

        # Check deployment service
        try:
            from generator.deployment_service import deployment_service
            deployment_health = await deployment_service.health_check()
            health_status["services"]["deployment_service"] = deployment_health.get("status", "unknown")
        except Exception as e:
            health_status["services"]["deployment_service"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"

        # Check asset management
        try:
            from generator.assets import asset_manager
            storage_stats = asset_manager.get_storage_stats()
            health_status["services"]["asset_manager"] = "healthy" if storage_stats.get("exists") else "degraded"
        except Exception as e:
            health_status["services"]["asset_manager"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"

        # Check git repository
        try:
            from generator.git_ops import git_content_manager
            git_status = await git_content_manager.check_repository_status()
            health_status["services"]["git_repository"] = "healthy" if git_status.get("accessible") else "degraded"
        except Exception as e:
            health_status["services"]["git_repository"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"

        # Check pipeline orchestrator
        try:
            from generator.pipeline_orchestrator import pipeline_orchestrator
            orchestrator_health = await pipeline_orchestrator.health_check()
            health_status["services"]["pipeline_orchestrator"] = orchestrator_health.get("status", "unknown")
        except Exception as e:
            health_status["services"]["pipeline_orchestrator"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"

        # Set overall status based on critical services
        critical_services = ["database", "ai_service", "content_pipeline"]
        unhealthy_critical = [
            service for service in critical_services
            if not health_status["services"].get(service, "").startswith("healthy")
        ]

        if unhealthy_critical:
            health_status["status"] = "unhealthy"
        elif any(not status.startswith("healthy") for status in health_status["services"].values()):
            health_status["status"] = "degraded"

        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )


@app.get("/api/health/ready")
async def readiness_check():
    """Readiness check for Kubernetes"""
    return {"status": "ready", "timestamp": time.time()}


@app.get("/api/health/live")
async def liveness_check():
    """Liveness check for Kubernetes"""
    return {"status": "alive", "timestamp": time.time()}


# Metrics endpoint
@app.get("/metrics")
async def metrics_endpoint():
    """Prometheus metrics endpoint"""
    metrics_data = get_metrics()
    return PlainTextResponse(metrics_data, media_type=CONTENT_TYPE_LATEST)


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "name": "Trend Platform API",
        "version": settings.app_version,
        "environment": settings.environment,
        "docs_url": "/docs" if settings.debug else None,
        "health_url": "/api/health",
        "metrics_url": "/metrics"
    }


# Include module routers
app.include_router(auth.router, prefix="/api/auth", tags=["auth"])
app.include_router(trends.router, prefix="/api/trends", tags=["trends"])
app.include_router(content.router, prefix="/api/content", tags=["content"])
app.include_router(templates.router, prefix="/api/templates", tags=["templates"])
app.include_router(ai_services.router, prefix="/api/ai", tags=["ai_services"])
app.include_router(assets.router, prefix="/api/assets", tags=["assets"])
app.include_router(deployment.router, prefix="/api/deployment", tags=["deployment"])
app.include_router(pipeline.router, prefix="/api/pipeline", tags=["pipeline"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["dashboard"])
app.include_router(deployments.router, prefix="/api/deployments", tags=["deployments"])
app.include_router(dns.router, prefix="/api/dns", tags=["dns"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["analytics"])
app.include_router(admin.router, prefix="/api/admin", tags=["admin"])


# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket):
    """WebSocket endpoint for real-time updates"""
    await websocket.accept()
    
    try:
        while True:
            # Send periodic updates
            await asyncio.sleep(30)
            
            # Get current statistics
            from database.models.trend_model import trend_repository
            stats = await trend_repository.get_statistics()
            
            await websocket.send_json({
                "type": "stats_update",
                "data": stats,
                "timestamp": time.time()
            })
            
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
    finally:
        await websocket.close()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info"
    )

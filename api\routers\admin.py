"""
Admin API router - handles administrative endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime

from security.auth import get_current_user, require_admin, User
from database.connection import get_db_manager
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from shared.exceptions import DatabaseError, ValidationError
from shared.config import settings

logger = get_logger('api.admin')
router = APIRouter()


class SystemStatusResponse(BaseModel):
    """Response model for system status"""
    status: str
    timestamp: str
    version: str
    environment: str
    services: Dict[str, str]
    uptime: float
    memory_usage: Dict[str, Any]
    disk_usage: Dict[str, Any]


class UserResponse(BaseModel):
    """Response model for user data"""
    id: str
    email: str
    role: str
    permissions: List[str]
    is_active: bool
    last_login: Optional[str]
    created_at: str


class SystemConfigResponse(BaseModel):
    """Response model for system configuration"""
    module: str
    config: Dict[str, Any]
    last_updated: str


class TaskStatusResponse(BaseModel):
    """Response model for task status"""
    task_id: str
    task_name: str
    status: str
    result: Optional[Dict[str, Any]]
    started_at: Optional[str]
    completed_at: Optional[str]
    error: Optional[str]


class MaintenanceRequest(BaseModel):
    """Request model for maintenance operations"""
    operation: str
    parameters: Optional[Dict[str, Any]] = None


@router.get("/system-status", response_model=SystemStatusResponse)
async def get_system_status(
    current_user: User = Depends(require_admin)
):
    """Get comprehensive system status"""
    try:
        # Check database connectivity
        db = await get_db_manager()
        db_healthy = await db.health_check()
        
        # Check Redis connectivity (simplified)
        redis_healthy = True  # Would implement actual Redis check
        
        # Get system metrics
        import psutil
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Calculate uptime (simplified)
        uptime = 86400.0  # Would implement actual uptime calculation
        
        system_status = {
            "status": "healthy" if db_healthy and redis_healthy else "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": settings.app_version,
            "environment": settings.environment,
            "services": {
                "database": "healthy" if db_healthy else "unhealthy",
                "redis": "healthy" if redis_healthy else "unhealthy",
                "celery": "healthy",  # Would implement actual check
                "monitoring": "healthy"
            },
            "uptime": uptime,
            "memory_usage": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used
            },
            "disk_usage": {
                "total": disk.total,
                "free": disk.free,
                "used": disk.used,
                "percent": (disk.used / disk.total) * 100
            }
        }
        
        logger.info("Retrieved system status", user_id=current_user.id)
        
        return SystemStatusResponse(**system_status)
        
    except Exception as e:
        logger.error(f"Failed to get system status: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system status"
        )


@router.get("/users", response_model=List[UserResponse])
async def list_users(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    role: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: User = Depends(require_admin)
):
    """List all users"""
    try:
        db = await get_db_manager()
        
        # Build query with filters
        conditions = []
        params = []
        param_count = 0
        
        if role:
            param_count += 1
            conditions.append(f"role = ${param_count}")
            params.append(role)
        
        if is_active is not None:
            param_count += 1
            conditions.append(f"is_active = ${param_count}")
            params.append(is_active)
        
        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
        
        # Get total count
        count_query = f"SELECT COUNT(*) FROM user_profiles{where_clause}"
        total_count = await db.fetchval(count_query, *params)
        
        # Get paginated results
        offset = (page - 1) * page_size
        param_count += 1
        limit_param = f"${param_count}"
        params.append(page_size)
        param_count += 1
        offset_param = f"${param_count}"
        params.append(offset)
        
        query = f"""
            SELECT id, email, role, permissions, is_active, last_login, created_at
            FROM user_profiles
            {where_clause}
            ORDER BY created_at DESC
            LIMIT {limit_param} OFFSET {offset_param}
        """
        
        rows = await db.fetch(query, *params)
        users = [UserResponse(**dict(row)) for row in rows]
        
        logger.info(
            f"Listed {len(users)} users",
            user_id=current_user.id,
            page=page,
            page_size=page_size,
            total_count=total_count
        )
        
        return users
        
    except Exception as e:
        logger.error(f"Failed to list users: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users"
        )


@router.put("/users/{user_id}/role")
async def update_user_role(
    user_id: str,
    role: str,
    current_user: User = Depends(require_admin)
):
    """Update user role"""
    try:
        if role not in ["admin", "editor", "viewer"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid role. Must be admin, editor, or viewer"
            )
        
        db = await get_db_manager()
        
        # Check if user exists
        user_exists = await db.fetchval("SELECT id FROM user_profiles WHERE id = $1", user_id)
        if not user_exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update user role
        await db.execute(
            "UPDATE user_profiles SET role = $1, updated_at = NOW() WHERE id = $2",
            role, user_id
        )
        
        logger.info(
            f"Updated user role to {role}",
            user_id=current_user.id,
            target_user_id=user_id,
            new_role=role
        )
        
        return {"message": "User role updated successfully", "user_id": user_id, "new_role": role}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user role: {str(e)}", user_id=current_user.id, target_user_id=user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user role"
        )


@router.get("/config", response_model=List[SystemConfigResponse])
async def get_system_config(
    current_user: User = Depends(require_admin)
):
    """Get system configuration"""
    try:
        from shared.config import (
            SCRAPER_CONFIG, GENERATOR_CONFIG, DEPLOY_CONFIG,
            DNS_CONFIG, HOUSEKEEPING_CONFIG, MONITORING_CONFIG, SECURITY_CONFIG
        )
        
        configs = [
            {
                "module": "scraper",
                "config": SCRAPER_CONFIG,
                "last_updated": datetime.utcnow().isoformat()
            },
            {
                "module": "generator",
                "config": GENERATOR_CONFIG,
                "last_updated": datetime.utcnow().isoformat()
            },
            {
                "module": "deploy",
                "config": DEPLOY_CONFIG,
                "last_updated": datetime.utcnow().isoformat()
            },
            {
                "module": "dns",
                "config": DNS_CONFIG,
                "last_updated": datetime.utcnow().isoformat()
            },
            {
                "module": "housekeeping",
                "config": HOUSEKEEPING_CONFIG,
                "last_updated": datetime.utcnow().isoformat()
            },
            {
                "module": "monitoring",
                "config": MONITORING_CONFIG,
                "last_updated": datetime.utcnow().isoformat()
            },
            {
                "module": "security",
                "config": SECURITY_CONFIG,
                "last_updated": datetime.utcnow().isoformat()
            }
        ]
        
        logger.info("Retrieved system configuration", user_id=current_user.id)
        
        return [SystemConfigResponse(**config) for config in configs]
        
    except Exception as e:
        logger.error(f"Failed to get system config: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system configuration"
        )


@router.get("/tasks", response_model=List[TaskStatusResponse])
async def get_task_status(
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(require_admin)
):
    """Get status of recent Celery tasks"""
    try:
        # This would integrate with Celery to get actual task status
        # For now, return mock data
        tasks = [
            {
                "task_id": "task-123",
                "task_name": "scraper.tasks.scrape_trends",
                "status": "SUCCESS",
                "result": {"trends_found": 25, "trends_saved": 20},
                "started_at": datetime.utcnow().isoformat(),
                "completed_at": datetime.utcnow().isoformat(),
                "error": None
            }
        ]
        
        logger.info(f"Retrieved {len(tasks)} task statuses", user_id=current_user.id)
        
        return [TaskStatusResponse(**task) for task in tasks]
        
    except Exception as e:
        logger.error(f"Failed to get task status: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve task status"
        )


@router.post("/maintenance")
async def perform_maintenance(
    request: MaintenanceRequest,
    current_user: User = Depends(require_admin)
):
    """Perform maintenance operations"""
    try:
        if request.operation == "database_vacuum":
            # Trigger database maintenance
            from housekeeping.cleanup_manager import DatabaseMaintenanceManager
            maintenance_manager = DatabaseMaintenanceManager()
            result = await maintenance_manager.vacuum_database()
            
        elif request.operation == "cache_clear":
            # Clear application cache
            result = {"message": "Cache cleared successfully"}
            
        elif request.operation == "restart_workers":
            # Restart Celery workers (would implement actual restart)
            result = {"message": "Worker restart initiated"}
            
        elif request.operation == "sync_dns":
            # Trigger DNS sync
            from dns.tasks import sync_dns_records
            task = sync_dns_records.delay()
            result = {"message": "DNS sync started", "task_id": task.id}
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown maintenance operation: {request.operation}"
            )
        
        logger.info(
            f"Performed maintenance operation: {request.operation}",
            user_id=current_user.id,
            operation=request.operation,
            parameters=request.parameters
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to perform maintenance: {str(e)}", user_id=current_user.id, operation=request.operation)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform maintenance operation"
        )


@router.get("/logs")
async def get_system_logs(
    level: str = Query("INFO", regex="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$"),
    limit: int = Query(100, ge=1, le=1000),
    module: Optional[str] = None,
    current_user: User = Depends(require_admin)
):
    """Get system logs"""
    try:
        db = await get_db_manager()
        
        # Build query with filters
        conditions = ["level >= $1"]
        params = [level]
        param_count = 1
        
        if module:
            param_count += 1
            conditions.append(f"module = ${param_count}")
            params.append(module)
        
        where_clause = " WHERE " + " AND ".join(conditions)
        
        param_count += 1
        limit_param = f"${param_count}"
        params.append(limit)
        
        query = f"""
            SELECT id, level, module, message, metadata, created_at
            FROM system_logs
            {where_clause}
            ORDER BY created_at DESC
            LIMIT {limit_param}
        """
        
        rows = await db.fetch(query, *params)
        logs = [dict(row) for row in rows]
        
        logger.info(
            f"Retrieved {len(logs)} system logs",
            user_id=current_user.id,
            level=level,
            module=module,
            limit=limit
        )
        
        return {"logs": logs, "count": len(logs)}
        
    except Exception as e:
        logger.error(f"Failed to get system logs: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system logs"
        )

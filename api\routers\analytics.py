"""
Analytics API router - handles analytics and reporting endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime, timedelta

from security.auth import get_current_user, require_editor, require_admin, User
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository
from database.models.deployment_model import DeploymentRepository
from database.models.dns_model import DNSRepository
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from shared.exceptions import DatabaseError, ValidationError

logger = get_logger('api.analytics')
router = APIRouter()
trend_repo = TrendRepository()
content_repo = ContentRepository()
deployment_repo = DeploymentRepository()
dns_repo = DNSRepository()


class TrendAnalyticsResponse(BaseModel):
    """Response model for trend analytics"""
    trend_id: str
    keyword: str
    slug: str
    search_volume: Optional[int]
    growth_rate: Optional[float]
    score: Optional[float]
    page_views: int
    unique_visitors: int
    bounce_rate: Optional[float]
    avg_session_duration: Optional[float]
    conversion_rate: Optional[float]
    created_at: str
    deployed_at: Optional[str]


class SystemMetricsResponse(BaseModel):
    """Response model for system metrics"""
    timestamp: str
    total_trends: int
    active_trends: int
    pending_trends: int
    live_deployments: int
    total_page_views: int
    unique_visitors: int
    avg_response_time: float
    error_rate: float
    uptime_percentage: float


class PerformanceMetricsResponse(BaseModel):
    """Response model for performance metrics"""
    scraper_metrics: Dict[str, Any]
    generator_metrics: Dict[str, Any]
    deployment_metrics: Dict[str, Any]
    dns_metrics: Dict[str, Any]
    database_metrics: Dict[str, Any]


class ReportResponse(BaseModel):
    """Response model for analytics reports"""
    report_type: str
    period: str
    generated_at: str
    data: Dict[str, Any]
    summary: Dict[str, Any]


@router.get("/trends", response_model=List[TrendAnalyticsResponse])
async def get_trend_analytics(
    period: str = Query("7d", regex="^(1d|7d|30d|90d)$"),
    limit: int = Query(50, ge=1, le=100),
    sort_by: str = Query("page_views", regex="^(page_views|unique_visitors|score|created_at)$"),
    current_user: User = Depends(get_current_user)
):
    """Get trend analytics data"""
    try:
        # Calculate date range
        days_map = {"1d": 1, "7d": 7, "30d": 30, "90d": 90}
        days = days_map[period]
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Get trends with analytics data
        trends_data = await trend_repo.get_trends_with_analytics(
            start_date=start_date,
            limit=limit,
            sort_by=sort_by
        )
        
        logger.info(
            f"Retrieved trend analytics",
            user_id=current_user.id,
            period=period,
            limit=limit,
            count=len(trends_data)
        )
        
        return [TrendAnalyticsResponse(**trend) for trend in trends_data]
        
    except Exception as e:
        logger.error(f"Failed to get trend analytics: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve trend analytics"
        )


@router.get("/system-metrics", response_model=SystemMetricsResponse)
async def get_system_metrics(
    current_user: User = Depends(get_current_user)
):
    """Get current system metrics"""
    try:
        # Get current statistics from repositories
        trend_stats = await trend_repo.get_statistics()
        deployment_stats = await deployment_repo.get_statistics()
        
        # Get metrics from monitoring system
        metrics_data = app_metrics.get_current_metrics()
        
        system_metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "total_trends": trend_stats.get('total_trends', 0),
            "active_trends": trend_stats.get('live_trends', 0),
            "pending_trends": trend_stats.get('pending_trends', 0),
            "live_deployments": deployment_stats.get('successful_deployments', 0),
            "total_page_views": metrics_data.get('total_page_views', 0),
            "unique_visitors": metrics_data.get('unique_visitors', 0),
            "avg_response_time": metrics_data.get('avg_response_time', 0.0),
            "error_rate": metrics_data.get('error_rate', 0.0),
            "uptime_percentage": metrics_data.get('uptime_percentage', 100.0)
        }
        
        logger.info("Retrieved system metrics", user_id=current_user.id)
        
        return SystemMetricsResponse(**system_metrics)
        
    except Exception as e:
        logger.error(f"Failed to get system metrics: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system metrics"
        )


@router.get("/performance", response_model=PerformanceMetricsResponse)
async def get_performance_metrics(
    current_user: User = Depends(get_current_user)
):
    """Get performance metrics for all modules"""
    try:
        # Get performance metrics from monitoring system
        metrics_data = app_metrics.get_performance_metrics()
        
        performance_metrics = {
            "scraper_metrics": {
                "total_scrapes": metrics_data.get('scraper_total_scrapes', 0),
                "successful_scrapes": metrics_data.get('scraper_successful_scrapes', 0),
                "avg_scrape_duration": metrics_data.get('scraper_avg_duration', 0.0),
                "trends_found": metrics_data.get('scraper_trends_found', 0)
            },
            "generator_metrics": {
                "total_generations": metrics_data.get('generator_total_generations', 0),
                "successful_generations": metrics_data.get('generator_successful_generations', 0),
                "avg_generation_duration": metrics_data.get('generator_avg_duration', 0.0),
                "content_created": metrics_data.get('generator_content_created', 0)
            },
            "deployment_metrics": {
                "total_deployments": metrics_data.get('deployment_total_deployments', 0),
                "successful_deployments": metrics_data.get('deployment_successful_deployments', 0),
                "avg_deployment_duration": metrics_data.get('deployment_avg_duration', 0.0),
                "success_rate": metrics_data.get('deployment_success_rate', 0.0)
            },
            "dns_metrics": {
                "total_dns_operations": metrics_data.get('dns_total_operations', 0),
                "successful_dns_operations": metrics_data.get('dns_successful_operations', 0),
                "avg_dns_duration": metrics_data.get('dns_avg_duration', 0.0),
                "active_records": metrics_data.get('dns_active_records', 0)
            },
            "database_metrics": {
                "total_queries": metrics_data.get('database_total_queries', 0),
                "avg_query_duration": metrics_data.get('database_avg_query_duration', 0.0),
                "active_connections": metrics_data.get('database_active_connections', 0),
                "connection_pool_usage": metrics_data.get('database_pool_usage', 0.0)
            }
        }
        
        logger.info("Retrieved performance metrics", user_id=current_user.id)
        
        return PerformanceMetricsResponse(**performance_metrics)
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance metrics"
        )


@router.get("/reports/{report_type}")
async def generate_report(
    report_type: str,
    period: str = Query("7d", regex="^(1d|7d|30d|90d)$"),
    format: str = Query("json", regex="^(json|csv)$"),
    current_user: User = Depends(require_editor)
):
    """Generate analytics report"""
    try:
        # Calculate date range
        days_map = {"1d": 1, "7d": 7, "30d": 30, "90d": 90}
        days = days_map[period]
        start_date = datetime.utcnow() - timedelta(days=days)
        end_date = datetime.utcnow()
        
        report_data = {}
        summary = {}
        
        if report_type == "trends":
            # Generate trends report
            trends_data = await trend_repo.get_trends_with_analytics(
                start_date=start_date,
                limit=1000,
                sort_by="page_views"
            )
            
            report_data = {
                "trends": trends_data,
                "period": period,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
            
            summary = {
                "total_trends": len(trends_data),
                "total_page_views": sum(t.get('page_views', 0) for t in trends_data),
                "total_unique_visitors": sum(t.get('unique_visitors', 0) for t in trends_data),
                "avg_score": sum(t.get('score', 0) for t in trends_data if t.get('score')) / len([t for t in trends_data if t.get('score')]) if trends_data else 0
            }
            
        elif report_type == "performance":
            # Generate performance report
            performance_data = app_metrics.get_performance_metrics()
            
            report_data = {
                "performance_metrics": performance_data,
                "period": period,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
            
            summary = {
                "avg_response_time": performance_data.get('avg_response_time', 0.0),
                "error_rate": performance_data.get('error_rate', 0.0),
                "uptime_percentage": performance_data.get('uptime_percentage', 100.0)
            }
            
        elif report_type == "deployments":
            # Generate deployments report
            deployment_stats = await deployment_repo.get_statistics()
            
            report_data = {
                "deployment_stats": deployment_stats,
                "period": period,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
            
            summary = {
                "total_deployments": deployment_stats.get('total_deployments', 0),
                "successful_deployments": deployment_stats.get('successful_deployments', 0),
                "success_rate": deployment_stats.get('success_rate', 0.0),
                "avg_build_time": deployment_stats.get('avg_build_time', 0.0)
            }
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown report type: {report_type}"
            )
        
        report = {
            "report_type": report_type,
            "period": period,
            "generated_at": datetime.utcnow().isoformat(),
            "data": report_data,
            "summary": summary
        }
        
        logger.info(
            f"Generated {report_type} report",
            user_id=current_user.id,
            report_type=report_type,
            period=period,
            format=format
        )
        
        if format == "json":
            return ReportResponse(**report)
        else:
            # TODO: Implement CSV export
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="CSV export not yet implemented"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to generate report: {str(e)}", user_id=current_user.id, report_type=report_type)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate report"
        )


@router.get("/dashboard-data")
async def get_dashboard_data(
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive dashboard data"""
    try:
        # Get data for dashboard widgets
        trend_stats = await trend_repo.get_statistics()
        content_stats = await content_repo.get_statistics()
        deployment_stats = await deployment_repo.get_statistics()
        dns_stats = await dns_repo.get_statistics()
        
        # Get recent trends
        recent_trends = await trend_repo.get_recent_trends(limit=10)
        
        # Get active deployments
        active_deployments = await deployment_repo.get_active_deployments()
        
        dashboard_data = {
            "overview": {
                "total_trends": trend_stats.get('total_trends', 0),
                "pending_trends": trend_stats.get('pending_trends', 0),
                "live_trends": trend_stats.get('live_trends', 0),
                "total_content": content_stats.get('total_content', 0),
                "ready_for_deployment": content_stats.get('ready_for_deployment', 0),
                "active_deployments": len(active_deployments),
                "active_dns_records": dns_stats.get('active_records', 0)
            },
            "recent_activity": {
                "recent_trends": [
                    {
                        "id": trend.id,
                        "keyword": trend.keyword,
                        "status": trend.status,
                        "score": trend.score,
                        "created_at": trend.created_at.isoformat()
                    } for trend in recent_trends
                ],
                "active_deployments": [
                    {
                        "id": deployment.id,
                        "trend_id": deployment.trend_id,
                        "status": deployment.status,
                        "started_at": deployment.started_at.isoformat() if deployment.started_at else None
                    } for deployment in active_deployments
                ]
            },
            "performance": app_metrics.get_current_metrics()
        }
        
        logger.info("Retrieved dashboard data", user_id=current_user.id)
        
        return dashboard_data
        
    except Exception as e:
        logger.error(f"Failed to get dashboard data: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard data"
        )

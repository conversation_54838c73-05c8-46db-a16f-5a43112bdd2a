"""
API endpoints for asset management
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from pydantic import BaseModel, HttpUrl
from datetime import datetime

from generator.assets import asset_manager, ImageProcessingOptions
from generator.asset_integration import content_asset_processor
from generator.models import ContentAsset
from shared.auth import get_current_user, require_permissions
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger

logger = get_logger('api.assets')
router = APIRouter(prefix="/assets", tags=["assets"])


class ProcessImageRequest(BaseModel):
    """Request model for image processing"""
    url: HttpUrl
    filename: Optional[str] = None
    max_width: Optional[int] = None
    max_height: Optional[int] = None
    quality: Optional[int] = None
    format: Optional[str] = None


class AssetResponse(BaseModel):
    """Response model for asset information"""
    filename: str
    original_url: Optional[str] = None
    local_path: str
    optimized_path: Optional[str] = None
    file_size: int
    optimized_size: Optional[int] = None
    mime_type: str
    width: Optional[int] = None
    height: Optional[int] = None
    alt_text: Optional[str] = None
    created_at: datetime


class ProcessingResultResponse(BaseModel):
    """Response model for processing results"""
    success: bool
    original_asset: Optional[AssetResponse] = None
    processed_asset: Optional[AssetResponse] = None
    webp_asset: Optional[AssetResponse] = None
    processing_time: float = 0.0
    size_reduction: float = 0.0
    error_message: Optional[str] = None


class StorageStatsResponse(BaseModel):
    """Response model for storage statistics"""
    exists: bool
    path: Optional[str] = None
    file_count: Optional[int] = None
    total_size_bytes: Optional[int] = None
    total_size_mb: Optional[float] = None
    error: Optional[str] = None


@router.post("/process-image", response_model=ProcessingResultResponse)
async def process_image(
    request: ProcessImageRequest,
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["assets:process"]))
):
    """
    Process image from URL
    
    Requires: assets:process permission
    """
    try:
        logger.info(
            f"Processing image from URL",
            url=str(request.url),
            user_id=current_user.get('id'),
            filename=request.filename
        )
        
        # Create processing options
        options = ImageProcessingOptions()
        if request.max_width:
            options.max_width = request.max_width
        if request.max_height:
            options.max_height = request.max_height
        if request.quality:
            options.quality = request.quality
        if request.format:
            options.format = request.format
        
        # Process image
        result = await asset_manager.download_and_process(
            url=str(request.url),
            filename=request.filename,
            options=options
        )
        
        # Convert to response format
        def asset_to_response(asset: ContentAsset) -> AssetResponse:
            return AssetResponse(
                filename=asset.filename,
                original_url=asset.original_url,
                local_path=asset.local_path,
                optimized_path=asset.optimized_path,
                file_size=asset.file_size,
                optimized_size=asset.optimized_size,
                mime_type=asset.mime_type,
                width=asset.width,
                height=asset.height,
                alt_text=asset.alt_text,
                created_at=asset.created_at
            )
        
        response = ProcessingResultResponse(
            success=result.success,
            processing_time=result.processing_time,
            size_reduction=result.size_reduction,
            error_message=result.error_message
        )
        
        if result.original_asset:
            response.original_asset = asset_to_response(result.original_asset)
        if result.processed_asset:
            response.processed_asset = asset_to_response(result.processed_asset)
        if result.webp_asset:
            response.webp_asset = asset_to_response(result.webp_asset)
        
        logger.info(
            f"Image processing completed",
            success=result.success,
            processing_time=result.processing_time,
            size_reduction=result.size_reduction
        )
        
        return response
        
    except TrendPlatformException as e:
        logger.error(f"Image processing failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error during image processing: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/upload-image", response_model=ProcessingResultResponse)
async def upload_image(
    file: UploadFile = File(...),
    max_width: Optional[int] = Form(None),
    max_height: Optional[int] = Form(None),
    quality: Optional[int] = Form(None),
    format: Optional[str] = Form(None),
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["assets:upload"]))
):
    """
    Upload and process image file
    
    Requires: assets:upload permission
    """
    try:
        logger.info(
            f"Processing uploaded image",
            filename=file.filename,
            content_type=file.content_type,
            user_id=current_user.get('id')
        )
        
        # Validate file type
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read file data
        file_data = await file.read()
        
        if len(file_data) == 0:
            raise HTTPException(status_code=400, detail="Empty file")
        
        # Save temporary file
        import tempfile
        import aiofiles
        
        temp_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
                temp_path = temp_file.name
            
            async with aiofiles.open(temp_path, 'wb') as f:
                await f.write(file_data)
            
            # Create asset from uploaded file
            asset = ContentAsset(
                filename=file.filename or "uploaded_image",
                local_path=temp_path,
                file_size=len(file_data),
                mime_type=file.content_type,
                created_at=datetime.utcnow()
            )
            
            # Get image dimensions
            width, height = asset_manager._get_image_dimensions(temp_path)
            asset.width = width
            asset.height = height
            
            # Create processing options
            options = ImageProcessingOptions()
            if max_width:
                options.max_width = max_width
            if max_height:
                options.max_height = max_height
            if quality:
                options.quality = quality
            if format:
                options.format = format
            
            # Process image
            result = await asset_manager.process_image(asset, options)
            
            # Convert to response format
            def asset_to_response(asset: ContentAsset) -> AssetResponse:
                return AssetResponse(
                    filename=asset.filename,
                    original_url=asset.original_url,
                    local_path=asset.local_path,
                    optimized_path=asset.optimized_path,
                    file_size=asset.file_size,
                    optimized_size=asset.optimized_size,
                    mime_type=asset.mime_type,
                    width=asset.width,
                    height=asset.height,
                    alt_text=asset.alt_text,
                    created_at=asset.created_at
                )
            
            response = ProcessingResultResponse(
                success=result.success,
                processing_time=result.processing_time,
                size_reduction=result.size_reduction,
                error_message=result.error_message
            )
            
            if result.original_asset:
                response.original_asset = asset_to_response(result.original_asset)
            if result.processed_asset:
                response.processed_asset = asset_to_response(result.processed_asset)
            if result.webp_asset:
                response.webp_asset = asset_to_response(result.webp_asset)
            
            logger.info(
                f"Image upload and processing completed",
                success=result.success,
                processing_time=result.processing_time,
                size_reduction=result.size_reduction
            )
            
            return response
            
        finally:
            # Cleanup temporary file
            if temp_path and os.path.exists(temp_path):
                os.unlink(temp_path)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during image upload: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/storage-stats", response_model=StorageStatsResponse)
async def get_storage_stats(
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["assets:read"]))
):
    """
    Get asset storage statistics
    
    Requires: assets:read permission
    """
    try:
        stats = asset_manager.get_storage_stats()
        
        return StorageStatsResponse(
            exists=stats.get('exists', False),
            path=stats.get('path'),
            file_count=stats.get('file_count'),
            total_size_bytes=stats.get('total_size_bytes'),
            total_size_mb=stats.get('total_size_mb'),
            error=stats.get('error')
        )
        
    except Exception as e:
        logger.error(f"Failed to get storage stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get storage statistics")


@router.post("/cleanup")
async def cleanup_assets(
    max_age_hours: int = 24,
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["assets:manage"]))
):
    """
    Cleanup old asset files
    
    Requires: assets:manage permission
    """
    try:
        logger.info(
            f"Starting asset cleanup",
            max_age_hours=max_age_hours,
            user_id=current_user.get('id')
        )
        
        await asset_manager.cleanup_temp_files(max_age_hours=max_age_hours)
        
        return {
            "message": f"Asset cleanup completed for files older than {max_age_hours} hours",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Asset cleanup failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Asset cleanup failed")


@router.get("/processing-stats")
async def get_processing_stats(
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["assets:read"]))
):
    """
    Get asset processing statistics
    
    Requires: assets:read permission
    """
    try:
        stats = await content_asset_processor.get_asset_stats()
        
        return {
            "asset_processing": stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get processing stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get processing statistics")


@router.get("/health")
async def asset_health_check():
    """
    Health check for asset management system
    
    Public endpoint for monitoring
    """
    try:
        storage_stats = asset_manager.get_storage_stats()
        processing_stats = await content_asset_processor.get_asset_stats()
        
        return {
            "status": "healthy" if storage_stats.get('exists', False) else "degraded",
            "storage": {
                "accessible": storage_stats.get('exists', False),
                "file_count": storage_stats.get('file_count', 0),
                "total_size_mb": storage_stats.get('total_size_mb', 0)
            },
            "processing": {
                "webp_enabled": processing_stats.get('processing_options', {}).get('webp_enabled', False),
                "optimization_enabled": processing_stats.get('processing_options', {}).get('optimization_enabled', False)
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Asset health check failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

"""
Content API router - handles content management endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from security.auth import get_current_user, require_editor, require_admin, User
from database.models.content_model import (
    ContentRepository, ContentEntity, 
    ContentCreateRequest, ContentUpdateRequest
)
from database.models.trend_model import TrendRepository
from monitoring.logger import get_logger
from shared.exceptions import DatabaseError, ValidationError

logger = get_logger('api.content')
router = APIRouter()
content_repo = ContentRepository()
trend_repo = TrendRepository()


class ContentResponse(BaseModel):
    """Response model for content data"""
    id: str
    trend_id: str
    title: str
    description: Optional[str]
    body: str
    meta_tags: Dict[str, str]
    hero_image_url: Optional[str]
    hero_image_alt: Optional[str]
    code_snippet: Optional[str]
    code_language: Optional[str]
    word_count: Optional[int]
    readability_score: Optional[float]
    ai_model_used: Optional[str]
    generation_metadata: Dict[str, Any]
    created_at: str
    updated_at: str
    created_by: Optional[str]


class ContentListResponse(BaseModel):
    """Response model for content list with pagination"""
    data: List[ContentResponse]
    pagination: Dict[str, Any]


class ContentStatsResponse(BaseModel):
    """Response model for content statistics"""
    total_content: int
    ready_for_deployment: int
    deployed_content: int
    avg_word_count: Optional[float]
    avg_readability_score: Optional[float]


@router.get("/", response_model=ContentListResponse)
async def list_content(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    trend_id: Optional[str] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """List content with filtering and pagination"""
    try:
        filters = {}
        if trend_id:
            filters['trend_id'] = trend_id
        
        if search:
            # Use search functionality
            content_list = await content_repo.search(search, ['title', 'body'], limit=page_size)
            result = {
                'data': [ContentResponse(**content.dict()) for content in content_list],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': len(content_list),
                    'total_pages': 1,
                    'has_next': False,
                    'has_prev': False
                }
            }
        else:
            result = await content_repo.list_with_pagination(
                page=page,
                page_size=page_size,
                filters=filters
            )
            
            # Convert entities to response models
            result['data'] = [ContentResponse(**content.dict()) for content in result['data']]
        
        logger.info(
            f"Listed content",
            user_id=current_user.id,
            page=page,
            page_size=page_size,
            filters=filters,
            total_count=result['pagination']['total_count']
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to list content: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve content"
        )


@router.get("/ready-for-deployment", response_model=List[ContentResponse])
async def get_ready_for_deployment(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(require_editor)
):
    """Get content ready for deployment"""
    try:
        content_list = await content_repo.get_ready_for_deployment(limit=limit)
        
        logger.info(
            f"Retrieved {len(content_list)} content items ready for deployment",
            user_id=current_user.id,
            count=len(content_list)
        )
        
        return [ContentResponse(**content) for content in content_list]
        
    except Exception as e:
        logger.error(f"Failed to get content ready for deployment: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve content ready for deployment"
        )


@router.get("/stats", response_model=ContentStatsResponse)
async def get_content_statistics(
    current_user: User = Depends(get_current_user)
):
    """Get content statistics"""
    try:
        stats = await content_repo.get_statistics()
        
        logger.info("Retrieved content statistics", user_id=current_user.id)
        
        return ContentStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Failed to get content statistics: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve statistics"
        )


@router.get("/{content_id}", response_model=ContentResponse)
async def get_content(
    content_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get specific content by ID"""
    try:
        content = await content_repo.get_by_id(content_id)
        
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )
        
        logger.info(f"Retrieved content: {content.title}", user_id=current_user.id, content_id=content_id)
        
        return ContentResponse(**content.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get content: {str(e)}", user_id=current_user.id, content_id=content_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve content"
        )


@router.post("/", response_model=ContentResponse)
async def create_content(
    request: ContentCreateRequest,
    current_user: User = Depends(require_editor)
):
    """Create new content"""
    try:
        # Verify trend exists
        trend = await trend_repo.get_by_id(request.trend_id)
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        # Create content
        content_data = request.dict()
        content_data['created_by'] = current_user.id
        
        content = await content_repo.create(content_data)
        
        logger.info(
            f"Created content: {content.title}",
            user_id=current_user.id,
            content_id=content.id,
            trend_id=request.trend_id
        )
        
        return ContentResponse(**content.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create content: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create content"
        )


@router.put("/{content_id}", response_model=ContentResponse)
async def update_content(
    content_id: str,
    request: ContentUpdateRequest,
    current_user: User = Depends(require_editor)
):
    """Update existing content"""
    try:
        # Verify content exists
        content = await content_repo.get_by_id(content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )
        
        # Update content
        update_data = request.dict(exclude_unset=True)
        updated_content = await content_repo.update(content_id, update_data)
        
        logger.info(
            f"Updated content: {updated_content.title}",
            user_id=current_user.id,
            content_id=content_id
        )
        
        return ContentResponse(**updated_content.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update content: {str(e)}", user_id=current_user.id, content_id=content_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update content"
        )


@router.delete("/{content_id}")
async def delete_content(
    content_id: str,
    current_user: User = Depends(require_admin)
):
    """Delete content"""
    try:
        # Verify content exists
        content = await content_repo.get_by_id(content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )
        
        # Delete content
        success = await content_repo.delete(content_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete content"
            )
        
        logger.info(
            f"Deleted content: {content.title}",
            user_id=current_user.id,
            content_id=content_id
        )
        
        return {"message": "Content deleted successfully", "content_id": content_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete content: {str(e)}", user_id=current_user.id, content_id=content_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete content"
        )


@router.post("/{content_id}/regenerate")
async def regenerate_content(
    content_id: str,
    current_user: User = Depends(require_editor)
):
    """Trigger content regeneration"""
    try:
        # Verify content exists
        content = await content_repo.get_by_id(content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )
        
        # Trigger regeneration task
        from generator.tasks import regenerate_content_task
        task = regenerate_content_task.delay(content_id)
        
        logger.info(
            f"Triggered content regeneration: {content.title}",
            user_id=current_user.id,
            content_id=content_id,
            task_id=task.id
        )
        
        return {"message": "Content regeneration started", "task_id": task.id, "content_id": content_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to trigger content regeneration: {str(e)}", user_id=current_user.id, content_id=content_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger content regeneration"
        )

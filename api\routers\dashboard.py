"""
Dashboard integration API endpoints
Provides comprehensive data for the admin dashboard
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
from datetime import datetime, timedelta
from sqlalchemy import func, desc

from generator.pipeline_orchestrator import pipeline_orchestrator
from generator.deployment_service import deployment_service
from generator.assets import asset_manager
from generator.asset_integration import content_asset_processor
from database.models.trend_model import TrendRepository, TrendEntity
from database.models.content_model import ContentRepository
from shared.auth import get_current_user, require_permissions
from monitoring.metrics import app_metrics
from monitoring.logger import get_logger

logger = get_logger('api.dashboard')
router = APIRouter(prefix="/dashboard", tags=["dashboard"])


class DashboardStatsResponse(BaseModel):
    """Response model for dashboard statistics"""
    trends: Dict[str, Any]
    content: Dict[str, Any]
    assets: Dict[str, Any]
    pipeline: Dict[str, Any]
    system: Dict[str, Any]
    timestamp: str


class TrendSummaryResponse(BaseModel):
    """Response model for trend summary"""
    id: str
    keyword: str
    category: str
    region: str
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    score: Optional[float] = None
    status: str
    created_at: datetime
    has_content: bool = False
    is_deployed: bool = False
    last_updated: Optional[datetime] = None


class ContentSummaryResponse(BaseModel):
    """Response model for content summary"""
    id: str
    trend_id: str
    title: str
    slug: Optional[str] = None
    word_count: Optional[int] = None
    readability_score: Optional[float] = None
    has_hero_image: bool = False
    has_code_snippet: bool = False
    ai_model_used: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None


class RecentActivityResponse(BaseModel):
    """Response model for recent activity"""
    activity_type: str
    description: str
    trend_keyword: Optional[str] = None
    timestamp: datetime
    status: str
    details: Dict[str, Any] = {}


@router.get("/stats", response_model=DashboardStatsResponse)
async def get_dashboard_stats(
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["dashboard:read"]))
):
    """
    Get comprehensive dashboard statistics
    
    Requires: dashboard:read permission
    """
    try:
        trend_repo = TrendRepository()
        content_repo = ContentRepository()
        
        # Get trend statistics
        trend_stats = await _get_trend_statistics(trend_repo)
        
        # Get content statistics
        content_stats = await _get_content_statistics(content_repo)
        
        # Get asset statistics
        asset_stats = await _get_asset_statistics()
        
        # Get pipeline statistics
        pipeline_stats = await _get_pipeline_statistics()
        
        # Get system statistics
        system_stats = await _get_system_statistics()
        
        return DashboardStatsResponse(
            trends=trend_stats,
            content=content_stats,
            assets=asset_stats,
            pipeline=pipeline_stats,
            system=system_stats,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Failed to get dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard statistics")


@router.get("/trends/recent", response_model=List[TrendSummaryResponse])
async def get_recent_trends(
    limit: int = Query(10, ge=1, le=100),
    status: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["trends:read"]))
):
    """
    Get recent trends with content status
    
    Requires: trends:read permission
    """
    try:
        trend_repo = TrendRepository()
        content_repo = ContentRepository()
        
        # Build filters
        filters = {}
        if status:
            filters['status'] = status
        if category:
            filters['category'] = category
        
        # Get trends
        trends = await trend_repo.get_recent(limit=limit, filters=filters)
        
        # Get content status for each trend
        trend_summaries = []
        for trend in trends:
            # Check if content exists
            content = await content_repo.get_by_trend_id(trend.id)
            
            summary = TrendSummaryResponse(
                id=trend.id,
                keyword=trend.keyword,
                category=trend.category,
                region=trend.region,
                search_volume=trend.search_volume,
                growth_rate=trend.growth_rate,
                score=trend.score,
                status=trend.status,
                created_at=trend.created_at,
                has_content=bool(content),
                is_deployed=bool(content and content.generation_metadata and 
                               content.generation_metadata.get('git_commit_hash')),
                last_updated=content.updated_at if content else None
            )
            
            trend_summaries.append(summary)
        
        return trend_summaries
        
    except Exception as e:
        logger.error(f"Failed to get recent trends: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get recent trends")


@router.get("/content/recent", response_model=List[ContentSummaryResponse])
async def get_recent_content(
    limit: int = Query(10, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:read"]))
):
    """
    Get recently generated content
    
    Requires: content:read permission
    """
    try:
        content_repo = ContentRepository()
        
        # Get recent content
        content_items = await content_repo.get_recent(limit=limit)
        
        content_summaries = []
        for content in content_items:
            summary = ContentSummaryResponse(
                id=content.id,
                trend_id=content.trend_id,
                title=content.title,
                slug=content.slug,
                word_count=content.word_count,
                readability_score=content.readability_score,
                has_hero_image=bool(content.hero_image_url),
                has_code_snippet=bool(content.code_snippet),
                ai_model_used=content.ai_model_used,
                created_at=content.created_at,
                updated_at=content.updated_at
            )
            
            content_summaries.append(summary)
        
        return content_summaries
        
    except Exception as e:
        logger.error(f"Failed to get recent content: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get recent content")


@router.get("/activity/recent", response_model=List[RecentActivityResponse])
async def get_recent_activity(
    limit: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["dashboard:read"]))
):
    """
    Get recent system activity
    
    Requires: dashboard:read permission
    """
    try:
        # This would typically query an activity log table
        # For now, we'll return a placeholder response
        activities = []
        
        # Get recent trends and content as activity
        trend_repo = TrendRepository()
        content_repo = ContentRepository()
        
        recent_trends = await trend_repo.get_recent(limit=limit//2)
        recent_content = await content_repo.get_recent(limit=limit//2)
        
        # Add trend activities
        for trend in recent_trends:
            activities.append(RecentActivityResponse(
                activity_type="trend_created",
                description=f"New trend '{trend.keyword}' added",
                trend_keyword=trend.keyword,
                timestamp=trend.created_at,
                status=trend.status,
                details={
                    "category": trend.category,
                    "region": trend.region,
                    "score": trend.score
                }
            ))
        
        # Add content activities
        for content in recent_content:
            activities.append(RecentActivityResponse(
                activity_type="content_generated",
                description=f"Content generated for '{content.title}'",
                timestamp=content.created_at,
                status="completed",
                details={
                    "word_count": content.word_count,
                    "ai_model": content.ai_model_used,
                    "has_image": bool(content.hero_image_url)
                }
            ))
        
        # Sort by timestamp
        activities.sort(key=lambda x: x.timestamp, reverse=True)
        
        return activities[:limit]
        
    except Exception as e:
        logger.error(f"Failed to get recent activity: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get recent activity")


@router.get("/performance/metrics")
async def get_performance_metrics(
    hours: int = Query(24, ge=1, le=168),  # Last 24 hours by default, max 1 week
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["dashboard:read"]))
):
    """
    Get performance metrics for the specified time period
    
    Requires: dashboard:read permission
    """
    try:
        # Get metrics from the monitoring system
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours)
        
        # This would typically query a metrics database
        # For now, we'll return mock data structure
        metrics = {
            "time_range": {
                "start": start_time.isoformat(),
                "end": end_time.isoformat(),
                "hours": hours
            },
            "content_generation": {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "average_time": 0.0,
                "success_rate": 0.0
            },
            "asset_processing": {
                "images_processed": 0,
                "total_size_mb": 0.0,
                "average_compression": 0.0,
                "webp_conversions": 0
            },
            "deployments": {
                "total_deployments": 0,
                "successful_deployments": 0,
                "failed_deployments": 0,
                "average_deployment_time": 0.0
            },
            "ai_usage": {
                "total_api_calls": 0,
                "total_tokens": 0,
                "estimated_cost": 0.0,
                "providers_used": []
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get performance metrics")


async def _get_trend_statistics(trend_repo: TrendRepository) -> Dict[str, Any]:
    """Get trend statistics"""
    try:
        # Get basic counts
        total_trends = await trend_repo.count()
        
        # Get status breakdown
        status_counts = await trend_repo.get_status_counts()
        
        # Get category breakdown
        category_counts = await trend_repo.get_category_counts()
        
        # Get recent trends count (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_trends = await trend_repo.count_since(week_ago)
        
        return {
            "total": total_trends,
            "recent_week": recent_trends,
            "by_status": status_counts,
            "by_category": category_counts,
            "growth_rate": (recent_trends / max(total_trends - recent_trends, 1)) * 100 if total_trends > recent_trends else 0
        }
        
    except Exception as e:
        logger.warning(f"Failed to get trend statistics: {str(e)}")
        return {"total": 0, "recent_week": 0, "by_status": {}, "by_category": {}, "growth_rate": 0}


async def _get_content_statistics(content_repo: ContentRepository) -> Dict[str, Any]:
    """Get content statistics"""
    try:
        # Get basic counts
        total_content = await content_repo.count()
        
        # Get content with images
        content_with_images = await content_repo.count_with_images()
        
        # Get content with code
        content_with_code = await content_repo.count_with_code()
        
        # Get average metrics
        avg_metrics = await content_repo.get_average_metrics()
        
        return {
            "total": total_content,
            "with_images": content_with_images,
            "with_code": content_with_code,
            "average_word_count": avg_metrics.get('word_count', 0),
            "average_readability": avg_metrics.get('readability_score', 0),
            "image_percentage": (content_with_images / max(total_content, 1)) * 100,
            "code_percentage": (content_with_code / max(total_content, 1)) * 100
        }
        
    except Exception as e:
        logger.warning(f"Failed to get content statistics: {str(e)}")
        return {"total": 0, "with_images": 0, "with_code": 0, "average_word_count": 0, "average_readability": 0}


async def _get_asset_statistics() -> Dict[str, Any]:
    """Get asset statistics"""
    try:
        # Get storage stats
        storage_stats = asset_manager.get_storage_stats()
        
        # Get processing stats
        processing_stats = await content_asset_processor.get_asset_stats()
        
        return {
            "storage": storage_stats,
            "processing": processing_stats.get('processing_options', {}),
            "total_files": storage_stats.get('file_count', 0),
            "total_size_mb": storage_stats.get('total_size_mb', 0),
            "webp_enabled": processing_stats.get('processing_options', {}).get('webp_enabled', False),
            "optimization_enabled": processing_stats.get('processing_options', {}).get('optimization_enabled', False)
        }
        
    except Exception as e:
        logger.warning(f"Failed to get asset statistics: {str(e)}")
        return {"storage": {}, "processing": {}, "total_files": 0, "total_size_mb": 0}


async def _get_pipeline_statistics() -> Dict[str, Any]:
    """Get pipeline statistics"""
    try:
        # Get active pipelines
        active_pipelines = await pipeline_orchestrator.get_active_pipelines()
        
        # Get pipeline health
        health = await pipeline_orchestrator.health_check()
        
        return {
            "active_count": len(active_pipelines),
            "health_status": health.get('status', 'unknown'),
            "components_healthy": sum(1 for status in health.get('components', {}).values() if status == 'healthy'),
            "total_components": len(health.get('components', {})),
            "active_pipelines": [
                {
                    "trend_id": trend_id,
                    "stage": result.stage,
                    "total_time": result.total_time
                }
                for trend_id, result in active_pipelines.items()
            ]
        }
        
    except Exception as e:
        logger.warning(f"Failed to get pipeline statistics: {str(e)}")
        return {"active_count": 0, "health_status": "unknown", "components_healthy": 0, "total_components": 0}


async def _get_system_statistics() -> Dict[str, Any]:
    """Get system statistics"""
    try:
        # Get deployment service health
        deployment_health = await deployment_service.health_check()
        
        # Get git repository status
        git_status = await deployment_service.git_manager.check_repository_status()
        
        return {
            "deployment_service": {
                "status": deployment_health.get('status', 'unknown'),
                "git_accessible": git_status.get('accessible', False),
                "latest_commit": git_status.get('latest_commit', '')[:8] if git_status.get('latest_commit') else None
            },
            "uptime_hours": 0,  # Would be calculated from service start time
            "version": "1.0.0",  # Would come from application metadata
            "environment": "production"  # Would come from configuration
        }
        
    except Exception as e:
        logger.warning(f"Failed to get system statistics: {str(e)}")
        return {"deployment_service": {"status": "unknown"}, "uptime_hours": 0}

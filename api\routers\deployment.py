"""
API endpoints for content deployment and git operations
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from pydantic import BaseModel
from datetime import datetime

from generator.deployment_service import deployment_service, DeploymentResult
from generator.git_ops import git_content_manager
from generator.webhook_handler import webhook_handler
from generator.models import TemplateType
from database.models.trend_model import TrendRepository, TrendEntity
from shared.auth import get_current_user, require_permissions
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger

logger = get_logger('api.deployment')
router = APIRouter(prefix="/deployment", tags=["deployment"])


class DeployTrendRequest(BaseModel):
    """Request model for trend deployment"""
    trend_id: str
    template_type: TemplateType = TemplateType.ARTICLE
    force_regenerate: bool = False
    custom_commit_message: Optional[str] = None


class BatchDeployRequest(BaseModel):
    """Request model for batch deployment"""
    trend_ids: List[str]
    template_type: TemplateType = TemplateType.ARTICLE
    max_concurrent: int = 3
    force_regenerate: bool = False


class DeploymentStatusResponse(BaseModel):
    """Response model for deployment status"""
    success: bool
    trend_id: str
    commit_hash: Optional[str] = None
    files_deployed: List[str] = []
    deployment_url: Optional[str] = None
    total_time: float = 0.0
    error_message: Optional[str] = None


class GitStatusResponse(BaseModel):
    """Response model for git repository status"""
    accessible: bool
    url: str
    branch: str
    latest_commit: Optional[str] = None
    latest_commit_date: Optional[str] = None
    content_directory: str
    assets_directory: str
    error: Optional[str] = None


@router.post("/deploy-trend", response_model=DeploymentStatusResponse)
async def deploy_trend(
    request: DeployTrendRequest,
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:deploy"]))
):
    """
    Deploy content for a specific trend
    
    Requires: content:deploy permission
    """
    try:
        logger.info(
            f"Deploying trend content",
            trend_id=request.trend_id,
            user_id=current_user.get('id'),
            template_type=request.template_type
        )
        
        # Get trend from database
        trend_repo = TrendRepository()
        trend = await trend_repo.get_by_id(request.trend_id)
        
        if not trend:
            raise HTTPException(status_code=404, detail="Trend not found")
        
        # Deploy content
        result = await deployment_service.deploy_trend_content(
            trend=trend,
            template_type=request.template_type,
            force_regenerate=request.force_regenerate,
            custom_commit_message=request.custom_commit_message
        )
        
        return DeploymentStatusResponse(
            success=result.success,
            trend_id=result.trend_id,
            commit_hash=result.commit_hash,
            files_deployed=result.files_deployed,
            deployment_url=result.deployment_url,
            total_time=result.total_time,
            error_message=result.error_message
        )
        
    except TrendPlatformException as e:
        logger.error(f"Deployment failed: {str(e)}", trend_id=request.trend_id)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected deployment error: {str(e)}", trend_id=request.trend_id)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/deploy-batch")
async def deploy_batch(
    request: BatchDeployRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:deploy"]))
):
    """
    Deploy content for multiple trends (background task)
    
    Requires: content:deploy permission
    """
    try:
        logger.info(
            f"Starting batch deployment",
            trend_count=len(request.trend_ids),
            user_id=current_user.get('id')
        )
        
        # Get trends from database
        trend_repo = TrendRepository()
        trends = []
        
        for trend_id in request.trend_ids:
            trend = await trend_repo.get_by_id(trend_id)
            if trend:
                trends.append(trend)
            else:
                logger.warning(f"Trend not found: {trend_id}")
        
        if not trends:
            raise HTTPException(status_code=400, detail="No valid trends found")
        
        # Start batch deployment in background
        background_tasks.add_task(
            _execute_batch_deployment,
            trends,
            request.template_type,
            request.max_concurrent,
            request.force_regenerate,
            current_user.get('id')
        )
        
        return {
            "message": f"Batch deployment started for {len(trends)} trends",
            "trend_ids": [trend.id for trend in trends],
            "status": "processing"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch deployment initiation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _execute_batch_deployment(
    trends: List[TrendEntity],
    template_type: TemplateType,
    max_concurrent: int,
    force_regenerate: bool,
    user_id: str
):
    """Execute batch deployment in background"""
    try:
        logger.info(f"Executing batch deployment for {len(trends)} trends", user_id=user_id)
        
        result = await deployment_service.deploy_multiple_trends(
            trends=trends,
            template_type=template_type,
            max_concurrent=max_concurrent,
            force_regenerate=force_regenerate
        )
        
        logger.info(
            f"Batch deployment completed",
            user_id=user_id,
            total_trends=result['total_trends'],
            successful=result['successful_deployments'],
            failed=result['failed_deployments'],
            total_time=result['total_time']
        )
        
    except Exception as e:
        logger.error(f"Batch deployment execution failed: {str(e)}", user_id=user_id)


@router.get("/status", response_model=GitStatusResponse)
async def get_git_status(
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:read"]))
):
    """
    Get git repository status
    
    Requires: content:read permission
    """
    try:
        status = await git_content_manager.check_repository_status()
        
        return GitStatusResponse(
            accessible=status.get('accessible', False),
            url=status.get('url', ''),
            branch=status.get('branch', ''),
            latest_commit=status.get('latest_commit'),
            latest_commit_date=status.get('latest_commit_date'),
            content_directory=status.get('content_directory', ''),
            assets_directory=status.get('assets_directory', ''),
            error=status.get('error')
        )
        
    except Exception as e:
        logger.error(f"Git status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to check git status")


@router.get("/health")
async def deployment_health_check():
    """
    Health check for deployment service
    
    Public endpoint for monitoring
    """
    try:
        health = await deployment_service.health_check()
        
        status_code = 200 if health.get('status') == 'healthy' else 503
        
        return {
            "status": health.get('status', 'unknown'),
            "timestamp": health.get('timestamp'),
            "components": {
                "content_pipeline": health.get('content_pipeline', {}).get('status'),
                "git_repository": health.get('git_repository', {}).get('accessible'),
                "template_engine": health.get('template_engine', {}).get('status')
            }
        }
        
    except Exception as e:
        logger.error(f"Deployment health check failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.post("/webhook/github")
async def github_webhook(request: Request):
    """
    Handle GitHub webhook
    
    Public endpoint for GitHub webhook delivery
    """
    try:
        result = await webhook_handler.handle_webhook(request, provider="github")
        
        logger.info(
            f"GitHub webhook processed",
            success=result.get('success'),
            event_type=result.get('event_type'),
            repository=result.get('repository')
        )
        
        return result
        
    except Exception as e:
        logger.error(f"GitHub webhook processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")


@router.post("/webhook/gitlab")
async def gitlab_webhook(request: Request):
    """
    Handle GitLab webhook
    
    Public endpoint for GitLab webhook delivery
    """
    try:
        result = await webhook_handler.handle_webhook(request, provider="gitlab")
        
        logger.info(
            f"GitLab webhook processed",
            success=result.get('success'),
            event_type=result.get('event_type'),
            repository=result.get('repository')
        )
        
        return result
        
    except Exception as e:
        logger.error(f"GitLab webhook processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")


@router.post("/webhook/generic")
async def generic_webhook(request: Request):
    """
    Handle generic webhook
    
    Public endpoint for generic webhook delivery
    """
    try:
        result = await webhook_handler.handle_webhook(request, provider="generic")
        
        logger.info(
            f"Generic webhook processed",
            success=result.get('success'),
            event_type=result.get('event_type'),
            repository=result.get('repository')
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Generic webhook processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")


@router.get("/deployments/recent")
async def get_recent_deployments(
    limit: int = 10,
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:read"]))
):
    """
    Get recent deployment history
    
    Requires: content:read permission
    """
    try:
        # This would typically query a deployment history table
        # For now, return a placeholder response
        return {
            "deployments": [],
            "total": 0,
            "limit": limit,
            "message": "Deployment history tracking not yet implemented"
        }
        
    except Exception as e:
        logger.error(f"Failed to get deployment history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get deployment history")

"""
Deployments API router - handles deployment management endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from security.auth import get_current_user, require_editor, require_admin, User
from database.models.deployment_model import (
    DeploymentRepository, DeploymentEntity, DeploymentStatus,
    DeploymentCreateRequest, DeploymentUpdateRequest
)
from database.models.content_model import ContentRepository
from monitoring.logger import get_logger
from shared.exceptions import DatabaseError, ValidationError

logger = get_logger('api.deployments')
router = APIRouter()
deployment_repo = DeploymentRepository()
content_repo = ContentRepository()


class DeploymentResponse(BaseModel):
    """Response model for deployment data"""
    id: str
    trend_id: str
    content_id: Optional[str]
    status: DeploymentStatus
    deploy_url: Optional[str]
    build_logs: Optional[str]
    error_message: Optional[str]
    deployment_metadata: Dict[str, Any]
    started_at: Optional[str]
    completed_at: Optional[str]
    created_at: str
    updated_at: str


class DeploymentListResponse(BaseModel):
    """Response model for deployment list with pagination"""
    data: List[DeploymentResponse]
    pagination: Dict[str, Any]


class DeploymentStatsResponse(BaseModel):
    """Response model for deployment statistics"""
    total_deployments: int
    pending_deployments: int
    building_deployments: int
    successful_deployments: int
    failed_deployments: int
    avg_build_time: Optional[float]
    success_rate: Optional[float]


class DeploymentTriggerRequest(BaseModel):
    """Request model for triggering deployment"""
    content_id: Optional[str] = None
    trend_id: Optional[str] = None
    force_rebuild: bool = False


@router.get("/", response_model=DeploymentListResponse)
async def list_deployments(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[DeploymentStatus] = None,
    trend_id: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """List deployments with filtering and pagination"""
    try:
        filters = {}
        if status:
            filters['status'] = status.value
        if trend_id:
            filters['trend_id'] = trend_id
        
        result = await deployment_repo.list_with_pagination(
            page=page,
            page_size=page_size,
            filters=filters
        )
        
        # Convert entities to response models
        result['data'] = [DeploymentResponse(**deployment.dict()) for deployment in result['data']]
        
        logger.info(
            f"Listed deployments",
            user_id=current_user.id,
            page=page,
            page_size=page_size,
            filters=filters,
            total_count=result['pagination']['total_count']
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to list deployments: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployments"
        )


@router.get("/active", response_model=List[DeploymentResponse])
async def get_active_deployments(
    current_user: User = Depends(get_current_user)
):
    """Get currently active deployments"""
    try:
        deployments = await deployment_repo.get_active_deployments()
        
        logger.info(
            f"Retrieved {len(deployments)} active deployments",
            user_id=current_user.id,
            count=len(deployments)
        )
        
        return [DeploymentResponse(**deployment.dict()) for deployment in deployments]
        
    except Exception as e:
        logger.error(f"Failed to get active deployments: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve active deployments"
        )


@router.get("/stats", response_model=DeploymentStatsResponse)
async def get_deployment_statistics(
    current_user: User = Depends(get_current_user)
):
    """Get deployment statistics"""
    try:
        stats = await deployment_repo.get_statistics()
        
        logger.info("Retrieved deployment statistics", user_id=current_user.id)
        
        return DeploymentStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Failed to get deployment statistics: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve statistics"
        )


@router.get("/{deployment_id}", response_model=DeploymentResponse)
async def get_deployment(
    deployment_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get specific deployment by ID"""
    try:
        deployment = await deployment_repo.get_by_id(deployment_id)
        
        if not deployment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deployment not found"
            )
        
        logger.info(f"Retrieved deployment", user_id=current_user.id, deployment_id=deployment_id)
        
        return DeploymentResponse(**deployment.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get deployment: {str(e)}", user_id=current_user.id, deployment_id=deployment_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployment"
        )


@router.post("/trigger", response_model=DeploymentResponse)
async def trigger_deployment(
    request: DeploymentTriggerRequest,
    current_user: User = Depends(require_editor)
):
    """Trigger a new deployment"""
    try:
        if not request.content_id and not request.trend_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either content_id or trend_id must be provided"
            )
        
        # If trend_id provided, get the latest content for that trend
        if request.trend_id and not request.content_id:
            content_list = await content_repo.get_ready_for_deployment(limit=1)
            trend_content = [c for c in content_list if c.get('trend_id') == request.trend_id]
            if not trend_content:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No content ready for deployment for this trend"
                )
            request.content_id = trend_content[0]['id']
        
        # Verify content exists
        if request.content_id:
            content = await content_repo.get_by_id(request.content_id)
            if not content:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Content not found"
                )
            trend_id = content.trend_id
        else:
            trend_id = request.trend_id
        
        # Create deployment record
        deployment_data = {
            'trend_id': trend_id,
            'content_id': request.content_id,
            'status': DeploymentStatus.PENDING,
            'deployment_metadata': {
                'force_rebuild': request.force_rebuild,
                'triggered_by': current_user.id
            }
        }
        
        deployment = await deployment_repo.create(deployment_data)
        
        # Trigger deployment task
        from deploy.tasks import deploy_content_task
        task = deploy_content_task.delay(deployment.id)
        
        logger.info(
            f"Triggered deployment",
            user_id=current_user.id,
            deployment_id=deployment.id,
            content_id=request.content_id,
            trend_id=trend_id,
            task_id=task.id
        )
        
        return DeploymentResponse(**deployment.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to trigger deployment: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger deployment"
        )


@router.post("/batch-deploy")
async def batch_deploy_ready_content(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(require_editor)
):
    """Deploy all content ready for deployment"""
    try:
        # Trigger batch deployment task
        from deploy.tasks import deploy_ready_content
        task = deploy_ready_content.delay(limit)
        
        logger.info(
            f"Triggered batch deployment",
            user_id=current_user.id,
            limit=limit,
            task_id=task.id
        )
        
        return {"message": "Batch deployment started", "task_id": task.id, "limit": limit}
        
    except Exception as e:
        logger.error(f"Failed to trigger batch deployment: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger batch deployment"
        )


@router.post("/{deployment_id}/cancel")
async def cancel_deployment(
    deployment_id: str,
    current_user: User = Depends(require_editor)
):
    """Cancel a pending or building deployment"""
    try:
        deployment = await deployment_repo.get_by_id(deployment_id)
        
        if not deployment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deployment not found"
            )
        
        if deployment.status not in [DeploymentStatus.PENDING, DeploymentStatus.BUILDING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only pending or building deployments can be cancelled"
            )
        
        # Update deployment status
        updated_deployment = await deployment_repo.update(deployment_id, {
            'status': DeploymentStatus.CANCELLED,
            'error_message': f'Cancelled by user {current_user.id}'
        })
        
        logger.info(
            f"Cancelled deployment",
            user_id=current_user.id,
            deployment_id=deployment_id
        )
        
        return {"message": "Deployment cancelled successfully", "deployment_id": deployment_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel deployment: {str(e)}", user_id=current_user.id, deployment_id=deployment_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel deployment"
        )


@router.get("/{deployment_id}/logs")
async def get_deployment_logs(
    deployment_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get deployment logs"""
    try:
        deployment = await deployment_repo.get_by_id(deployment_id)
        
        if not deployment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deployment not found"
            )
        
        logger.info(f"Retrieved deployment logs", user_id=current_user.id, deployment_id=deployment_id)
        
        return {
            "deployment_id": deployment_id,
            "status": deployment.status,
            "build_logs": deployment.build_logs or "No logs available",
            "error_message": deployment.error_message
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get deployment logs: {str(e)}", user_id=current_user.id, deployment_id=deployment_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployment logs"
        )

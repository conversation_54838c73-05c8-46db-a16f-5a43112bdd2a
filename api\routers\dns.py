"""
DNS API router - handles DNS management endpoints
"""
from fastapi import APIRout<PERSON>, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from security.auth import get_current_user, require_editor, require_admin, User
from database.models.dns_model import (
    DNSRepository, DNSEntity, DNSStatus,
    DNSCreateRequest, DNSUpdateRequest
)
from database.models.trend_model import TrendRepository
from dns.dns_manager import DNSManager
from monitoring.logger import get_logger
from shared.exceptions import DatabaseError, ValidationError

logger = get_logger('api.dns')
router = APIRouter()
dns_repo = DNSRepository()
trend_repo = TrendRepository()
dns_manager = DNSManager()


class DNSResponse(BaseModel):
    """Response model for DNS record data"""
    id: str
    trend_id: str
    subdomain: str
    record_type: str
    record_value: str
    status: DNSStatus
    cloudflare_record_id: Optional[str]
    ttl: int
    proxied: bool
    dns_metadata: Dict[str, Any]
    created_at: str
    updated_at: str


class DNSListResponse(BaseModel):
    """Response model for DNS list with pagination"""
    data: List[DNSResponse]
    pagination: Dict[str, Any]


class DNSStatsResponse(BaseModel):
    """Response model for DNS statistics"""
    total_records: int
    active_records: int
    pending_records: int
    failed_records: int
    deleted_records: int


class DNSCreateRequest(BaseModel):
    """Request model for creating DNS record"""
    trend_id: str
    subdomain: Optional[str] = None  # Auto-generated if not provided
    record_type: str = "CNAME"
    record_value: str
    ttl: int = 3600
    proxied: bool = True


class DNSResolveRequest(BaseModel):
    """Request model for resolving DNS"""
    slug: str


@router.get("/", response_model=DNSListResponse)
async def list_dns_records(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[DNSStatus] = None,
    trend_id: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """List DNS records with filtering and pagination"""
    try:
        filters = {}
        if status:
            filters['status'] = status.value
        if trend_id:
            filters['trend_id'] = trend_id
        
        result = await dns_repo.list_with_pagination(
            page=page,
            page_size=page_size,
            filters=filters
        )
        
        # Convert entities to response models
        result['data'] = [DNSResponse(**record.dict()) for record in result['data']]
        
        logger.info(
            f"Listed DNS records",
            user_id=current_user.id,
            page=page,
            page_size=page_size,
            filters=filters,
            total_count=result['pagination']['total_count']
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to list DNS records: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve DNS records"
        )


@router.get("/active", response_model=List[DNSResponse])
async def get_active_dns_records(
    current_user: User = Depends(get_current_user)
):
    """Get active DNS records"""
    try:
        records = await dns_repo.get_active_records()
        
        logger.info(
            f"Retrieved {len(records)} active DNS records",
            user_id=current_user.id,
            count=len(records)
        )
        
        return [DNSResponse(**record.dict()) for record in records]
        
    except Exception as e:
        logger.error(f"Failed to get active DNS records: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve active DNS records"
        )


@router.get("/stats", response_model=DNSStatsResponse)
async def get_dns_statistics(
    current_user: User = Depends(get_current_user)
):
    """Get DNS statistics"""
    try:
        stats = await dns_repo.get_statistics()
        
        logger.info("Retrieved DNS statistics", user_id=current_user.id)
        
        return DNSStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Failed to get DNS statistics: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve statistics"
        )


@router.get("/{record_id}", response_model=DNSResponse)
async def get_dns_record(
    record_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get specific DNS record by ID"""
    try:
        record = await dns_repo.get_by_id(record_id)
        
        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="DNS record not found"
            )
        
        logger.info(f"Retrieved DNS record", user_id=current_user.id, record_id=record_id)
        
        return DNSResponse(**record.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get DNS record: {str(e)}", user_id=current_user.id, record_id=record_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve DNS record"
        )


@router.post("/create", response_model=DNSResponse)
async def create_dns_record(
    request: DNSCreateRequest,
    current_user: User = Depends(require_editor)
):
    """Create a new DNS record"""
    try:
        # Verify trend exists
        trend = await trend_repo.get_by_id(request.trend_id)
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        # Generate subdomain if not provided
        subdomain = request.subdomain or f"{trend.slug}.trends.yourdomain.com"
        
        # Create DNS record via DNS manager
        dns_result = await dns_manager.create_trend_dns(
            trend_id=request.trend_id,
            subdomain=subdomain,
            target_url=request.record_value,
            ttl=request.ttl,
            proxied=request.proxied
        )
        
        if not dns_result['success']:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create DNS record: {dns_result.get('error', 'Unknown error')}"
            )
        
        # Create database record
        dns_data = {
            'trend_id': request.trend_id,
            'subdomain': subdomain,
            'record_type': request.record_type,
            'record_value': request.record_value,
            'status': DNSStatus.ACTIVE,
            'cloudflare_record_id': dns_result.get('record_id'),
            'ttl': request.ttl,
            'proxied': request.proxied,
            'dns_metadata': dns_result.get('metadata', {})
        }
        
        record = await dns_repo.create(dns_data)
        
        logger.info(
            f"Created DNS record: {subdomain}",
            user_id=current_user.id,
            record_id=record.id,
            trend_id=request.trend_id,
            subdomain=subdomain
        )
        
        return DNSResponse(**record.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create DNS record: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create DNS record"
        )


@router.post("/resolve/{slug}")
async def resolve_dns(
    slug: str,
    current_user: User = Depends(get_current_user)
):
    """Resolve DNS for a given slug"""
    try:
        # Get trend by slug
        trend = await trend_repo.get_by_slug(slug)
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        # Get DNS record for trend
        dns_record = await dns_repo.get_by_trend_id(trend.id)
        if not dns_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="DNS record not found for this trend"
            )
        
        logger.info(
            f"Resolved DNS for slug: {slug}",
            user_id=current_user.id,
            trend_id=trend.id,
            subdomain=dns_record.subdomain
        )
        
        return {
            "slug": slug,
            "trend_id": trend.id,
            "subdomain": dns_record.subdomain,
            "record_value": dns_record.record_value,
            "status": dns_record.status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to resolve DNS for slug: {slug}", user_id=current_user.id, slug=slug)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resolve DNS"
        )


@router.delete("/{record_id}")
async def delete_dns_record(
    record_id: str,
    current_user: User = Depends(require_editor)
):
    """Delete a DNS record"""
    try:
        # Verify record exists
        record = await dns_repo.get_by_id(record_id)
        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="DNS record not found"
            )
        
        # Delete from Cloudflare if record ID exists
        if record.cloudflare_record_id:
            delete_result = await dns_manager.delete_dns_record(record.cloudflare_record_id)
            if not delete_result['success']:
                logger.warning(
                    f"Failed to delete DNS record from Cloudflare: {delete_result.get('error')}",
                    record_id=record_id,
                    cloudflare_record_id=record.cloudflare_record_id
                )
        
        # Update database record status
        await dns_repo.update(record_id, {
            'status': DNSStatus.DELETED
        })
        
        logger.info(
            f"Deleted DNS record: {record.subdomain}",
            user_id=current_user.id,
            record_id=record_id
        )
        
        return {"message": "DNS record deleted successfully", "record_id": record_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete DNS record: {str(e)}", user_id=current_user.id, record_id=record_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete DNS record"
        )


@router.post("/sync")
async def sync_dns_records(
    current_user: User = Depends(require_admin)
):
    """Sync DNS records with Cloudflare"""
    try:
        # Trigger DNS sync task
        from dns.tasks import sync_dns_records
        task = sync_dns_records.delay()
        
        logger.info(
            f"Triggered DNS sync",
            user_id=current_user.id,
            task_id=task.id
        )
        
        return {"message": "DNS sync started", "task_id": task.id}
        
    except Exception as e:
        logger.error(f"Failed to trigger DNS sync: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger DNS sync"
        )


@router.post("/purge-cache")
async def purge_dns_cache(
    current_user: User = Depends(require_editor)
):
    """Purge DNS cache"""
    try:
        # Trigger cache purge
        purge_result = await dns_manager.purge_cache()
        
        logger.info(
            f"Purged DNS cache",
            user_id=current_user.id,
            success=purge_result.get('success', False)
        )
        
        if purge_result.get('success'):
            return {"message": "DNS cache purged successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to purge DNS cache: {purge_result.get('error', 'Unknown error')}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to purge DNS cache: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to purge DNS cache"
        )

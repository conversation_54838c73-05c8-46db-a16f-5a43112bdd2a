"""
API endpoints for complete content generation pipeline
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel
from datetime import datetime

from generator.pipeline_orchestrator import (
    pipeline_orchestrator, PipelineConfig, PipelineMode, PipelineStage
)
from generator.models import TemplateType
from generator.ai.ai_service_factory import AIProvider
from database.models.trend_model import TrendRepository, TrendEntity
from shared.auth import get_current_user, require_permissions
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger

logger = get_logger('api.pipeline')
router = APIRouter(prefix="/pipeline", tags=["pipeline"])


class PipelineExecutionRequest(BaseModel):
    """Request model for pipeline execution"""
    trend_id: str
    mode: PipelineMode = PipelineMode.FULL_PIPELINE
    ai_provider: Optional[AIProvider] = None
    template_type: TemplateType = TemplateType.ARTICLE
    include_assets: bool = True
    include_code: bool = True
    force_regenerate: bool = False
    auto_deploy: bool = True
    cleanup_assets: bool = True
    timeout_seconds: int = 300


class BatchPipelineRequest(BaseModel):
    """Request model for batch pipeline execution"""
    trend_ids: List[str]
    mode: PipelineMode = PipelineMode.FULL_PIPELINE
    ai_provider: Optional[AIProvider] = None
    template_type: TemplateType = TemplateType.ARTICLE
    include_assets: bool = True
    include_code: bool = True
    force_regenerate: bool = False
    auto_deploy: bool = True
    cleanup_assets: bool = True
    max_concurrent: int = 3
    timeout_seconds: int = 300


class PipelineStatusResponse(BaseModel):
    """Response model for pipeline status"""
    success: bool
    trend_id: str
    stage: PipelineStage
    total_time: float = 0.0
    stage_times: Dict[str, float] = {}
    error_message: Optional[str] = None
    warnings: List[str] = []
    content_generated: bool = False
    assets_processed: bool = False
    deployed: bool = False


class PipelineHealthResponse(BaseModel):
    """Response model for pipeline health"""
    status: str
    components: Dict[str, str]
    active_pipelines: int
    timestamp: str


@router.post("/execute", response_model=PipelineStatusResponse)
async def execute_pipeline(
    request: PipelineExecutionRequest,
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:generate"]))
):
    """
    Execute complete content generation pipeline
    
    Requires: content:generate permission
    """
    try:
        logger.info(
            f"Executing pipeline",
            trend_id=request.trend_id,
            mode=request.mode,
            user_id=current_user.get('id')
        )
        
        # Get trend from database
        trend_repo = TrendRepository()
        trend = await trend_repo.get_by_id(request.trend_id)
        
        if not trend:
            raise HTTPException(status_code=404, detail="Trend not found")
        
        # Create pipeline configuration
        config = PipelineConfig(
            mode=request.mode,
            ai_provider=request.ai_provider,
            template_type=request.template_type,
            include_assets=request.include_assets,
            include_code=request.include_code,
            force_regenerate=request.force_regenerate,
            auto_deploy=request.auto_deploy,
            cleanup_assets=request.cleanup_assets,
            timeout_seconds=request.timeout_seconds
        )
        
        # Execute pipeline
        result = await pipeline_orchestrator.execute_pipeline(trend, config)
        
        # Convert to response format
        response = PipelineStatusResponse(
            success=result.success,
            trend_id=result.trend_id,
            stage=result.stage,
            total_time=result.total_time,
            stage_times=result.stage_times,
            error_message=result.error_message,
            warnings=result.warnings,
            content_generated=bool(result.content_generation_result and result.content_generation_result.success),
            assets_processed=bool(result.content_generation_result and result.content_generation_result.assets),
            deployed=bool(result.deployment_result and result.deployment_result.success)
        )
        
        logger.info(
            f"Pipeline execution completed",
            trend_id=request.trend_id,
            success=result.success,
            stage=result.stage,
            total_time=result.total_time
        )
        
        return response
        
    except TrendPlatformException as e:
        logger.error(f"Pipeline execution failed: {str(e)}", trend_id=request.trend_id)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected pipeline error: {str(e)}", trend_id=request.trend_id)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/execute-batch")
async def execute_batch_pipeline(
    request: BatchPipelineRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:generate"]))
):
    """
    Execute pipeline for multiple trends (background task)
    
    Requires: content:generate permission
    """
    try:
        logger.info(
            f"Starting batch pipeline execution",
            trend_count=len(request.trend_ids),
            mode=request.mode,
            user_id=current_user.get('id')
        )
        
        # Get trends from database
        trend_repo = TrendRepository()
        trends = []
        
        for trend_id in request.trend_ids:
            trend = await trend_repo.get_by_id(trend_id)
            if trend:
                trends.append(trend)
            else:
                logger.warning(f"Trend not found: {trend_id}")
        
        if not trends:
            raise HTTPException(status_code=400, detail="No valid trends found")
        
        # Create pipeline configuration
        config = PipelineConfig(
            mode=request.mode,
            ai_provider=request.ai_provider,
            template_type=request.template_type,
            include_assets=request.include_assets,
            include_code=request.include_code,
            force_regenerate=request.force_regenerate,
            auto_deploy=request.auto_deploy,
            cleanup_assets=request.cleanup_assets,
            max_concurrent=request.max_concurrent,
            timeout_seconds=request.timeout_seconds
        )
        
        # Start batch execution in background
        background_tasks.add_task(
            _execute_batch_pipeline_background,
            trends,
            config,
            current_user.get('id')
        )
        
        return {
            "message": f"Batch pipeline started for {len(trends)} trends",
            "trend_ids": [trend.id for trend in trends],
            "mode": request.mode,
            "status": "processing"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch pipeline initiation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _execute_batch_pipeline_background(
    trends: List[TrendEntity],
    config: PipelineConfig,
    user_id: str
):
    """Execute batch pipeline in background"""
    try:
        logger.info(f"Executing batch pipeline for {len(trends)} trends", user_id=user_id)
        
        result = await pipeline_orchestrator.execute_batch_pipeline(trends, config)
        
        logger.info(
            f"Batch pipeline completed",
            user_id=user_id,
            total_trends=result['total_trends'],
            successful=result['successful'],
            failed=result['failed'],
            total_time=result['total_time']
        )
        
    except Exception as e:
        logger.error(f"Batch pipeline execution failed: {str(e)}", user_id=user_id)


@router.get("/status/{trend_id}", response_model=Optional[PipelineStatusResponse])
async def get_pipeline_status(
    trend_id: str,
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:read"]))
):
    """
    Get status of active pipeline
    
    Requires: content:read permission
    """
    try:
        result = await pipeline_orchestrator.get_pipeline_status(trend_id)
        
        if not result:
            return None
        
        return PipelineStatusResponse(
            success=result.success,
            trend_id=result.trend_id,
            stage=result.stage,
            total_time=result.total_time,
            stage_times=result.stage_times,
            error_message=result.error_message,
            warnings=result.warnings,
            content_generated=bool(result.content_generation_result and result.content_generation_result.success),
            assets_processed=bool(result.content_generation_result and result.content_generation_result.assets),
            deployed=bool(result.deployment_result and result.deployment_result.success)
        )
        
    except Exception as e:
        logger.error(f"Failed to get pipeline status: {str(e)}", trend_id=trend_id)
        raise HTTPException(status_code=500, detail="Failed to get pipeline status")


@router.get("/active")
async def get_active_pipelines(
    current_user: dict = Depends(get_current_user),
    permissions: None = Depends(require_permissions(["content:read"]))
):
    """
    Get all active pipelines
    
    Requires: content:read permission
    """
    try:
        active_pipelines = await pipeline_orchestrator.get_active_pipelines()
        
        return {
            "active_count": len(active_pipelines),
            "pipelines": [
                {
                    "trend_id": trend_id,
                    "stage": result.stage,
                    "success": result.success,
                    "total_time": result.total_time,
                    "error_message": result.error_message
                }
                for trend_id, result in active_pipelines.items()
            ],
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get active pipelines: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get active pipelines")


@router.get("/health", response_model=PipelineHealthResponse)
async def pipeline_health_check():
    """
    Health check for pipeline orchestrator
    
    Public endpoint for monitoring
    """
    try:
        health = await pipeline_orchestrator.health_check()
        
        return PipelineHealthResponse(
            status=health.get('status', 'unknown'),
            components=health.get('components', {}),
            active_pipelines=health.get('active_pipelines', 0),
            timestamp=health.get('timestamp', datetime.utcnow().isoformat())
        )
        
    except Exception as e:
        logger.error(f"Pipeline health check failed: {str(e)}")
        return PipelineHealthResponse(
            status="error",
            components={"error": str(e)},
            active_pipelines=0,
            timestamp=datetime.utcnow().isoformat()
        )


@router.get("/modes")
async def get_pipeline_modes():
    """
    Get available pipeline modes and configurations
    
    Public endpoint for documentation
    """
    return {
        "modes": [
            {
                "value": mode.value,
                "description": _get_mode_description(mode)
            }
            for mode in PipelineMode
        ],
        "template_types": [
            {
                "value": template.value,
                "description": _get_template_description(template)
            }
            for template in TemplateType
        ],
        "ai_providers": [
            {
                "value": provider.value,
                "description": _get_provider_description(provider)
            }
            for provider in AIProvider
        ]
    }


def _get_mode_description(mode: PipelineMode) -> str:
    """Get description for pipeline mode"""
    descriptions = {
        PipelineMode.GENERATE_ONLY: "Generate content without deployment",
        PipelineMode.DEPLOY_ONLY: "Deploy existing content without regeneration",
        PipelineMode.FULL_PIPELINE: "Complete end-to-end workflow",
        PipelineMode.BATCH_PROCESSING: "Process multiple trends concurrently"
    }
    return descriptions.get(mode, "Unknown mode")


def _get_template_description(template: TemplateType) -> str:
    """Get description for template type"""
    descriptions = {
        TemplateType.ARTICLE: "Standard article template with full content structure",
        TemplateType.LANDING: "Landing page template for trend showcases"
    }
    return descriptions.get(template, "Unknown template")


def _get_provider_description(provider: AIProvider) -> str:
    """Get description for AI provider"""
    descriptions = {
        AIProvider.OPENAI: "OpenAI GPT models (GPT-3.5, GPT-4, DALL-E)",
        AIProvider.ANTHROPIC: "Anthropic Claude models",
        AIProvider.AZURE_OPENAI: "Azure OpenAI service",
        AIProvider.LOCAL: "Local AI models",
        AIProvider.CUSTOM: "Custom AI provider"
    }
    return descriptions.get(provider, "Unknown provider")

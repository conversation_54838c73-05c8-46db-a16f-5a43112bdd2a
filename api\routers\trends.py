"""
Trends API router - handles trend-related endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from security.auth import get_current_user, require_editor, require_admin, User
from database.models.trend_model import (
    TrendRepository, TrendEntity, TrendStatus, 
    TrendCreateRequest, TrendUpdateRequest
)
from scraper.tasks import scrape_trends, scrape_trends_for_region
from monitoring.logger import get_logger
from shared.exceptions import DatabaseError, ValidationError

logger = get_logger('api.trends')
router = APIRouter()
trend_repo = TrendRepository()


class TrendResponse(BaseModel):
    """Response model for trend data"""
    id: str
    keyword: str
    slug: str
    status: TrendStatus
    region: str
    category: str
    search_volume: Optional[int]
    growth_rate: Optional[float]
    score: Optional[float]
    source: str
    created_at: str
    deployed_at: Optional[str]
    expire_at: Optional[str]


class TrendListResponse(BaseModel):
    """Response model for trend list with pagination"""
    data: List[TrendResponse]
    pagination: Dict[str, Any]


class TrendStatsResponse(BaseModel):
    """Response model for trend statistics"""
    total_trends: int
    pending_trends: int
    approved_trends: int
    live_trends: int
    expired_trends: int
    rejected_trends: int
    avg_score: Optional[float]


@router.get("/", response_model=TrendListResponse)
async def list_trends(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[TrendStatus] = None,
    region: Optional[str] = None,
    category: Optional[str] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """List trends with filtering and pagination"""
    try:
        filters = {}
        if status:
            filters['status'] = status.value
        if region:
            filters['region'] = region
        if category:
            filters['category'] = category
        
        if search:
            # Use search functionality
            trends = await trend_repo.search(search, ['keyword', 'category'], limit=page_size)
            # Convert to pagination format
            result = {
                'data': [TrendResponse(**trend.dict()) for trend in trends],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': len(trends),
                    'total_pages': 1,
                    'has_next': False,
                    'has_prev': False
                }
            }
        else:
            result = await trend_repo.list_with_pagination(
                page=page,
                page_size=page_size,
                filters=filters
            )
            
            # Convert entities to response models
            result['data'] = [TrendResponse(**trend.dict()) for trend in result['data']]
        
        logger.info(
            f"Listed trends",
            user_id=current_user.id,
            page=page,
            page_size=page_size,
            filters=filters,
            total_count=result['pagination']['total_count']
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to list trends: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve trends"
        )


@router.get("/pending", response_model=List[TrendResponse])
async def get_pending_trends(
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(require_editor)
):
    """Get pending trends for approval"""
    try:
        trends = await trend_repo.get_pending_trends(limit=limit)
        
        logger.info(
            f"Retrieved {len(trends)} pending trends",
            user_id=current_user.id,
            count=len(trends)
        )
        
        return [TrendResponse(**trend.dict()) for trend in trends]
        
    except Exception as e:
        logger.error(f"Failed to get pending trends: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve pending trends"
        )


@router.get("/live", response_model=List[TrendResponse])
async def get_live_trends(
    region: Optional[str] = None,
    category: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get currently live trends"""
    try:
        trends = await trend_repo.get_live_trends(region=region, category=category)
        
        logger.info(
            f"Retrieved {len(trends)} live trends",
            user_id=current_user.id,
            region=region,
            category=category,
            count=len(trends)
        )
        
        return [TrendResponse(**trend.dict()) for trend in trends]
        
    except Exception as e:
        logger.error(f"Failed to get live trends: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve live trends"
        )


@router.get("/stats", response_model=TrendStatsResponse)
async def get_trend_statistics(
    current_user: User = Depends(get_current_user)
):
    """Get trend statistics"""
    try:
        stats = await trend_repo.get_statistics()
        
        logger.info("Retrieved trend statistics", user_id=current_user.id)
        
        return TrendStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Failed to get trend statistics: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve statistics"
        )


@router.get("/{trend_id}", response_model=TrendResponse)
async def get_trend(
    trend_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get specific trend by ID"""
    try:
        trend = await trend_repo.get_by_id(trend_id)
        
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        logger.info(f"Retrieved trend: {trend.keyword}", user_id=current_user.id, trend_id=trend_id)
        
        return TrendResponse(**trend.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get trend: {str(e)}", user_id=current_user.id, trend_id=trend_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve trend"
        )


@router.post("/{trend_id}/approve")
async def approve_trend(
    trend_id: str,
    current_user: User = Depends(require_editor)
):
    """Approve a pending trend"""
    try:
        trend = await trend_repo.get_by_id(trend_id)
        
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        if trend.status != TrendStatus.PENDING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only pending trends can be approved"
            )
        
        success = await trend_repo.approve_trend(trend_id, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to approve trend"
            )
        
        logger.info(
            f"Trend approved: {trend.keyword}",
            user_id=current_user.id,
            trend_id=trend_id,
            keyword=trend.keyword
        )
        
        # Trigger content generation
        from generator.tasks import generate_content_for_trend
        generate_content_for_trend.delay(trend_id)
        
        return {"message": "Trend approved successfully", "trend_id": trend_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to approve trend: {str(e)}", user_id=current_user.id, trend_id=trend_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to approve trend"
        )


@router.post("/{trend_id}/reject")
async def reject_trend(
    trend_id: str,
    current_user: User = Depends(require_editor)
):
    """Reject a pending trend"""
    try:
        trend = await trend_repo.get_by_id(trend_id)
        
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        if trend.status != TrendStatus.PENDING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only pending trends can be rejected"
            )
        
        success = await trend_repo.reject_trend(trend_id, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to reject trend"
            )
        
        logger.info(
            f"Trend rejected: {trend.keyword}",
            user_id=current_user.id,
            trend_id=trend_id,
            keyword=trend.keyword
        )
        
        return {"message": "Trend rejected successfully", "trend_id": trend_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reject trend: {str(e)}", user_id=current_user.id, trend_id=trend_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reject trend"
        )


@router.post("/scrape")
async def trigger_scraping(
    region: Optional[str] = None,
    current_user: User = Depends(require_editor)
):
    """Trigger trend scraping"""
    try:
        if region:
            # Scrape specific region
            task = scrape_trends_for_region.delay(region)
            logger.info(f"Triggered scraping for region: {region}", user_id=current_user.id, task_id=task.id)
            return {"message": f"Scraping started for region: {region}", "task_id": task.id}
        else:
            # Scrape all regions
            task = scrape_trends.delay()
            logger.info("Triggered comprehensive scraping", user_id=current_user.id, task_id=task.id)
            return {"message": "Comprehensive scraping started", "task_id": task.id}
        
    except Exception as e:
        logger.error(f"Failed to trigger scraping: {str(e)}", user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger scraping"
        )

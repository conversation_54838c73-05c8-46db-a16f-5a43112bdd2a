{"name": "trendsite-dashboard", "version": "1.0.0", "private": true, "description": "TrendSite Admin Dashboard - Real-time monitoring and control interface", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "next-auth": "^4.24.0", "@next-auth/prisma-adapter": "^1.0.7", "tailwindcss": "^3.3.0", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.290.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "socket.io-client": "^4.7.2", "swr": "^2.2.4", "axios": "^1.5.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-table": "^7.8.0", "@types/react-table": "^7.7.14", "react-virtual": "^2.10.4", "framer-motion": "^10.16.4", "recharts": "^2.8.0", "react-tagcloud": "^2.3.0"}, "devDependencies": {"eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.6", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
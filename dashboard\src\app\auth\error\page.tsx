'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, Home, RefreshCw } from 'lucide-react'

const errorMessages = {
  Configuration: 'There is a problem with the server configuration.',
  AccessDenied: 'You do not have permission to access this application.',
  Verification: 'The verification token has expired or has already been used.',
  Default: 'An error occurred during authentication.',
  CredentialsSignin: 'Invalid credentials provided.',
  SessionRequired: 'You must be signed in to access this page.',
  Callback: 'Error in the OAuth callback.',
  OAuthSignin: 'Error in constructing an authorization URL.',
  OAuthCallback: 'Error in handling the response from an OAuth provider.',
  OAuthCreateAccount: 'Could not create OAuth account.',
  EmailCreateAccount: 'Could not create email account.',
  OAuthAccountNotLinked: 'The account is not linked. To confirm your identity, sign in with the same account you used originally.',
}

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error') as keyof typeof errorMessages
  
  const errorMessage = errorMessages[error] || errorMessages.Default
  
  const getErrorTitle = (error: string) => {
    switch (error) {
      case 'AccessDenied':
        return 'Access Denied'
      case 'Configuration':
        return 'Configuration Error'
      case 'Verification':
        return 'Verification Failed'
      case 'CredentialsSignin':
        return 'Invalid Credentials'
      case 'SessionRequired':
        return 'Authentication Required'
      default:
        return 'Authentication Error'
    }
  }
  
  const getErrorSeverity = (error: string) => {
    switch (error) {
      case 'Configuration':
        return 'destructive'
      case 'AccessDenied':
        return 'destructive'
      default:
        return 'default'
    }
  }
  
  const showRetryButton = !['Configuration', 'AccessDenied'].includes(error)

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center text-red-600">
            {getErrorTitle(error)}
          </CardTitle>
          <CardDescription className="text-center">
            There was a problem with your authentication
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <Alert variant={getErrorSeverity(error) as any}>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {errorMessage}
            </AlertDescription>
          </Alert>
          
          {error === 'AccessDenied' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Need Access?</h4>
              <p className="text-sm text-blue-700">
                If you believe you should have access to this application, please contact your system administrator with the following information:
              </p>
              <ul className="text-sm text-blue-700 mt-2 list-disc list-inside">
                <li>Your email address</li>
                <li>The reason you need access</li>
                <li>Your role or department</li>
              </ul>
            </div>
          )}
          
          {error === 'Configuration' && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-900 mb-2">System Administrator</h4>
              <p className="text-sm text-yellow-700">
                This error indicates a server configuration issue. Please check the authentication settings and environment variables.
              </p>
            </div>
          )}
          
          <div className="flex flex-col space-y-2">
            {showRetryButton && (
              <Button asChild className="w-full">
                <Link href="/auth/login">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Link>
              </Button>
            )}
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/">
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Link>
            </Button>
          </div>
          
          {error && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600">
                <strong>Error Code:</strong> {error}
              </p>
              <p className="text-xs text-gray-600 mt-1">
                <strong>Timestamp:</strong> {new Date().toISOString()}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

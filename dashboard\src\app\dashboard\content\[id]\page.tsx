'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Trash2,
  Copy,
  Download,
  ExternalLink,
  Rocket,
  RefreshCw,
  Edit,
  FileText,
  Calendar,
  User,
  Loader2
} from 'lucide-react'
import { contentApi, deploymentsApi } from '@/lib/api'
import { useAuth } from '@/components/auth/AuthGuard'
import { PERMISSIONS } from '@/lib/auth'
import { formatRelativeTime } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'
import { Content } from '@/types'

const formSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  slug: z.string().min(1, 'Slug is required').max(200, 'Slug must be less than 200 characters'),
  meta_description: z.string().min(1, 'Meta description is required').max(160, 'Meta description must be less than 160 characters'),
  content: z.string().min(1, 'Content is required'),
})

type FormData = z.infer<typeof formSchema>

export default function ContentDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { checkPermission } = useAuth()
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const contentId = params.id as string

  const [isEditing, setIsEditing] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)

  // Queries
  const { data: content, isLoading, error } = useQuery({
    queryKey: ['content', contentId],
    queryFn: async () => {
      const response = await contentApi.getContentItem(contentId)
      return response.data as Content
    },
  })

  const { data: deployments } = useQuery({
    queryKey: ['deployments', 'by-content', contentId],
    queryFn: async () => {
      const response = await deploymentsApi.getDeployments({ content_id: contentId })
      return response.data
    },
    enabled: !!contentId,
  })

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      slug: '',
      meta_description: '',
      content: '',
    },
  })

  // Update form when content loads
  useEffect(() => {
    if (content) {
      form.reset({
        title: content.title,
        slug: content.slug,
        meta_description: content.meta_description,
        content: content.content,
      })
    }
  }, [content, form])

  // Mutations
  const updateMutation = useMutation({
    mutationFn: async (data: FormData) => {
      const response = await contentApi.updateContent(contentId, data)
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['content', contentId] })
      setIsEditing(false)
      toast({ title: 'Success', description: 'Content updated successfully' })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update content',
        variant: 'destructive',
      })
    },
  })

  const approveMutation = useMutation({
    mutationFn: () => contentApi.approveContent(contentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['content', contentId] })
      toast({ title: 'Success', description: 'Content approved successfully' })
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to approve content', variant: 'destructive' })
    }
  })

  const rejectMutation = useMutation({
    mutationFn: (reason?: string) => contentApi.rejectContent(contentId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['content', contentId] })
      toast({ title: 'Success', description: 'Content rejected successfully' })
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to reject content', variant: 'destructive' })
    }
  })

  const deleteMutation = useMutation({
    mutationFn: () => contentApi.deleteContent(contentId),
    onSuccess: () => {
      toast({ title: 'Success', description: 'Content deleted successfully' })
      router.push('/dashboard/content')
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to delete content', variant: 'destructive' })
    }
  })

  const onSubmit = async (data: FormData) => {
    await updateMutation.mutateAsync(data)
  }

  const handleCopyContent = async () => {
    if (content) {
      try {
        await navigator.clipboard.writeText(content.content)
        toast({ title: 'Copied', description: 'Content copied to clipboard' })
      } catch (error) {
        toast({ title: 'Error', description: 'Failed to copy content', variant: 'destructive' })
      }
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      approved: 'success',
      rejected: 'destructive',
      published: 'default'
    }
    return (
      <Badge variant={variants[status as keyof typeof variants] as any}>
        {status}
      </Badge>
    )
  }

  const canWrite = checkPermission(PERMISSIONS.WRITE_CONTENT)
  const canDelete = checkPermission(PERMISSIONS.DELETE_CONTENT)

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-8 w-48 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="space-y-6">
            <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !content) {
    return (
      <div className="text-center py-8">
        <Alert className="max-w-md mx-auto">
          <AlertDescription>
            Failed to load content. The content may not exist or you may not have permission to view it.
          </AlertDescription>
        </Alert>
        <Button 
          className="mt-4" 
          onClick={() => router.push('/dashboard/content')}
        >
          Back to Content
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <div className="flex items-center space-x-2 mb-1">
              <h1 className="text-3xl font-bold">{content.title}</h1>
              {getStatusBadge(content.status)}
            </div>
            <p className="text-muted-foreground">
              {content.trend ? `Based on: ${content.trend.keyword}` : 'Standalone content'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {canWrite && (
            <>
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsEditing(false)
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={form.handleSubmit(onSubmit)}
                    disabled={updateMutation.isPending}
                  >
                    {updateMutation.isPending ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Save Changes
                  </Button>
                </>
              ) : (
                <Button onClick={() => setIsEditing(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </>
          )}

          {content.status === 'draft' && canWrite && (
            <>
              <Button
                size="sm"
                onClick={() => approveMutation.mutate()}
                disabled={approveMutation.isPending}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Approve
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => rejectMutation.mutate()}
                disabled={rejectMutation.isPending}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject
              </Button>
            </>
          )}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleCopyContent}>
                <Copy className="h-4 w-4 mr-2" />
                Copy Content
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push(`/dashboard/deployments/new?content_id=${contentId}`)}
              >
                <Rocket className="h-4 w-4 mr-2" />
                Deploy
              </DropdownMenuItem>
              {content.status === 'published' && (
                <DropdownMenuItem>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Live
                </DropdownMenuItem>
              )}
              {canDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => deleteMutation.mutate()}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="content" className="space-y-6">
            <TabsList>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="seo">SEO</TabsTrigger>
              <TabsTrigger value="deployments">Deployments ({deployments?.length || 0})</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="space-y-6">
              {isEditing ? (
                <Form {...form}>
                  <form className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Edit Content</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <FormField
                          control={form.control}
                          name="title"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Title</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  onChange={(e) => {
                                    field.onChange(e)
                                    // Auto-generate slug from title
                                    const slug = generateSlug(e.target.value)
                                    form.setValue('slug', slug)
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="slug"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Slug</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormDescription>
                                URL-friendly version of the title
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="meta_description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Meta Description</FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  className="min-h-[80px]"
                                  maxLength={160}
                                />
                              </FormControl>
                              <FormDescription>
                                {field.value?.length || 0}/160 characters
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="content"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Content</FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  className="min-h-[400px] font-mono text-sm"
                                />
                              </FormControl>
                              <FormDescription>
                                Markdown and HTML supported
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                  </form>
                </Form>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      Content Preview
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPreviewMode(!previewMode)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        {previewMode ? 'Raw' : 'Preview'}
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {previewMode ? (
                      <div 
                        className="prose max-w-none"
                        dangerouslySetInnerHTML={{ __html: content.content }}
                      />
                    ) : (
                      <pre className="whitespace-pre-wrap text-sm bg-gray-50 dark:bg-gray-900 p-4 rounded-lg overflow-auto max-h-96">
                        {content.content}
                      </pre>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="seo" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>SEO Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium text-sm text-muted-foreground">Title</h3>
                    <p className="font-medium">{content.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {content.title.length}/60 characters (recommended)
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-muted-foreground">Meta Description</h3>
                    <p className="text-sm">{content.meta_description}</p>
                    <p className="text-xs text-muted-foreground">
                      {content.meta_description.length}/160 characters
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-muted-foreground">URL Slug</h3>
                    <p className="text-sm font-mono">{content.slug}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-muted-foreground">Word Count</h3>
                    <p className="text-sm">{content.content.split(' ').length} words</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="deployments" className="space-y-4">
              {deployments && deployments.length > 0 ? (
                deployments.map((deployment: any) => (
                  <Card key={deployment.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium">{deployment.site_name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {deployment.domain}
                          </p>
                          <div className="flex items-center space-x-4 mt-2">
                            <Badge variant="outline">{deployment.status}</Badge>
                            <span className="text-xs text-muted-foreground">
                              {formatRelativeTime(deployment.started_at)}
                            </span>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/dashboard/deployments/${deployment.id}`)}
                        >
                          View
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Rocket className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-muted-foreground">No deployments yet</p>
                    <Button 
                      className="mt-4"
                      onClick={() => router.push(`/dashboard/deployments/new?content_id=${contentId}`)}
                    >
                      Create Deployment
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="analytics">
              <Card>
                <CardContent className="p-8 text-center">
                  <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-muted-foreground">Analytics coming soon</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Details */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Status: {content.status}</span>
              </div>
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Model: {content.ai_model}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Created: {formatRelativeTime(content.created_at)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Updated: {formatRelativeTime(content.updated_at)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Related Trend */}
          {content.trend && (
            <Card>
              <CardHeader>
                <CardTitle>Related Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <h3 className="font-medium">{content.trend.keyword}</h3>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{content.trend.region}</Badge>
                    <Badge variant="outline">{content.trend.category}</Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => router.push(`/dashboard/trends/${content.trend?.id}`)}
                  >
                    View Trend
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={handleCopyContent}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Content
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push(`/dashboard/deployments/new?content_id=${contentId}`)}
              >
                <Rocket className="h-4 w-4 mr-2" />
                Deploy Content
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useSearchParams } from 'next/navigation'
import { useQuery, useMutation } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ArrowLeft, 
  Rocket, 
  Loader2, 
  Globe,
  FileText,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { deploymentsApi, contentApi, dnsApi } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'
import { DeploymentCreateRequest } from '@/types'

const DEPLOYMENT_PLATFORMS = [
  { value: 'vercel', label: 'Vercel', description: 'Fast and reliable hosting' },
  { value: 'netlify', label: 'Netlify', description: 'JAMstack platform' },
  { value: 'github-pages', label: 'GitHub Pages', description: 'Free static hosting' },
  { value: 'aws-s3', label: 'AWS S3', description: 'Amazon S3 static hosting' },
  { value: 'custom', label: 'Custom', description: 'Custom deployment configuration' }
]

const formSchema = z.object({
  content_id: z.string().min(1, 'Please select content to deploy'),
  site_name: z.string().min(1, 'Site name is required').max(100, 'Site name must be less than 100 characters'),
  domain: z.string().min(1, 'Domain is required').regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/, 'Please enter a valid domain'),
  platform: z.string().min(1, 'Please select a deployment platform'),
  subdomain: z.string().optional(),
  custom_config: z.string().optional(),
  auto_ssl: z.boolean().default(true),
  auto_deploy: z.boolean().default(false),
})

type FormData = z.infer<typeof formSchema>

export default function NewDeploymentPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const preselectedContentId = searchParams.get('content_id')
  const preselectedTrendId = searchParams.get('trend_id')

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content_id: preselectedContentId || '',
      site_name: '',
      domain: '',
      platform: 'vercel',
      subdomain: '',
      custom_config: '',
      auto_ssl: true,
      auto_deploy: false,
    },
  })

  // Queries
  const { data: contentList } = useQuery({
    queryKey: ['content', 'approved'],
    queryFn: async () => {
      const params: any = { status: 'approved', limit: 100 }
      if (preselectedTrendId) params.trend_id = preselectedTrendId
      
      const response = await contentApi.getContent(params)
      return response.data
    },
  })

  const { data: selectedContent } = useQuery({
    queryKey: ['content', form.watch('content_id')],
    queryFn: async () => {
      const contentId = form.watch('content_id')
      if (!contentId) return null
      const response = await contentApi.getContentItem(contentId)
      return response.data
    },
    enabled: !!form.watch('content_id'),
  })

  const { data: domains } = useQuery({
    queryKey: ['domains'],
    queryFn: async () => {
      const response = await dnsApi.getDomains()
      return response.data
    },
  })

  // Auto-generate site name from content title
  useEffect(() => {
    if (selectedContent && !form.getValues('site_name')) {
      const siteName = selectedContent.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
        .substring(0, 50)
      
      form.setValue('site_name', siteName)
    }
  }, [selectedContent, form])

  const createMutation = useMutation({
    mutationFn: async (data: FormData) => {
      const deploymentData: DeploymentCreateRequest = {
        content_id: data.content_id,
        site_name: data.site_name,
        domain: data.domain,
        deployment_config: {
          platform: data.platform,
          subdomain: data.subdomain,
          custom_config: data.custom_config ? JSON.parse(data.custom_config) : undefined,
          auto_ssl: data.auto_ssl,
          auto_deploy: data.auto_deploy,
        }
      }
      
      const response = await deploymentsApi.createDeployment(deploymentData)
      return response.data
    },
    onSuccess: (data) => {
      toast({
        title: 'Success',
        description: 'Deployment created successfully',
      })
      router.push(`/dashboard/deployments/${data.id}`)
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create deployment',
        variant: 'destructive',
      })
    },
  })

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    try {
      await createMutation.mutateAsync(data)
    } finally {
      setIsSubmitting(false)
    }
  }

  const generateSubdomain = () => {
    const siteName = form.getValues('site_name')
    if (siteName) {
      const subdomain = siteName.toLowerCase().replace(/[^a-z0-9-]/g, '')
      form.setValue('subdomain', subdomain)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create New Deployment</h1>
          <p className="text-muted-foreground">
            Deploy content to a live website
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Form */}
        <div className="lg:col-span-2">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Content Selection</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="content_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Content to Deploy *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select content" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {contentList?.data?.map((content: any) => (
                              <SelectItem key={content.id} value={content.id}>
                                <div className="flex items-center space-x-2">
                                  <span>{content.title}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {content.status}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose the content you want to deploy
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Site Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="site_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Site Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="my-awesome-site"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          A unique name for your site (used for internal reference)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="domain"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Domain *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select domain" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {domains?.map((domain: any) => (
                              <SelectItem key={domain.name} value={domain.name}>
                                <div className="flex items-center space-x-2">
                                  <Globe className="h-4 w-4" />
                                  <span>{domain.name}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {domain.status}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose the domain where your site will be deployed
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="subdomain"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subdomain</FormLabel>
                        <div className="flex space-x-2">
                          <FormControl>
                            <Input
                              placeholder="www"
                              {...field}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={generateSubdomain}
                          >
                            Auto-generate
                          </Button>
                        </div>
                        <FormDescription>
                          Optional subdomain (e.g., 'www' for www.yourdomain.com)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Deployment Platform</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="platform"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Platform *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {DEPLOYMENT_PLATFORMS.map((platform) => (
                              <SelectItem key={platform.value} value={platform.value}>
                                <div>
                                  <div className="font-medium">{platform.label}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {platform.description}
                                  </div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('platform') === 'custom' && (
                    <FormField
                      control={form.control}
                      name="custom_config"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Configuration</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder='{"build_command": "npm run build", "output_dir": "dist"}'
                              className="min-h-[100px] font-mono text-sm"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            JSON configuration for custom deployment
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </CardContent>
              </Card>

              {/* Actions */}
              <div className="flex items-center justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Rocket className="h-4 w-4 mr-2" />
                      Create Deployment
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Selected Content Preview */}
          {selectedContent && (
            <Card>
              <CardHeader>
                <CardTitle>Selected Content</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <h3 className="font-medium">{selectedContent.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedContent.meta_description}
                  </p>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{selectedContent.status}</Badge>
                    <Badge variant="outline">{selectedContent.ai_model}</Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Words: {selectedContent.content.split(' ').length}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Deployment Info */}
          <Card>
            <CardHeader>
              <CardTitle>Deployment Process</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <div className="text-sm">
                  <div className="font-medium">Content Processing</div>
                  <div className="text-muted-foreground">Convert content to static site</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <div className="text-sm">
                  <div className="font-medium">Build & Deploy</div>
                  <div className="text-muted-foreground">Deploy to selected platform</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <div className="text-sm">
                  <div className="font-medium">DNS Configuration</div>
                  <div className="text-muted-foreground">Configure domain and SSL</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tips */}
          <Card>
            <CardHeader>
              <CardTitle>Tips</CardTitle>
            </CardHeader>
            <CardContent className="text-sm space-y-2">
              <p>• Vercel offers the fastest deployment times</p>
              <p>• Make sure your domain DNS is properly configured</p>
              <p>• SSL certificates are automatically provisioned</p>
              <p>• You can monitor deployment progress in real-time</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

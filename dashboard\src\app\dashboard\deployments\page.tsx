'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Search, 
  Filter, 
  Plus, 
  MoreHorizontal, 
  Eye, 
  Trash2, 
  Rocket,
  RefreshCw,
  Download,
  ExternalLink,
  Play,
  Pause,
  Square,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react'
import { deploymentsApi, contentApi } from '@/lib/api'
import { useAuth } from '@/components/auth/AuthGuard'
import { PERMISSIONS } from '@/lib/auth'
import { formatRelativeTime, formatDuration } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'
import { Deployment } from '@/types'

const STATUSES = ['pending', 'running', 'completed', 'failed', 'cancelled']

export default function DeploymentsPage() {
  const router = useRouter()
  const { checkPermission } = useAuth()
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // State
  const [selectedDeployments, setSelectedDeployments] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [page, setPage] = useState(1)
  const [limit] = useState(20)

  // Query
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['deployments', { page, limit, searchQuery, statusFilter }],
    queryFn: async () => {
      const params: any = { page, limit }
      if (searchQuery) params.search = searchQuery
      if (statusFilter !== 'all') params.status = statusFilter
      
      const response = await deploymentsApi.getDeployments(params)
      return response.data
    },
    refetchInterval: 30000, // Auto-refresh every 30 seconds
  })

  // Mutations
  const cancelMutation = useMutation({
    mutationFn: (id: string) => deploymentsApi.cancelDeployment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deployments'] })
      toast({ title: 'Success', description: 'Deployment cancelled successfully' })
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to cancel deployment', variant: 'destructive' })
    }
  })

  const retryMutation = useMutation({
    mutationFn: async (deployment: Deployment) => {
      // Create a new deployment with the same configuration
      const response = await deploymentsApi.createDeployment({
        content_id: deployment.content_id,
        site_name: deployment.site_name,
        domain: deployment.domain,
      })
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deployments'] })
      toast({ title: 'Success', description: 'Deployment restarted successfully' })
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to restart deployment', variant: 'destructive' })
    }
  })

  // Handlers
  const handleSelectDeployment = (deploymentId: string, checked: boolean) => {
    if (checked) {
      setSelectedDeployments([...selectedDeployments, deploymentId])
    } else {
      setSelectedDeployments(selectedDeployments.filter(id => id !== deploymentId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked && data?.data) {
      setSelectedDeployments(data.data.map((deployment: Deployment) => deployment.id))
    } else {
      setSelectedDeployments([])
    }
  }

  const handleBulkCancel = async () => {
    if (selectedDeployments.length === 0) return

    try {
      await Promise.all(selectedDeployments.map(id => cancelMutation.mutateAsync(id)))
      setSelectedDeployments([])
    } catch (error) {
      console.error('Bulk cancel failed:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'warning',
      running: 'default',
      completed: 'success',
      failed: 'destructive',
      cancelled: 'secondary'
    }
    
    const icons = {
      pending: Clock,
      running: Play,
      completed: CheckCircle,
      failed: XCircle,
      cancelled: Square
    }
    
    const Icon = icons[status as keyof typeof icons] || Clock
    
    return (
      <Badge variant={variants[status as keyof typeof variants] as any} className="flex items-center space-x-1">
        <Icon className="h-3 w-3" />
        <span>{status}</span>
      </Badge>
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'running':
        return <Play className="h-4 w-4 text-blue-500 animate-pulse" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  const canTrigger = checkPermission(PERMISSIONS.TRIGGER_DEPLOYMENTS)
  const canCancel = checkPermission(PERMISSIONS.TRIGGER_DEPLOYMENTS)

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">Failed to load deployments</p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Deployments</h1>
          <p className="text-muted-foreground">
            Monitor and manage content deployments
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {canTrigger && (
            <Button onClick={() => router.push('/dashboard/deployments/new')}>
              <Plus className="h-4 w-4 mr-2" />
              New Deployment
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search deployments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                {STATUSES.map(status => (
                  <SelectItem key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>

            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedDeployments.length > 0 && (
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {selectedDeployments.length} deployment(s) selected
              </span>
              <div className="flex items-center space-x-2">
                {canCancel && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={handleBulkCancel}
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Cancel Selected
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Deployments Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={data?.data && selectedDeployments.length === data.data.length}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Site Name</TableHead>
              <TableHead>Domain</TableHead>
              <TableHead>Content</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Started</TableHead>
              <TableHead>Completed</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell colSpan={9}>
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  </TableCell>
                </TableRow>
              ))
            ) : data?.data?.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <Rocket className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-muted-foreground">No deployments found</p>
                </TableCell>
              </TableRow>
            ) : (
              data?.data?.map((deployment: Deployment) => (
                <TableRow key={deployment.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedDeployments.includes(deployment.id)}
                      onCheckedChange={(checked) => handleSelectDeployment(deployment.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(deployment.status)}
                      <span className="font-medium">{deployment.site_name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs truncate" title={deployment.domain}>
                      {deployment.domain}
                    </div>
                  </TableCell>
                  <TableCell>
                    {deployment.content ? (
                      <div className="max-w-xs truncate" title={deployment.content.title}>
                        {deployment.content.title}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>{getStatusBadge(deployment.status)}</TableCell>
                  <TableCell>
                    {deployment.duration ? formatDuration(deployment.duration) : '-'}
                  </TableCell>
                  <TableCell>{formatRelativeTime(deployment.started_at)}</TableCell>
                  <TableCell>
                    {deployment.completed_at ? formatRelativeTime(deployment.completed_at) : '-'}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => router.push(`/dashboard/deployments/${deployment.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        {deployment.deployment_url && (
                          <DropdownMenuItem
                            onClick={() => window.open(deployment.deployment_url, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            View Live Site
                          </DropdownMenuItem>
                        )}
                        {deployment.status === 'failed' && canTrigger && (
                          <DropdownMenuItem
                            onClick={() => retryMutation.mutate(deployment)}
                          >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Retry Deployment
                          </DropdownMenuItem>
                        )}
                        {(deployment.status === 'pending' || deployment.status === 'running') && canCancel && (
                          <DropdownMenuItem
                            onClick={() => cancelMutation.mutate(deployment.id)}
                            className="text-red-600"
                          >
                            <Square className="h-4 w-4 mr-2" />
                            Cancel
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Pagination */}
      {data?.pagination && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {((page - 1) * limit) + 1} to {Math.min(page * limit, data.pagination.total)} of {data.pagination.total} deployments
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={!data.pagination.has_prev}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={!data.pagination.has_next}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

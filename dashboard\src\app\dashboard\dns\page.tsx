'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Globe,
  RefreshCw,
  Settings,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Copy,
  ExternalLink
} from 'lucide-react'
import { dnsApi } from '@/lib/api'
import { useAuth } from '@/components/auth/AuthGuard'
import { PERMISSIONS } from '@/lib/auth'
import { formatRelativeTime } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'
import { Domain, DNSRecord } from '@/types'

const RECORD_TYPES = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS']

export default function DNSPage() {
  const router = useRouter()
  const { checkPermission } = useAuth()
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // State
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedDomain, setSelectedDomain] = useState<string>('all')
  const [recordTypeFilter, setRecordTypeFilter] = useState<string>('all')

  // Queries
  const { data: domains, isLoading: domainsLoading, refetch: refetchDomains } = useQuery({
    queryKey: ['domains'],
    queryFn: async () => {
      const response = await dnsApi.getDomains()
      return response.data as Domain[]
    },
  })

  const { data: records, isLoading: recordsLoading, refetch: refetchRecords } = useQuery({
    queryKey: ['dns-records', selectedDomain],
    queryFn: async () => {
      if (selectedDomain === 'all') {
        // Get all records for all domains
        const allRecords: DNSRecord[] = []
        if (domains) {
          for (const domain of domains) {
            const response = await dnsApi.getDnsRecords(domain.name)
            allRecords.push(...response.data)
          }
        }
        return allRecords
      } else {
        const response = await dnsApi.getDnsRecords(selectedDomain)
        return response.data as DNSRecord[]
      }
    },
    enabled: !!domains && (selectedDomain === 'all' || !!selectedDomain),
  })

  // Mutations
  const deleteDomainMutation = useMutation({
    mutationFn: (domain: string) => dnsApi.deleteDomain(domain),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['domains'] })
      toast({ title: 'Success', description: 'Domain deleted successfully' })
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to delete domain', variant: 'destructive' })
    }
  })

  const deleteRecordMutation = useMutation({
    mutationFn: ({ domain, recordId }: { domain: string; recordId: string }) => 
      dnsApi.deleteDnsRecord(domain, recordId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dns-records'] })
      toast({ title: 'Success', description: 'DNS record deleted successfully' })
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to delete DNS record', variant: 'destructive' })
    }
  })

  const validateDomainMutation = useMutation({
    mutationFn: (domain: string) => dnsApi.validateDomain(domain),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['domains'] })
      toast({ title: 'Success', description: 'Domain validation completed' })
    },
    onError: () => {
      toast({ title: 'Error', description: 'Domain validation failed', variant: 'destructive' })
    }
  })

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({ title: 'Copied', description: 'Value copied to clipboard' })
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to copy to clipboard', variant: 'destructive' })
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'success',
      inactive: 'secondary',
      pending: 'warning'
    }
    return (
      <Badge variant={variants[status as keyof typeof variants] as any}>
        {status}
      </Badge>
    )
  }

  const getRecordTypeColor = (type: string) => {
    const colors = {
      A: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      AAAA: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      CNAME: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      MX: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
      TXT: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      NS: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const filteredRecords = records?.filter(record => {
    const matchesSearch = !searchQuery || 
      record.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.value.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesType = recordTypeFilter === 'all' || record.type === recordTypeFilter
    
    return matchesSearch && matchesType
  })

  const canManage = checkPermission(PERMISSIONS.MANAGE_DNS)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">DNS Management</h1>
          <p className="text-muted-foreground">
            Manage domains and DNS records
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {canManage && (
            <>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/dns/manage')}
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage DNS
              </Button>
              <Button onClick={() => router.push('/dashboard/dns/domains/new')}>
                <Plus className="h-4 w-4 mr-2" />
                Add Domain
              </Button>
            </>
          )}
        </div>
      </div>

      <Tabs defaultValue="records" className="space-y-6">
        <TabsList>
          <TabsTrigger value="records">DNS Records</TabsTrigger>
          <TabsTrigger value="domains">Domains ({domains?.length || 0})</TabsTrigger>
        </TabsList>

        <TabsContent value="records" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search records..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={selectedDomain} onValueChange={setSelectedDomain}>
                  <SelectTrigger>
                    <SelectValue placeholder="Domain" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Domains</SelectItem>
                    {domains?.map(domain => (
                      <SelectItem key={domain.name} value={domain.name}>
                        {domain.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={recordTypeFilter} onValueChange={setRecordTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Record Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {RECORD_TYPES.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button variant="outline" onClick={() => refetchRecords()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* DNS Records Table */}
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>TTL</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Domain</TableHead>
                  <TableHead>Updated</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recordsLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell colSpan={8}>
                        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : filteredRecords?.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <Globe className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      <p className="text-muted-foreground">No DNS records found</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRecords?.map((record: DNSRecord) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">
                        <div className="max-w-xs truncate" title={record.name}>
                          {record.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRecordTypeColor(record.type)}>
                          {record.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs truncate" title={record.value}>
                          {record.value}
                        </div>
                      </TableCell>
                      <TableCell>{record.ttl}</TableCell>
                      <TableCell>{record.priority || '-'}</TableCell>
                      <TableCell>{record.domain}</TableCell>
                      <TableCell>{formatRelativeTime(record.updated_at)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => copyToClipboard(record.value)}
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Copy Value
                            </DropdownMenuItem>
                            {canManage && (
                              <>
                                <DropdownMenuItem
                                  onClick={() => router.push(`/dashboard/dns/records/${record.id}/edit`)}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit Record
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => deleteRecordMutation.mutate({ 
                                    domain: record.domain, 
                                    recordId: record.id 
                                  })}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Record
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </Card>
        </TabsContent>

        <TabsContent value="domains" className="space-y-6">
          {/* Domains Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {domainsLoading ? (
              Array.from({ length: 6 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="h-24 bg-gray-200 rounded animate-pulse"></div>
                  </CardContent>
                </Card>
              ))
            ) : domains?.length === 0 ? (
              <Card className="col-span-full">
                <CardContent className="p-8 text-center">
                  <Globe className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-muted-foreground">No domains configured</p>
                  {canManage && (
                    <Button 
                      className="mt-4"
                      onClick={() => router.push('/dashboard/dns/domains/new')}
                    >
                      Add Your First Domain
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              domains?.map((domain: Domain) => (
                <Card key={domain.name}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <Globe className="h-5 w-5 text-blue-500" />
                        <h3 className="font-medium">{domain.name}</h3>
                      </div>
                      {getStatusBadge(domain.status)}
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Records:</span>
                        <span>{domain.records?.length || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Registrar:</span>
                        <span>{domain.registrar}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created:</span>
                        <span>{formatRelativeTime(domain.created_at)}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-4 pt-4 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedDomain(domain.name)}
                      >
                        View Records
                      </Button>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => router.push(`/dashboard/dns/domains/${domain.name}`)}
                          >
                            <Settings className="h-4 w-4 mr-2" />
                            Manage Domain
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => validateDomainMutation.mutate(domain.name)}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Validate Domain
                          </DropdownMenuItem>
                          {canManage && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => deleteDomainMutation.mutate(domain.name)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Domain
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

'use client'

import { AuthGuard } from '@/components/auth/AuthGuard'
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar'
import { DashboardHeader } from '@/components/dashboard/DashboardHeader'
import { PERMISSIONS } from '@/lib/auth'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthGuard 
      requiredPermissions={[PERMISSIONS.READ_SYSTEM]}
      redirectTo="/auth/login"
    >
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <DashboardSidebar />
        
        <div className="lg:pl-64">
          <DashboardHeader />
          
          <main className="py-6">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </main>
        </div>
      </div>
    </AuthGuard>
  )
}

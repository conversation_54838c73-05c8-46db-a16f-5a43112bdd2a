'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { 
  BarChart3, 
  TrendingUp, 
  Globe, 
  Rocket, 
  Activity, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Users
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MetricCard } from '@/components/dashboard/MetricCard'
import { SystemHealthWidget } from '@/components/dashboard/SystemHealthWidget'
import { RecentActivityWidget } from '@/components/dashboard/RecentActivityWidget'
import { TrendsOverviewChart } from '@/components/dashboard/TrendsOverviewChart'
import { DeploymentStatusChart } from '@/components/dashboard/DeploymentStatusChart'
import { useWebSocket } from '@/hooks/useWebSocket'
import { adminApi, trendsApi, deploymentsApi, analyticsApi } from '@/lib/api'
import { DashboardStats, SystemHealth } from '@/types'
import { formatNumber, formatRelativeTime } from '@/lib/utils'

const DashboardPage: React.FC = () => {
  const { data: session } = useSession()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // WebSocket for real-time updates
  const { isConnected, lastMessage } = useWebSocket('ws://localhost:8000/ws/dashboard')

  // Load initial data
  useEffect(() => {
    loadDashboardData()
  }, [])

  // Handle real-time updates
  useEffect(() => {
    if (lastMessage) {
      handleRealtimeUpdate(lastMessage)
    }
  }, [lastMessage])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load dashboard statistics
      const [
        trendsResponse,
        deploymentsResponse,
        healthResponse,
        analyticsResponse
      ] = await Promise.all([
        trendsApi.getStats(),
        deploymentsApi.getStats(),
        adminApi.getSystemHealth(),
        analyticsApi.getOverview('24h')
      ])

      // Combine stats from different sources
      const dashboardStats: DashboardStats = {
        totalTrends: trendsResponse.data?.total || 0,
        activeTrends: trendsResponse.data?.active || 0,
        totalDeployments: deploymentsResponse.data?.total || 0,
        activeDeployments: deploymentsResponse.data?.active || 0,
        totalDNSRecords: analyticsResponse.data?.dns_records || 0,
        activeDNSRecords: analyticsResponse.data?.active_dns_records || 0,
        systemHealth: healthResponse.data?.status || 'unknown',
        lastUpdated: new Date().toISOString()
      }

      setStats(dashboardStats)
      setSystemHealth(healthResponse.data)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const handleRealtimeUpdate = (message: any) => {
    try {
      const update = JSON.parse(message.data)
      
      switch (update.type) {
        case 'stats_update':
          setStats(prev => prev ? { ...prev, ...update.data } : null)
          break
        case 'health_update':
          setSystemHealth(update.data)
          break
        default:
          // Handle other update types
          break
      }
    } catch (err) {
      console.error('Error handling realtime update:', err)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card">
                <div className="card-content p-6">
                  <div className="loading-pulse h-4 w-24 mb-2"></div>
                  <div className="loading-pulse h-8 w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="card">
          <div className="card-content p-6">
            <div className="flex items-center space-x-2 text-error-600">
              <AlertTriangle className="h-5 w-5" />
              <span>Error loading dashboard: {error}</span>
            </div>
            <button 
              onClick={loadDashboardData}
              className="btn btn-primary mt-4"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {session?.user?.name}
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className={`status-indicator ${isConnected ? 'status-healthy' : 'status-error'}`}>
              <div className="status-dot"></div>
              <span className="text-sm">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            {stats && (
              <span className="text-sm text-muted-foreground">
                Updated {formatRelativeTime(stats.lastUpdated)}
              </span>
            )}
          </div>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total Trends"
            value={formatNumber(stats?.totalTrends || 0)}
            icon={<BarChart3 className="h-5 w-5" />}
            color="primary"
            change={{
              value: 12,
              type: 'increase',
              period: 'vs last week'
            }}
          />
          
          <MetricCard
            title="Active Deployments"
            value={formatNumber(stats?.activeDeployments || 0)}
            icon={<Rocket className="h-5 w-5" />}
            color="success"
            change={{
              value: 3,
              type: 'increase',
              period: 'vs yesterday'
            }}
          />
          
          <MetricCard
            title="DNS Records"
            value={formatNumber(stats?.activeDNSRecords || 0)}
            icon={<Globe className="h-5 w-5" />}
            color="warning"
          />
          
          <MetricCard
            title="System Health"
            value={stats?.systemHealth === 'healthy' ? 'Healthy' : 'Issues'}
            icon={stats?.systemHealth === 'healthy' ? 
              <CheckCircle className="h-5 w-5" /> : 
              <AlertTriangle className="h-5 w-5" />
            }
            color={stats?.systemHealth === 'healthy' ? 'success' : 'error'}
          />
        </div>

        {/* Charts and Widgets */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Trends Overview Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Trends Overview</span>
              </CardTitle>
              <CardDescription>
                Trend creation and approval over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TrendsOverviewChart />
            </CardContent>
          </Card>

          {/* Deployment Status Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Rocket className="h-5 w-5" />
                <span>Deployment Status</span>
              </CardTitle>
              <CardDescription>
                Current deployment status distribution
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DeploymentStatusChart />
            </CardContent>
          </Card>
        </div>

        {/* System Health and Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* System Health Widget */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>System Health</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <SystemHealthWidget health={systemHealth} />
            </CardContent>
          </Card>

          {/* Recent Activity Widget */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Recent Activity</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <RecentActivityWidget />
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <button className="btn btn-outline flex items-center space-x-2 p-4 h-auto">
                <BarChart3 className="h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">Add Trend</div>
                  <div className="text-sm text-muted-foreground">Create new trend</div>
                </div>
              </button>
              
              <button className="btn btn-outline flex items-center space-x-2 p-4 h-auto">
                <Rocket className="h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">Deploy</div>
                  <div className="text-sm text-muted-foreground">Start deployment</div>
                </div>
              </button>
              
              <button className="btn btn-outline flex items-center space-x-2 p-4 h-auto">
                <Globe className="h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">DNS Setup</div>
                  <div className="text-sm text-muted-foreground">Configure DNS</div>
                </div>
              </button>
              
              <button className="btn btn-outline flex items-center space-x-2 p-4 h-auto">
                <Users className="h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">Manage Users</div>
                  <div className="text-sm text-muted-foreground">User administration</div>
                </div>
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default DashboardPage

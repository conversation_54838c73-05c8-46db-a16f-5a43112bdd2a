'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { 
  Activity,
  Server,
  Database,
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { systemApi } from '@/lib/api'
import { formatBytes, formatDuration } from '@/lib/utils'
import { SystemHealthWidget } from '@/components/dashboard/SystemHealthWidget'

export default function SystemHealthPage() {
  const [refreshInterval, setRefreshInterval] = useState(30000) // 30 seconds

  // Queries
  const { data: healthData, isLoading, refetch } = useQuery({
    queryKey: ['system-health'],
    queryFn: async () => {
      const response = await systemApi.getHealth()
      return response.data
    },
    refetchInterval: refreshInterval,
  })

  const { data: metricsData } = useQuery({
    queryKey: ['system-metrics', '1h'],
    queryFn: async () => {
      const [cpu, memory, disk] = await Promise.all([
        systemApi.getMetrics('cpu', '1h'),
        systemApi.getMetrics('memory', '1h'),
        systemApi.getMetrics('disk', '1h')
      ])
      
      return {
        cpu: cpu.data,
        memory: memory.data,
        disk: disk.data
      }
    },
    refetchInterval: 60000, // 1 minute
  })

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
      case 'degraded':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'critical':
      case 'error':
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'active':
        return 'success'
      case 'warning':
      case 'degraded':
        return 'warning'
      case 'critical':
      case 'error':
      case 'failed':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const formatMetricValue = (value: number, type: string) => {
    switch (type) {
      case 'cpu':
      case 'memory':
      case 'disk':
        return `${value.toFixed(1)}%`
      case 'bytes':
        return formatBytes(value)
      case 'duration':
        return formatDuration(value)
      default:
        return value.toString()
    }
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium mb-2">{new Date(label).toLocaleTimeString()}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="capitalize">{entry.dataKey}:</span>
              <span className="font-medium">{formatMetricValue(entry.value, entry.dataKey)}</span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">System Health</h1>
          <div className="h-10 w-24 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Health</h1>
          <p className="text-muted-foreground">
            Monitor system performance and service status
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overall Status */}
      {healthData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {getStatusIcon(healthData.status)}
              <span>System Status: {healthData.status.charAt(0).toUpperCase() + healthData.status.slice(1)}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatDuration(healthData.system.uptime)}
                </div>
                <div className="text-sm text-muted-foreground">Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {healthData.system.cpu_usage.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">CPU Usage</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {healthData.system.memory_usage.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">Memory Usage</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {healthData.system.disk_usage.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">Disk Usage</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* System Health Widget */}
            <Card>
              <CardHeader>
                <CardTitle>System Health</CardTitle>
              </CardHeader>
              <CardContent>
                <SystemHealthWidget health={healthData} />
              </CardContent>
            </Card>

            {/* Resource Usage */}
            <Card>
              <CardHeader>
                <CardTitle>Resource Usage</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Cpu className="h-4 w-4 text-blue-500" />
                      <span>CPU</span>
                    </div>
                    <span className="font-medium">{healthData?.system.cpu_usage.toFixed(1)}%</span>
                  </div>
                  <Progress value={healthData?.system.cpu_usage} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <MemoryStick className="h-4 w-4 text-green-500" />
                      <span>Memory</span>
                    </div>
                    <span className="font-medium">{healthData?.system.memory_usage.toFixed(1)}%</span>
                  </div>
                  <Progress value={healthData?.system.memory_usage} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <HardDrive className="h-4 w-4 text-purple-500" />
                      <span>Disk</span>
                    </div>
                    <span className="font-medium">{healthData?.system.disk_usage.toFixed(1)}%</span>
                  </div>
                  <Progress value={healthData?.system.disk_usage} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {healthData?.services && Object.entries(healthData.services).map(([serviceName, serviceData]) => (
              <Card key={serviceName}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Server className="h-5 w-5 text-blue-500" />
                      <h3 className="font-medium capitalize">{serviceName}</h3>
                    </div>
                    <Badge variant={getStatusColor(serviceData.status) as any}>
                      {serviceData.status}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    {serviceName === 'api' && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Response Time:</span>
                        <span>{serviceData.response_time}ms</span>
                      </div>
                    )}
                    {serviceName === 'database' && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Connections:</span>
                          <span>{serviceData.connections}/{serviceData.max_connections}</span>
                        </div>
                        <Progress 
                          value={(serviceData.connections / serviceData.max_connections) * 100} 
                          className="h-2" 
                        />
                      </>
                    )}
                    {serviceName === 'redis' && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Memory:</span>
                        <span>{formatBytes(serviceData.memory_usage)}</span>
                      </div>
                    )}
                    {serviceName === 'scraper' && serviceData.active_jobs > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Active Jobs:</span>
                        <span>{serviceData.active_jobs}</span>
                      </div>
                    )}
                    {serviceName === 'generator' && serviceData.queue_size > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Queue Size:</span>
                        <span>{serviceData.queue_size}</span>
                      </div>
                    )}
                    {serviceName === 'deployer' && serviceData.active_deployments > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Active Deployments:</span>
                        <span>{serviceData.active_deployments}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          {metricsData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>CPU Usage (Last Hour)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={metricsData.cpu}>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis 
                          dataKey="timestamp" 
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                        />
                        <YAxis tick={{ fontSize: 12 }} />
                        <Tooltip content={<CustomTooltip />} />
                        <Area
                          type="monotone"
                          dataKey="cpu"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.6}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Memory Usage (Last Hour)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={metricsData.memory}>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis 
                          dataKey="timestamp" 
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                        />
                        <YAxis tick={{ fontSize: 12 }} />
                        <Tooltip content={<CustomTooltip />} />
                        <Area
                          type="monotone"
                          dataKey="memory"
                          stroke="#10b981"
                          fill="#10b981"
                          fillOpacity={0.6}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Disk Usage (Last Hour)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={metricsData.disk}>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis 
                          dataKey="timestamp" 
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                        />
                        <YAxis tick={{ fontSize: 12 }} />
                        <Tooltip content={<CustomTooltip />} />
                        <Area
                          type="monotone"
                          dataKey="disk"
                          stroke="#8b5cf6"
                          fill="#8b5cf6"
                          fillOpacity={0.6}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="performance">
          <Card>
            <CardContent className="p-8 text-center">
              <Activity className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-muted-foreground">Performance analytics coming soon</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

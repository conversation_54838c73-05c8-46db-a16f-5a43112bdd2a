'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, ReactNode } from 'react'
import { Loader2 } from 'lucide-react'
import { hasPermission, hasRole, PERMISSIONS, ROLES } from '@/lib/auth'

interface AuthGuardProps {
  children: ReactNode
  requiredPermissions?: string[]
  requiredRoles?: string[]
  fallback?: ReactNode
  redirectTo?: string
}

export function AuthGuard({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  fallback,
  redirectTo = '/auth/login'
}: AuthGuardProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (status === 'unauthenticated') {
      // Redirect to login with callback URL
      const callbackUrl = encodeURIComponent(window.location.pathname + window.location.search)
      router.push(`${redirectTo}?callbackUrl=${callbackUrl}`)
      return
    }

    if (session) {
      // Check role requirements
      if (requiredRoles.length > 0 && !hasRole(session.user.role, requiredRoles)) {
        router.push('/auth/error?error=AccessDenied')
        return
      }

      // Check permission requirements
      if (requiredPermissions.length > 0) {
        const hasRequiredPermissions = requiredPermissions.every(permission =>
          hasPermission(session.user.permissions, permission)
        )
        
        if (!hasRequiredPermissions) {
          router.push('/auth/error?error=AccessDenied')
          return
        }
      }
    }
  }, [session, status, router, requiredPermissions, requiredRoles, redirectTo])

  // Show loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Show fallback if not authenticated
  if (status === 'unauthenticated') {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // Check authorization
  if (session) {
    // Check role requirements
    if (requiredRoles.length > 0 && !hasRole(session.user.role, requiredRoles)) {
      return fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-600">Access denied. Insufficient role permissions.</p>
          </div>
        </div>
      )
    }

    // Check permission requirements
    if (requiredPermissions.length > 0) {
      const hasRequiredPermissions = requiredPermissions.every(permission =>
        hasPermission(session.user.permissions, permission)
      )
      
      if (!hasRequiredPermissions) {
        return fallback || (
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-600">Access denied. Insufficient permissions.</p>
            </div>
          </div>
        )
      }
    }
  }

  return <>{children}</>
}

// Convenience components for common permission checks
export function AdminOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <AuthGuard requiredRoles={[ROLES.ADMIN]} fallback={fallback}>
      {children}
    </AuthGuard>
  )
}

export function EditorOrAdmin({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <AuthGuard requiredRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={fallback}>
      {children}
    </AuthGuard>
  )
}

export function WithPermission({ 
  children, 
  permission, 
  fallback 
}: { 
  children: ReactNode
  permission: string
  fallback?: ReactNode 
}) {
  return (
    <AuthGuard requiredPermissions={[permission]} fallback={fallback}>
      {children}
    </AuthGuard>
  )
}

// Hook for checking permissions in components
export function useAuth() {
  const { data: session, status } = useSession()
  
  const checkPermission = (permission: string): boolean => {
    if (!session?.user?.permissions) return false
    return hasPermission(session.user.permissions, permission)
  }
  
  const checkRole = (role: string): boolean => {
    if (!session?.user?.role) return false
    return hasRole(session.user.role, [role])
  }
  
  const checkMultiplePermissions = (permissions: string[]): boolean => {
    if (!session?.user?.permissions) return false
    return permissions.every(permission => 
      hasPermission(session.user.permissions, permission)
    )
  }
  
  return {
    session,
    status,
    isAuthenticated: status === 'authenticated',
    isLoading: status === 'loading',
    user: session?.user,
    checkPermission,
    checkRole,
    checkMultiplePermissions,
    isAdmin: session?.user?.role === ROLES.ADMIN,
    isEditor: session?.user?.role === ROLES.EDITOR || session?.user?.role === ROLES.ADMIN,
    isViewer: !!session?.user?.role,
  }
}

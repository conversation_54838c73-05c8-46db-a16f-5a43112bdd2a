'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useAuth } from '@/components/auth/AuthGuard'
import { PERMISSIONS } from '@/lib/auth'
import {
  BarChart3,
  TrendingUp,
  Globe,
  Rocket,
  Activity,
  Users,
  Settings,
  FileText,
  Database,
  Shield,
  Zap,
  Home,
  ChevronDown,
  ChevronRight
} from 'lucide-react'

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  permission?: string
  children?: NavigationItem[]
}

const navigation: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    permission: PERMISSIONS.READ_SYSTEM
  },
  {
    name: 'Trends',
    href: '/dashboard/trends',
    icon: TrendingUp,
    permission: PERMISSIONS.READ_TRENDS,
    children: [
      {
        name: 'All Trends',
        href: '/dashboard/trends',
        icon: BarChart3,
        permission: PERMISSIONS.READ_TRENDS
      },
      {
        name: 'Add Trend',
        href: '/dashboard/trends/new',
        icon: TrendingUp,
        permission: PERMISSIONS.WRITE_TRENDS
      },
      {
        name: 'Analytics',
        href: '/dashboard/trends/analytics',
        icon: BarChart3,
        permission: PERMISSIONS.READ_ANALYTICS
      }
    ]
  },
  {
    name: 'Content',
    href: '/dashboard/content',
    icon: FileText,
    permission: PERMISSIONS.READ_CONTENT,
    children: [
      {
        name: 'All Content',
        href: '/dashboard/content',
        icon: FileText,
        permission: PERMISSIONS.READ_CONTENT
      },
      {
        name: 'Generate',
        href: '/dashboard/content/generate',
        icon: Zap,
        permission: PERMISSIONS.REGENERATE_CONTENT
      }
    ]
  },
  {
    name: 'Deployments',
    href: '/dashboard/deployments',
    icon: Rocket,
    permission: PERMISSIONS.READ_DEPLOYMENTS,
    children: [
      {
        name: 'All Deployments',
        href: '/dashboard/deployments',
        icon: Rocket,
        permission: PERMISSIONS.READ_DEPLOYMENTS
      },
      {
        name: 'Deploy Now',
        href: '/dashboard/deployments/new',
        icon: Zap,
        permission: PERMISSIONS.TRIGGER_DEPLOYMENTS
      }
    ]
  },
  {
    name: 'DNS & Domains',
    href: '/dashboard/dns',
    icon: Globe,
    permission: PERMISSIONS.READ_DNS,
    children: [
      {
        name: 'DNS Records',
        href: '/dashboard/dns',
        icon: Globe,
        permission: PERMISSIONS.READ_DNS
      },
      {
        name: 'Manage DNS',
        href: '/dashboard/dns/manage',
        icon: Settings,
        permission: PERMISSIONS.MANAGE_DNS
      }
    ]
  },
  {
    name: 'Analytics',
    href: '/dashboard/analytics',
    icon: BarChart3,
    permission: PERMISSIONS.READ_ANALYTICS
  },
  {
    name: 'System',
    href: '/dashboard/system',
    icon: Activity,
    permission: PERMISSIONS.READ_SYSTEM,
    children: [
      {
        name: 'Health',
        href: '/dashboard/system/health',
        icon: Activity,
        permission: PERMISSIONS.READ_SYSTEM
      },
      {
        name: 'Logs',
        href: '/dashboard/system/logs',
        icon: FileText,
        permission: PERMISSIONS.READ_SYSTEM
      },
      {
        name: 'Database',
        href: '/dashboard/system/database',
        icon: Database,
        permission: PERMISSIONS.MANAGE_SYSTEM
      },
      {
        name: 'Maintenance',
        href: '/dashboard/system/maintenance',
        icon: Settings,
        permission: PERMISSIONS.TRIGGER_MAINTENANCE
      }
    ]
  },
  {
    name: 'Users',
    href: '/dashboard/users',
    icon: Users,
    permission: PERMISSIONS.READ_USERS
  },
  {
    name: 'Security',
    href: '/dashboard/security',
    icon: Shield,
    permission: PERMISSIONS.MANAGE_SYSTEM
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
    permission: PERMISSIONS.MANAGE_SYSTEM
  }
]

export function DashboardSidebar() {
  const pathname = usePathname()
  const { checkPermission } = useAuth()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isItemActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    // Check permissions
    if (item.permission && !checkPermission(item.permission)) {
      return null
    }

    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.name)
    const isActive = isItemActive(item.href)

    return (
      <div key={item.name}>
        <div className="group">
          {hasChildren ? (
            <button
              onClick={() => toggleExpanded(item.name)}
              className={cn(
                'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors',
                level > 0 && 'ml-4',
                isActive
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                  : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
              )}
            >
              <div className="flex items-center">
                <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                {item.name}
              </div>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
          ) : (
            <Link
              href={item.href}
              className={cn(
                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                level > 0 && 'ml-4',
                isActive
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                  : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
              )}
            >
              <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
              {item.name}
            </Link>
          )}
        </div>

        {/* Render children if expanded */}
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
      <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-gray-800 px-6 pb-4 shadow-sm border-r border-gray-200 dark:border-gray-700">
        {/* Logo */}
        <div className="flex h-16 shrink-0 items-center">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <TrendingUp className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900 dark:text-white">
              TrendSite
            </span>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex flex-1 flex-col">
          <ul role="list" className="flex flex-1 flex-col gap-y-7">
            <li>
              <ul role="list" className="-mx-2 space-y-1">
                {navigation.map(item => (
                  <li key={item.name}>
                    {renderNavigationItem(item)}
                  </li>
                ))}
              </ul>
            </li>
          </ul>
        </nav>

        {/* Footer */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <p>TrendSite Dashboard</p>
            <p>v1.0.0</p>
          </div>
        </div>
      </div>
    </div>
  )
}

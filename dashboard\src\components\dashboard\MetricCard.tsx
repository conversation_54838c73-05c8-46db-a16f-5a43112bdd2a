'use client'

import React from 'react'
import { TrendingUp, TrendingDown } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { MetricCard as MetricCardType } from '@/types'

interface MetricCardProps extends MetricCardType {
  className?: string
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  color = 'primary',
  className
}) => {
  const colorClasses = {
    primary: 'text-primary bg-primary/10',
    success: 'text-success-600 bg-success-50',
    warning: 'text-warning-600 bg-warning-50',
    error: 'text-error-600 bg-error-50'
  }

  const changeColorClasses = {
    increase: 'text-success-600',
    decrease: 'text-error-600'
  }

  return (
    <Card className={cn('transition-all hover:shadow-md', className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-muted-foreground mb-1">
              {title}
            </p>
            <p className="text-2xl font-bold">
              {value}
            </p>
            
            {change && (
              <div className={cn(
                'flex items-center space-x-1 mt-2 text-sm',
                changeColorClasses[change.type]
              )}>
                {change.type === 'increase' ? (
                  <TrendingUp className="h-3 w-3" />
                ) : (
                  <TrendingDown className="h-3 w-3" />
                )}
                <span className="font-medium">
                  {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
                </span>
                <span className="text-muted-foreground">
                  {change.period}
                </span>
              </div>
            )}
          </div>
          
          {icon && (
            <div className={cn(
              'p-3 rounded-full',
              colorClasses[color]
            )}>
              {icon}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

'use client'

import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  TrendingUp, 
  FileText, 
  Rocket, 
  Globe, 
  User, 
  AlertTriangle,
  CheckCircle,
  Clock,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import { formatRelativeTime } from '@/lib/utils'
import { adminApi } from '@/lib/api'

interface ActivityItem {
  id: string
  type: 'trend' | 'content' | 'deployment' | 'dns' | 'user' | 'system'
  action: string
  description: string
  user: {
    name: string
    email: string
  }
  metadata: {
    resource_id?: string
    resource_name?: string
    status?: string
    [key: string]: any
  }
  timestamp: string
  severity: 'info' | 'warning' | 'error' | 'success'
}

export function RecentActivityWidget() {
  const [activities, setActivities] = useState<ActivityItem[]>([])

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['recent-activity'],
    queryFn: async () => {
      const response = await adminApi.getRecentActivity({ limit: 20 })
      return response.data
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  })

  useEffect(() => {
    if (data) {
      setActivities(data)
    }
  }, [data])

  const getActivityIcon = (type: string, severity: string) => {
    const iconClass = "h-4 w-4"
    
    switch (type) {
      case 'trend':
        return <TrendingUp className={iconClass} />
      case 'content':
        return <FileText className={iconClass} />
      case 'deployment':
        return <Rocket className={iconClass} />
      case 'dns':
        return <Globe className={iconClass} />
      case 'user':
        return <User className={iconClass} />
      case 'system':
        if (severity === 'error') {
          return <AlertTriangle className={`${iconClass} text-red-500`} />
        } else if (severity === 'success') {
          return <CheckCircle className={`${iconClass} text-green-500`} />
        }
        return <Clock className={iconClass} />
      default:
        return <Clock className={iconClass} />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'destructive'
      case 'warning':
        return 'warning'
      case 'success':
        return 'success'
      case 'info':
      default:
        return 'secondary'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'trend':
        return 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
      case 'content':
        return 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300'
      case 'deployment':
        return 'bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300'
      case 'dns':
        return 'bg-orange-50 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300'
      case 'user':
        return 'bg-pink-50 text-pink-700 dark:bg-pink-900/20 dark:text-pink-300'
      case 'system':
        return 'bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300'
      default:
        return 'bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-start space-x-3 animate-pulse">
            <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
        <p className="text-sm text-muted-foreground mb-4">
          Failed to load recent activity
        </p>
        <Button variant="outline" size="sm" onClick={() => refetch()}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    )
  }

  if (!activities || activities.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">
          No recent activity
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Recent Activity</h3>
        <Button variant="ghost" size="sm" onClick={() => refetch()}>
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3">
              {/* Icon */}
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                {getActivityIcon(activity.type, activity.severity)}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${getTypeColor(activity.type)}`}
                  >
                    {activity.type}
                  </Badge>
                  <Badge 
                    variant={getSeverityColor(activity.severity) as any}
                    className="text-xs"
                  >
                    {activity.action}
                  </Badge>
                </div>

                <p className="text-sm text-gray-900 dark:text-gray-100 mb-1">
                  {activity.description}
                </p>

                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>
                    by {activity.user.name}
                  </span>
                  <span>
                    {formatRelativeTime(activity.timestamp)}
                  </span>
                </div>

                {/* Metadata */}
                {activity.metadata.resource_name && (
                  <div className="mt-2 flex items-center space-x-2">
                    <span className="text-xs text-muted-foreground">
                      Resource: {activity.metadata.resource_name}
                    </span>
                    {activity.metadata.resource_id && (
                      <Button variant="ghost" size="sm" className="h-auto p-0">
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                )}

                {activity.metadata.status && (
                  <div className="mt-1">
                    <Badge variant="outline" className="text-xs">
                      Status: {activity.metadata.status}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* View All Link */}
      <div className="text-center pt-2 border-t">
        <Button variant="ghost" size="sm">
          View All Activity
          <ExternalLink className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  )
}

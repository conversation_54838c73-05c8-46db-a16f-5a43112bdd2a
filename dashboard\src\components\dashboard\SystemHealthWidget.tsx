'use client'

import { useEffect, useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Activity,
  Server,
  Database,
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical' | 'unknown'
  services: {
    api: { status: string; response_time: number }
    database: { status: string; connections: number; max_connections: number }
    redis: { status: string; memory_usage: number }
    scraper: { status: string; active_jobs: number }
    generator: { status: string; queue_size: number }
    deployer: { status: string; active_deployments: number }
  }
  system: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    uptime: number
  }
  last_updated: string
}

interface SystemHealthWidgetProps {
  health: SystemHealth | null
}

export function SystemHealthWidget({ health }: SystemHealthWidgetProps) {
  const [localHealth, setLocalHealth] = useState<SystemHealth | null>(health)

  useEffect(() => {
    setLocalHealth(health)
  }, [health])

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'critical':
      case 'error':
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'active':
        return 'success'
      case 'warning':
      case 'degraded':
        return 'warning'
      case 'critical':
      case 'error':
      case 'failed':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  if (!localHealth) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Activity className="h-4 w-4 animate-pulse" />
          <span className="text-sm text-muted-foreground">Loading system health...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Overall Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStatusIcon(localHealth.status)}
          <span className="font-medium">System Status</span>
        </div>
        <Badge variant={getStatusColor(localHealth.status) as any}>
          {localHealth.status.charAt(0).toUpperCase() + localHealth.status.slice(1)}
        </Badge>
      </div>

      {/* System Metrics */}
      <div className="space-y-3">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <Cpu className="h-4 w-4 text-blue-500" />
              <span>CPU Usage</span>
            </div>
            <span className="font-medium">{localHealth.system.cpu_usage}%</span>
          </div>
          <Progress value={localHealth.system.cpu_usage} className="h-2" />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <MemoryStick className="h-4 w-4 text-green-500" />
              <span>Memory Usage</span>
            </div>
            <span className="font-medium">{localHealth.system.memory_usage}%</span>
          </div>
          <Progress value={localHealth.system.memory_usage} className="h-2" />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <HardDrive className="h-4 w-4 text-purple-500" />
              <span>Disk Usage</span>
            </div>
            <span className="font-medium">{localHealth.system.disk_usage}%</span>
          </div>
          <Progress value={localHealth.system.disk_usage} className="h-2" />
        </div>
      </div>

      {/* Services Status */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium">Services</h4>
        <div className="space-y-2">
          {Object.entries(localHealth.services).map(([serviceName, serviceData]) => (
            <div key={serviceName} className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                {getStatusIcon(serviceData.status)}
                <span className="capitalize">{serviceName}</span>
              </div>
              <div className="flex items-center space-x-2">
                {serviceName === 'api' && (
                  <span className="text-xs text-muted-foreground">
                    {serviceData.response_time}ms
                  </span>
                )}
                {serviceName === 'database' && (
                  <span className="text-xs text-muted-foreground">
                    {serviceData.connections}/{serviceData.max_connections}
                  </span>
                )}
                {serviceName === 'redis' && (
                  <span className="text-xs text-muted-foreground">
                    {formatBytes(serviceData.memory_usage)}
                  </span>
                )}
                {serviceName === 'scraper' && serviceData.active_jobs > 0 && (
                  <span className="text-xs text-muted-foreground">
                    {serviceData.active_jobs} jobs
                  </span>
                )}
                {serviceName === 'generator' && serviceData.queue_size > 0 && (
                  <span className="text-xs text-muted-foreground">
                    {serviceData.queue_size} queued
                  </span>
                )}
                {serviceName === 'deployer' && serviceData.active_deployments > 0 && (
                  <span className="text-xs text-muted-foreground">
                    {serviceData.active_deployments} active
                  </span>
                )}
                <Badge 
                  variant={getStatusColor(serviceData.status) as any}
                  className="text-xs"
                >
                  {serviceData.status}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Uptime */}
      <div className="flex items-center justify-between text-sm pt-2 border-t">
        <span className="text-muted-foreground">Uptime</span>
        <span className="font-medium">{formatUptime(localHealth.system.uptime)}</span>
      </div>

      {/* Last Updated */}
      <div className="text-xs text-muted-foreground text-center">
        Last updated: {new Date(localHealth.last_updated).toLocaleTimeString()}
      </div>
    </div>
  )
}

'use client'

import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  RefreshCw, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  BarChart3
} from 'lucide-react'
import { trendsApi } from '@/lib/api'

interface TrendDataPoint {
  date: string
  created: number
  approved: number
  rejected: number
  total_volume: number
  avg_score: number
}

interface TrendsOverviewData {
  data: TrendDataPoint[]
  summary: {
    total_trends: number
    approved_trends: number
    rejection_rate: number
    avg_daily_creation: number
    trend_change: number
  }
  period: string
}

export function TrendsOverviewChart() {
  const [period, setPeriod] = useState<'7d' | '30d' | '90d'>('7d')
  const [chartType, setChartType] = useState<'line' | 'area'>('area')

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['trends-overview', period],
    queryFn: async () => {
      const response = await trendsApi.getOverview(period)
      return response.data as TrendsOverviewData
    },
    refetchInterval: 60000, // Refetch every minute
  })

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    if (period === '7d') {
      return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium mb-2">{formatDate(label)}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="capitalize">{entry.dataKey.replace('_', ' ')}:</span>
              <span className="font-medium">{formatNumber(entry.value)}</span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-muted-foreground">Loading trends data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-8 w-8 mx-auto mb-2 text-red-500" />
          <p className="text-sm text-muted-foreground mb-4">Failed to load trends data</p>
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  if (!data || !data.data || data.data.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-muted-foreground">No trends data available</p>
        </div>
      </div>
    )
  }

  const chartData = data.data.map(point => ({
    ...point,
    date: formatDate(point.date)
  }))

  return (
    <div className="space-y-4">
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant={period === '7d' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setPeriod('7d')}
          >
            7 Days
          </Button>
          <Button
            variant={period === '30d' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setPeriod('30d')}
          >
            30 Days
          </Button>
          <Button
            variant={period === '90d' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setPeriod('90d')}
          >
            90 Days
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant={chartType === 'area' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setChartType('area')}
          >
            Area
          </Button>
          <Button
            variant={chartType === 'line' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setChartType('line')}
          >
            Line
          </Button>
          <Button variant="ghost" size="sm" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {formatNumber(data.summary.total_trends)}
          </div>
          <div className="text-xs text-muted-foreground">Total Trends</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {formatNumber(data.summary.approved_trends)}
          </div>
          <div className="text-xs text-muted-foreground">Approved</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {data.summary.rejection_rate.toFixed(1)}%
          </div>
          <div className="text-xs text-muted-foreground">Rejection Rate</div>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1">
            <span className="text-2xl font-bold">
              {data.summary.avg_daily_creation.toFixed(1)}
            </span>
            {data.summary.trend_change > 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : data.summary.trend_change < 0 ? (
              <TrendingDown className="h-4 w-4 text-red-500" />
            ) : null}
          </div>
          <div className="text-xs text-muted-foreground">Daily Average</div>
        </div>
      </div>

      {/* Chart */}
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'area' ? (
            <AreaChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12 }}
                tickLine={false}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Area
                type="monotone"
                dataKey="created"
                stackId="1"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.6}
                name="Created"
              />
              <Area
                type="monotone"
                dataKey="approved"
                stackId="1"
                stroke="#10b981"
                fill="#10b981"
                fillOpacity={0.6}
                name="Approved"
              />
              <Area
                type="monotone"
                dataKey="rejected"
                stackId="1"
                stroke="#ef4444"
                fill="#ef4444"
                fillOpacity={0.6}
                name="Rejected"
              />
            </AreaChart>
          ) : (
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12 }}
                tickLine={false}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="created"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={{ r: 4 }}
                name="Created"
              />
              <Line
                type="monotone"
                dataKey="approved"
                stroke="#10b981"
                strokeWidth={2}
                dot={{ r: 4 }}
                name="Approved"
              />
              <Line
                type="monotone"
                dataKey="rejected"
                stroke="#ef4444"
                strokeWidth={2}
                dot={{ r: 4 }}
                name="Rejected"
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  BarChart3, 
  Settings, 
  Users, 
  Globe, 
  Activity, 
  FileText, 
  Rocket, 
  Menu, 
  X,
  Bell,
  Search,
  User,
  LogOut,
  ChevronDown,
  Home
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { hasPermission, PERMISSIONS } from '@/lib/auth'
import { NavItem } from '@/types'

interface DashboardLayoutProps {
  children: React.ReactNode
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner h-8 w-8"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const userPermissions = session.user.permissions || []

  // Navigation items based on permissions
  const navigationItems: NavItem[] = [
    {
      title: 'Dashboard',
      href: '/dashboard',
      icon: 'Home'
    },
    {
      title: 'Trends',
      href: '/dashboard/trends',
      icon: 'BarChart3',
      disabled: !hasPermission(userPermissions, PERMISSIONS.READ_TRENDS)
    },
    {
      title: 'Content',
      href: '/dashboard/content',
      icon: 'FileText',
      disabled: !hasPermission(userPermissions, PERMISSIONS.READ_CONTENT)
    },
    {
      title: 'Deployments',
      href: '/dashboard/deployments',
      icon: 'Rocket',
      disabled: !hasPermission(userPermissions, PERMISSIONS.READ_DEPLOYMENTS)
    },
    {
      title: 'DNS',
      href: '/dashboard/dns',
      icon: 'Globe',
      disabled: !hasPermission(userPermissions, PERMISSIONS.READ_DNS)
    },
    {
      title: 'Analytics',
      href: '/dashboard/analytics',
      icon: 'Activity',
      disabled: !hasPermission(userPermissions, PERMISSIONS.READ_ANALYTICS)
    },
    {
      title: 'Users',
      href: '/dashboard/users',
      icon: 'Users',
      disabled: !hasPermission(userPermissions, PERMISSIONS.READ_USERS)
    },
    {
      title: 'Settings',
      href: '/dashboard/settings',
      icon: 'Settings',
      disabled: !hasPermission(userPermissions, PERMISSIONS.READ_SYSTEM)
    }
  ].filter(item => !item.disabled)

  const getIcon = (iconName: string) => {
    const icons = {
      Home,
      BarChart3,
      FileText,
      Rocket,
      Globe,
      Activity,
      Users,
      Settings
    }
    const IconComponent = icons[iconName as keyof typeof icons]
    return IconComponent ? <IconComponent className="h-5 w-5" /> : null
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-border">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
              <BarChart3 className="h-5 w-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-bold gradient-text">TrendSite</span>
          </Link>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-md hover:bg-accent"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <ul className="space-y-1">
            {navigationItems.map((item) => (
              <li key={item.href}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    "hover:bg-accent hover:text-accent-foreground",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  )}
                  onClick={() => setSidebarOpen(false)}
                >
                  {getIcon(item.icon || '')}
                  <span className="ml-3">{item.title}</span>
                  {item.badge && (
                    <span className="ml-auto badge badge-secondary">
                      {item.badge}
                    </span>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* User info in sidebar */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-border">
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-primary-foreground" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {session.user.name}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {session.user.role}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top header */}
        <header className="bg-card border-b border-border">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md hover:bg-accent"
              >
                <Menu className="h-5 w-5" />
              </button>

              {/* Search bar */}
              <div className="hidden md:block ml-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="input pl-10 w-64"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="p-2 rounded-md hover:bg-accent relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-1 right-1 h-2 w-2 bg-error-500 rounded-full"></span>
              </button>

              {/* User menu */}
              <div className="relative">
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex items-center space-x-2 p-2 rounded-md hover:bg-accent"
                >
                  <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-primary-foreground" />
                  </div>
                  <span className="hidden md:block text-sm font-medium">
                    {session.user.name}
                  </span>
                  <ChevronDown className="h-4 w-4" />
                </button>

                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50">
                    <div className="py-1">
                      <Link
                        href="/dashboard/profile"
                        className="block px-4 py-2 text-sm hover:bg-accent"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        Profile
                      </Link>
                      <Link
                        href="/dashboard/settings"
                        className="block px-4 py-2 text-sm hover:bg-accent"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        Settings
                      </Link>
                      <hr className="my-1 border-border" />
                      <button
                        onClick={() => {
                          setUserMenuOpen(false)
                          // Handle logout
                        }}
                        className="block w-full text-left px-4 py-2 text-sm hover:bg-accent"
                      >
                        <LogOut className="inline h-4 w-4 mr-2" />
                        Sign out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

export default DashboardLayout

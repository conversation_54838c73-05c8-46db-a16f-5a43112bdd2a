'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { WebSocketMessage, RealTimeUpdate } from '@/types'

interface UseWebSocketOptions {
  reconnectAttempts?: number
  reconnectInterval?: number
  onOpen?: () => void
  onClose?: () => void
  onError?: (error: Event) => void
  onMessage?: (message: WebSocketMessage) => void
}

interface UseWebSocketReturn {
  isConnected: boolean
  isConnecting: boolean
  lastMessage: WebSocketMessage | null
  sendMessage: (message: any) => void
  disconnect: () => void
  reconnect: () => void
}

export const useWebSocket = (
  url: string,
  options: UseWebSocketOptions = {}
): UseWebSocketReturn => {
  const {
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    onOpen,
    onClose,
    onError,
    onMessage
  } = options

  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectCountRef = useRef(0)
  const shouldReconnectRef = useRef(true)

  const connect = useCallback(() => {
    if (typeof window === 'undefined') return
    
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    setIsConnecting(true)
    
    try {
      const ws = new WebSocket(url)
      wsRef.current = ws

      ws.onopen = () => {
        console.log('WebSocket connected to:', url)
        setIsConnected(true)
        setIsConnecting(false)
        reconnectCountRef.current = 0
        onOpen?.()
      }

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setIsConnected(false)
        setIsConnecting(false)
        onClose?.()

        // Attempt to reconnect if it wasn't a manual disconnect
        if (shouldReconnectRef.current && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++
          console.log(`Attempting to reconnect (${reconnectCountRef.current}/${reconnectAttempts})...`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectInterval)
        }
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        setIsConnecting(false)
        onError?.(error)
      }

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = {
            type: 'message',
            data: event.data,
            timestamp: new Date().toISOString()
          }
          
          setLastMessage(message)
          onMessage?.(message)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

    } catch (error) {
      console.error('Error creating WebSocket connection:', error)
      setIsConnecting(false)
    }
  }, [url, reconnectAttempts, reconnectInterval, onOpen, onClose, onError, onMessage])

  const disconnect = useCallback(() => {
    shouldReconnectRef.current = false
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect')
      wsRef.current = null
    }
    
    setIsConnected(false)
    setIsConnecting(false)
  }, [])

  const reconnect = useCallback(() => {
    disconnect()
    shouldReconnectRef.current = true
    reconnectCountRef.current = 0
    setTimeout(connect, 100)
  }, [disconnect, connect])

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        const messageString = typeof message === 'string' ? message : JSON.stringify(message)
        wsRef.current.send(messageString)
      } catch (error) {
        console.error('Error sending WebSocket message:', error)
      }
    } else {
      console.warn('WebSocket is not connected. Cannot send message.')
    }
  }, [])

  // Connect on mount
  useEffect(() => {
    connect()
    
    return () => {
      shouldReconnectRef.current = false
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (wsRef.current) {
        wsRef.current.close()
      }
    }
  }, [connect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    isConnected,
    isConnecting,
    lastMessage,
    sendMessage,
    disconnect,
    reconnect
  }
}

// Specialized hook for real-time updates
export const useRealTimeUpdates = (
  url: string = 'ws://localhost:8000/ws/updates'
) => {
  const [updates, setUpdates] = useState<RealTimeUpdate[]>([])
  
  const handleMessage = useCallback((message: WebSocketMessage) => {
    try {
      const update: RealTimeUpdate = JSON.parse(message.data)
      setUpdates(prev => [update, ...prev.slice(0, 99)]) // Keep last 100 updates
    } catch (error) {
      console.error('Error parsing real-time update:', error)
    }
  }, [])

  const { isConnected, sendMessage, disconnect, reconnect } = useWebSocket(url, {
    onMessage: handleMessage,
    reconnectAttempts: 10,
    reconnectInterval: 2000
  })

  const clearUpdates = useCallback(() => {
    setUpdates([])
  }, [])

  const getUpdatesByType = useCallback((type: string) => {
    return updates.filter(update => update.type === type)
  }, [updates])

  return {
    isConnected,
    updates,
    clearUpdates,
    getUpdatesByType,
    sendMessage,
    disconnect,
    reconnect
  }
}

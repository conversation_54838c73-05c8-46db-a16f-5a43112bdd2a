import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ApiResponse, PaginationParams } from '@/types'

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
const API_TIMEOUT = 30000 // 30 seconds

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    }
    
    // Add request timestamp
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log response time
    const endTime = new Date()
    const startTime = response.config.metadata?.startTime
    if (startTime) {
      const duration = endTime.getTime() - startTime.getTime()
      console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`)
    }
    
    return response
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token')
        window.location.href = '/auth/login'
      }
    } else if (error.response?.status === 403) {
      // Forbidden - show error message
      console.error('Access forbidden:', error.response.data)
    } else if (error.response?.status >= 500) {
      // Server error
      console.error('Server error:', error.response.data)
    }
    
    return Promise.reject(error)
  }
)

// Generic API methods
class ApiService {
  private client: AxiosInstance

  constructor(client: AxiosInstance) {
    this.client = client
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.get(url, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.post(url, data, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.put(url, data, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.patch(url, data, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.delete(url, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || error.response.data?.error || 'An error occurred'
      return new Error(message)
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - please check your connection')
    } else {
      // Something else happened
      return new Error(error.message || 'An unexpected error occurred')
    }
  }

  // Helper method for paginated requests
  async getPaginated<T>(
    url: string, 
    params: PaginationParams = {}
  ): Promise<ApiResponse<T[]>> {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.sortBy) queryParams.append('sort_by', params.sortBy)
    if (params.sortOrder) queryParams.append('sort_order', params.sortOrder)
    if (params.search) queryParams.append('search', params.search)
    
    // Add filters
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })
    }
    
    const fullUrl = queryParams.toString() ? `${url}?${queryParams.toString()}` : url
    return this.get<T[]>(fullUrl)
  }
}

// Create API service instance
export const api = new ApiService(apiClient)

// Specific API endpoints
export const trendsApi = {
  getAll: (params?: PaginationParams) => api.getPaginated('/trends', params),
  getById: (id: string) => api.get(`/trends/${id}`),
  create: (data: any) => api.post('/trends', data),
  update: (id: string, data: any) => api.put(`/trends/${id}`, data),
  delete: (id: string) => api.delete(`/trends/${id}`),
  approve: (id: string) => api.post(`/trends/${id}/approve`),
  reject: (id: string) => api.post(`/trends/${id}/reject`),
  getStats: () => api.get('/trends/stats'),
}

export const contentApi = {
  getAll: (params?: PaginationParams) => api.getPaginated('/content', params),
  getById: (id: string) => api.get(`/content/${id}`),
  getByTrendId: (trendId: string) => api.get(`/content/trend/${trendId}`),
  create: (data: any) => api.post('/content', data),
  update: (id: string, data: any) => api.put(`/content/${id}`, data),
  delete: (id: string) => api.delete(`/content/${id}`),
  regenerate: (id: string) => api.post(`/content/${id}/regenerate`),
  approve: (id: string) => api.post(`/content/${id}/approve`),
}

export const deploymentsApi = {
  getAll: (params?: PaginationParams) => api.getPaginated('/deployments', params),
  getById: (id: string) => api.get(`/deployments/${id}`),
  create: (data: any) => api.post('/deployments', data),
  cancel: (id: string) => api.post(`/deployments/${id}/cancel`),
  getLogs: (id: string) => api.get(`/deployments/${id}/logs`),
  getStats: () => api.get('/deployments/stats'),
  getActive: () => api.get('/deployments/active'),
}

export const dnsApi = {
  getAll: (params?: PaginationParams) => api.getPaginated('/dns', params),
  getById: (id: string) => api.get(`/dns/${id}`),
  create: (data: any) => api.post('/dns', data),
  update: (id: string, data: any) => api.put(`/dns/${id}`, data),
  delete: (id: string) => api.delete(`/dns/${id}`),
  sync: () => api.post('/dns/sync'),
  getStats: () => api.get('/dns/stats'),
}

export const analyticsApi = {
  getOverview: (timeRange?: string) => api.get(`/analytics/overview?time_range=${timeRange || '24h'}`),
  getTrendAnalytics: (trendId: string, timeRange?: string) => 
    api.get(`/analytics/trends/${trendId}?time_range=${timeRange || '24h'}`),
  getSystemMetrics: (timeRange?: string) => 
    api.get(`/analytics/system?time_range=${timeRange || '24h'}`),
  getTopTrends: (limit?: number, timeRange?: string) => 
    api.get(`/analytics/top-trends?limit=${limit || 10}&time_range=${timeRange || '24h'}`),
  exportData: (params: any) => api.post('/analytics/export', params),
}

export const adminApi = {
  getSystemHealth: () => api.get('/admin/health'),
  getSystemInfo: () => api.get('/admin/system-info'),
  getUsers: (params?: PaginationParams) => api.getPaginated('/admin/users', params),
  createUser: (data: any) => api.post('/admin/users', data),
  updateUser: (id: string, data: any) => api.put(`/admin/users/${id}`, data),
  deleteUser: (id: string) => api.delete(`/admin/users/${id}`),
  getTasks: () => api.get('/admin/tasks'),
  runTask: (taskName: string) => api.post(`/admin/tasks/${taskName}/run`),
  getTaskStatus: (taskName: string) => api.get(`/admin/tasks/${taskName}/status`),
  updateTaskConfig: (taskName: string, config: any) => 
    api.put(`/admin/tasks/${taskName}/config`, config),
  getLogs: (params?: PaginationParams) => api.getPaginated('/admin/logs', params),
  clearCache: () => api.post('/admin/cache/clear'),
  triggerMaintenance: () => api.post('/admin/maintenance'),
}

// WebSocket connection helper
export const createWebSocketConnection = (url: string = 'ws://localhost:8000/ws') => {
  if (typeof window === 'undefined') {
    return null
  }
  
  const ws = new WebSocket(url)
  
  ws.onopen = () => {
    console.log('WebSocket connected')
  }
  
  ws.onclose = () => {
    console.log('WebSocket disconnected')
  }
  
  ws.onerror = (error) => {
    console.error('WebSocket error:', error)
  }
  
  return ws
}

// Export the axios instance for custom requests
export { apiClient }
export default api

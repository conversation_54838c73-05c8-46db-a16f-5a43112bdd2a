import { NextAuthOptions } from 'next-auth'
import Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import { api } from './api'

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Call backend authentication endpoint
          const response = await api.post('/auth/login', {
            email: credentials.email,
            password: credentials.password
          })

          if (response.success && response.data) {
            const { user, token } = response.data
            
            return {
              id: user.id,
              email: user.email,
              name: user.profile_data?.name || user.email,
              role: user.role,
              permissions: user.permissions,
              accessToken: token,
              image: user.profile_data?.avatar || null
            }
          }
          
          return null
        } catch (error) {
          console.error('Authentication error:', error)
          return null
        }
      }
    })
  ],
  
  callbacks: {
    async jwt({ token, user }) {
      // Persist user data in JWT token
      if (user) {
        token.role = user.role
        token.permissions = user.permissions
        token.accessToken = user.accessToken
      }
      return token
    },
    
    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
        session.user.permissions = token.permissions as string[]
        session.accessToken = token.accessToken as string
      }
      return session
    },
    
    async redirect({ url, baseUrl }) {
      // Redirect to dashboard after login
      if (url.startsWith('/')) return `${baseUrl}${url}`
      else if (new URL(url).origin === baseUrl) return url
      return `${baseUrl}/dashboard`
    }
  },
  
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
  },
  
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },
  
  secret: process.env.NEXTAUTH_SECRET,
  
  debug: process.env.NODE_ENV === 'development',
}

// Permission checking utilities
export const hasPermission = (userPermissions: string[], requiredPermission: string): boolean => {
  return userPermissions.includes(requiredPermission) || userPermissions.includes('*')
}

export const hasRole = (userRole: string, requiredRoles: string[]): boolean => {
  return requiredRoles.includes(userRole) || userRole === 'admin'
}

export const canAccessResource = (
  userRole: string, 
  userPermissions: string[], 
  resource: string, 
  action: string
): boolean => {
  // Admin can access everything
  if (userRole === 'admin') return true
  
  // Check specific permission
  const permission = `${action}_${resource}`
  return hasPermission(userPermissions, permission)
}

// Role-based access control
export const ROLES = {
  ADMIN: 'admin',
  EDITOR: 'editor',
  VIEWER: 'viewer'
} as const

export const PERMISSIONS = {
  // Trends
  READ_TRENDS: 'read_trends',
  WRITE_TRENDS: 'write_trends',
  APPROVE_TRENDS: 'approve_trends',
  
  // Content
  READ_CONTENT: 'read_content',
  WRITE_CONTENT: 'write_content',
  REGENERATE_CONTENT: 'regenerate_content',
  
  // Deployments
  READ_DEPLOYMENTS: 'read_deployments',
  TRIGGER_DEPLOYMENTS: 'trigger_deployments',
  CANCEL_DEPLOYMENTS: 'cancel_deployments',
  
  // DNS
  READ_DNS: 'read_dns',
  MANAGE_DNS: 'manage_dns',
  
  // Analytics
  READ_ANALYTICS: 'read_analytics',
  EXPORT_ANALYTICS: 'export_analytics',
  
  // Users
  READ_USERS: 'read_users',
  MANAGE_USERS: 'manage_users',
  
  // System
  READ_SYSTEM: 'read_system',
  MANAGE_SYSTEM: 'manage_system',
  TRIGGER_MAINTENANCE: 'trigger_maintenance'
} as const

// Default permissions by role
export const DEFAULT_PERMISSIONS = {
  [ROLES.ADMIN]: Object.values(PERMISSIONS),
  [ROLES.EDITOR]: [
    PERMISSIONS.READ_TRENDS,
    PERMISSIONS.WRITE_TRENDS,
    PERMISSIONS.READ_CONTENT,
    PERMISSIONS.WRITE_CONTENT,
    PERMISSIONS.REGENERATE_CONTENT,
    PERMISSIONS.READ_DEPLOYMENTS,
    PERMISSIONS.TRIGGER_DEPLOYMENTS,
    PERMISSIONS.READ_DNS,
    PERMISSIONS.READ_ANALYTICS
  ],
  [ROLES.VIEWER]: [
    PERMISSIONS.READ_TRENDS,
    PERMISSIONS.READ_CONTENT,
    PERMISSIONS.READ_DEPLOYMENTS,
    PERMISSIONS.READ_DNS,
    PERMISSIONS.READ_ANALYTICS
  ]
}

// Type extensions for NextAuth
declare module 'next-auth' {
  interface User {
    role: string
    permissions: string[]
    accessToken: string
  }
  
  interface Session {
    user: {
      id: string
      email: string
      name: string
      image?: string
      role: string
      permissions: string[]
    }
    accessToken: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: string
    permissions: string[]
    accessToken: string
  }
}

// Core types for TrendSite Dashboard
export interface User {
  id: string
  email: string
  role: 'admin' | 'editor' | 'viewer'
  permissions: string[]
  isActive: boolean
  lastLogin?: string
  profileData: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface Trend {
  id: string
  keyword: string
  slug: string
  status: 'pending' | 'approved' | 'rejected' | 'processing'
  searchVolume?: number
  growthRate?: number
  region: string
  category?: string
  source: string
  confidenceScore: number
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
  approvedAt?: string
  approvedBy?: string
  rejectedAt?: string
  rejectedBy?: string
}

export interface Content {
  id: string
  trendId: string
  title: string
  description: string
  content: string
  status: 'draft' | 'generated' | 'approved' | 'published'
  contentType: 'article' | 'landing_page' | 'blog_post'
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
  createdBy?: string
  updatedBy?: string
}

export interface Deployment {
  id: string
  trendId: string
  contentId?: string
  status: 'pending' | 'building' | 'deploying' | 'completed' | 'failed' | 'cancelled'
  provider: string
  deployUrl?: string
  buildLogs?: string
  errorMessage?: string
  metadata: Record<string, any>
  startedAt: string
  completedAt?: string
  triggeredBy?: string
}

export interface DNSRecord {
  id: string
  trendId: string
  subdomain: string
  recordType: string
  recordValue: string
  status: 'active' | 'inactive' | 'deleted'
  cloudflareRecordId?: string
  ttl: number
  proxied: boolean
  dnsMetadata: Record<string, any>
  createdAt: string
  updatedAt: string
  createdBy?: string
}

export interface Analytics {
  id: string
  eventType: 'page_view' | 'user_interaction' | 'system_event' | 'performance_metric' | 'error_event'
  trendId?: string
  userId?: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
  referrer?: string
  pageUrl?: string
  eventData: Record<string, any>
  metrics: Record<string, number>
  timestamp: string
  createdAt: string
}

export interface SystemLog {
  id: string
  level: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL'
  category: 'application' | 'security' | 'performance' | 'audit' | 'system' | 'api' | 'database' | 'celery'
  module: string
  message: string
  correlationId?: string
  userId?: string
  sessionId?: string
  ipAddress?: string
  requestId?: string
  taskId?: string
  metadata: Record<string, any>
  stackTrace?: string
  timestamp: string
  createdAt: string
}

// Dashboard-specific types
export interface DashboardStats {
  totalTrends: number
  activeTrends: number
  totalDeployments: number
  activeDeployments: number
  totalDNSRecords: number
  activeDNSRecords: number
  systemHealth: 'healthy' | 'degraded' | 'unhealthy'
  lastUpdated: string
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  components: {
    database: ComponentHealth
    redis: ComponentHealth
    celery: ComponentHealth
    scraper: ComponentHealth
    dns: ComponentHealth
    monitoring: ComponentHealth
  }
  lastCheck: string
}

export interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown'
  responseTime?: number
  errorRate?: number
  lastCheck: string
  message?: string
}

export interface TaskExecution {
  id: string
  taskName: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  itemsProcessed: number
  itemsCleaned: number
  bytesFreed: number
  executionTime: number
  errorMessage?: string
  metadata: Record<string, any>
  startedAt: string
  completedAt?: string
}

export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
    fill?: boolean
  }[]
}

export interface MetricCard {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon?: string
  color?: 'primary' | 'success' | 'warning' | 'error'
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  pagination?: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}

// WebSocket types
export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
}

export interface RealTimeUpdate {
  type: 'trend_update' | 'deployment_update' | 'system_health' | 'task_update' | 'analytics_update'
  data: any
  timestamp: string
}

// Form types
export interface TrendFormData {
  keyword: string
  region: string
  category?: string
  priority?: 'low' | 'medium' | 'high'
}

export interface DeploymentFormData {
  trendId: string
  provider: string
  environment: 'development' | 'staging' | 'production'
  autoApprove?: boolean
}

export interface TaskFormData {
  taskName: string
  schedule?: string
  enabled: boolean
  config: Record<string, any>
}

// Filter and search types
export interface FilterOption {
  label: string
  value: string
  count?: number
}

export interface SearchFilters {
  status?: string[]
  dateRange?: {
    start: string
    end: string
  }
  category?: string[]
  source?: string[]
  region?: string[]
}

// Navigation types
export interface NavItem {
  title: string
  href: string
  icon?: string
  badge?: string | number
  children?: NavItem[]
  disabled?: boolean
}

export interface BreadcrumbItem {
  title: string
  href?: string
}

// Theme types
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system'
  primaryColor: string
  radius: number
}

// Error types
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: string
}

// Utility types
export type Status = 'idle' | 'loading' | 'success' | 'error'

export type SortDirection = 'asc' | 'desc'

export type TimeRange = '1h' | '24h' | '7d' | '30d' | '90d' | 'custom'

export type RefreshInterval = 'off' | '5s' | '30s' | '1m' | '5m' | '15m'

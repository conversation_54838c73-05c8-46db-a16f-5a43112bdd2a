"""
Database connection management for the Trend Platform
Handles connection pooling, transactions, and database operations
"""
import asyncio
import asyncpg
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager
import logging
from shared.config import settings, DATABASE_CONFIG
from shared.exceptions import DatabaseError

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize database connection pool"""
        if self._initialized:
            return
        
        try:
            self.pool = await asyncpg.create_pool(
                settings.database_url,
                min_size=DATABASE_CONFIG['connection']['min_connections'],
                max_size=DATABASE_CONFIG['connection']['max_connections'],
                command_timeout=DATABASE_CONFIG['connection']['command_timeout'],
                server_settings={
                    'jit': 'off'  # Disable JIT for better performance with short queries
                }
            )
            self._initialized = True
            logger.info("Database connection pool initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database connection pool: {str(e)}")
            raise DatabaseError(f"Database initialization failed: {str(e)}")
    
    async def close(self):
        """Close database connection pool"""
        if self.pool:
            await self.pool.close()
            self._initialized = False
            logger.info("Database connection pool closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection from pool"""
        if not self.pool:
            raise DatabaseError("Database pool not initialized")
        
        async with self.pool.acquire() as connection:
            yield connection
    
    @asynccontextmanager
    async def transaction(self):
        """Get database transaction"""
        async with self.get_connection() as conn:
            async with conn.transaction():
                yield conn
    
    async def execute(self, query: str, *args) -> str:
        """Execute a query and return the result"""
        try:
            async with self.get_connection() as conn:
                return await conn.execute(query, *args)
        except Exception as e:
            logger.error(f"Database execute error: {str(e)}")
            raise DatabaseError(f"Query execution failed: {str(e)}")
    
    async def fetch(self, query: str, *args) -> List[Dict[str, Any]]:
        """Fetch multiple rows"""
        try:
            async with self.get_connection() as conn:
                rows = await conn.fetch(query, *args)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Database fetch error: {str(e)}")
            raise DatabaseError(f"Query fetch failed: {str(e)}")
    
    async def fetchrow(self, query: str, *args) -> Optional[Dict[str, Any]]:
        """Fetch single row"""
        try:
            async with self.get_connection() as conn:
                row = await conn.fetchrow(query, *args)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Database fetchrow error: {str(e)}")
            raise DatabaseError(f"Query fetchrow failed: {str(e)}")
    
    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        try:
            async with self.get_connection() as conn:
                return await conn.fetchval(query, *args)
        except Exception as e:
            logger.error(f"Database fetchval error: {str(e)}")
            raise DatabaseError(f"Query fetchval failed: {str(e)}")
    
    async def executemany(self, query: str, args_list: List[tuple]) -> None:
        """Execute query multiple times with different parameters"""
        try:
            async with self.get_connection() as conn:
                await conn.executemany(query, args_list)
        except Exception as e:
            logger.error(f"Database executemany error: {str(e)}")
            raise DatabaseError(f"Batch query execution failed: {str(e)}")
    
    async def health_check(self) -> bool:
        """Check database health"""
        try:
            result = await self.fetchval("SELECT 1")
            return result == 1
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get database connection pool statistics"""
        if not self.pool:
            return {}
        
        return {
            'size': self.pool.get_size(),
            'min_size': self.pool.get_min_size(),
            'max_size': self.pool.get_max_size(),
            'idle_size': self.pool.get_idle_size(),
        }


# Global database manager instance
db_manager = DatabaseManager()


async def get_db_manager() -> DatabaseManager:
    """Get database manager instance"""
    if not db_manager._initialized:
        await db_manager.initialize()
    return db_manager


async def init_database():
    """Initialize database connection"""
    await db_manager.initialize()


async def close_database():
    """Close database connection"""
    await db_manager.close()


# Dependency for FastAPI
async def get_database():
    """FastAPI dependency for database access"""
    return await get_db_manager()

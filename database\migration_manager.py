"""
Database migration manager for schema versioning and updates
Handles database schema migrations, rollbacks, and version tracking
"""
import os
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from database.connection import get_db_manager
from monitoring.logger import get_logger
from shared.exceptions import DatabaseError

logger = get_logger('database.migration_manager')


class Migration:
    """Represents a single database migration"""
    
    def __init__(self, version: str, name: str, file_path: str):
        self.version = version
        self.name = name
        self.file_path = file_path
        self.applied_at: Optional[datetime] = None
    
    def __str__(self):
        return f"Migration {self.version}: {self.name}"
    
    def __repr__(self):
        return f"Migration(version='{self.version}', name='{self.name}')"


class MigrationManager:
    """Manages database schema migrations"""
    
    def __init__(self, migrations_dir: str = "database/migrations"):
        self.migrations_dir = Path(migrations_dir)
        self.logger = logger
        
        # Ensure migrations directory exists
        self.migrations_dir.mkdir(parents=True, exist_ok=True)
    
    async def initialize_migration_table(self):
        """Create the schema_migrations table if it doesn't exist"""
        db = await get_db_manager()
        
        create_table_sql = """
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version VARCHAR(255) PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                checksum VARCHAR(64),
                execution_time_ms INTEGER
            );
            
            CREATE INDEX IF NOT EXISTS idx_schema_migrations_applied_at 
            ON schema_migrations(applied_at);
        """
        
        try:
            await db.execute(create_table_sql)
            self.logger.info("Migration table initialized")
        except Exception as e:
            raise DatabaseError(f"Failed to initialize migration table: {str(e)}")
    
    def discover_migrations(self) -> List[Migration]:
        """Discover all migration files in the migrations directory"""
        migrations = []
        
        # Pattern to match migration files: 001_migration_name.sql
        pattern = re.compile(r'^(\d{3})_(.+)\.sql$')
        
        for file_path in self.migrations_dir.glob("*.sql"):
            match = pattern.match(file_path.name)
            if match:
                version = match.group(1)
                name = match.group(2).replace('_', ' ').title()
                
                migration = Migration(
                    version=version,
                    name=name,
                    file_path=str(file_path)
                )
                migrations.append(migration)
        
        # Sort by version
        migrations.sort(key=lambda m: m.version)
        
        self.logger.info(f"Discovered {len(migrations)} migration files")
        return migrations
    
    async def get_applied_migrations(self) -> List[str]:
        """Get list of applied migration versions"""
        db = await get_db_manager()
        
        query = "SELECT version FROM schema_migrations ORDER BY version"
        
        try:
            rows = await db.fetch(query)
            return [row['version'] for row in rows]
        except Exception as e:
            # If table doesn't exist, return empty list
            if "does not exist" in str(e).lower():
                return []
            raise DatabaseError(f"Failed to get applied migrations: {str(e)}")
    
    async def get_pending_migrations(self) -> List[Migration]:
        """Get list of migrations that haven't been applied yet"""
        all_migrations = self.discover_migrations()
        applied_versions = await self.get_applied_migrations()
        
        pending = [m for m in all_migrations if m.version not in applied_versions]
        
        self.logger.info(f"Found {len(pending)} pending migrations")
        return pending
    
    async def apply_migration(self, migration: Migration) -> bool:
        """Apply a single migration"""
        db = await get_db_manager()
        
        self.logger.info(f"Applying migration: {migration}")
        
        try:
            # Read migration file
            with open(migration.file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # Calculate checksum
            import hashlib
            checksum = hashlib.sha256(sql_content.encode()).hexdigest()
            
            # Execute migration in a transaction
            start_time = datetime.now()
            
            async with db.transaction():
                # Execute the migration SQL
                await db.execute(sql_content)
                
                # Record the migration
                execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
                
                await db.execute("""
                    INSERT INTO schema_migrations (version, name, applied_at, checksum, execution_time_ms)
                    VALUES ($1, $2, NOW(), $3, $4)
                """, migration.version, migration.name, checksum, execution_time)
            
            self.logger.info(f"Successfully applied migration {migration.version} in {execution_time}ms")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to apply migration {migration.version}: {str(e)}")
            raise DatabaseError(f"Migration {migration.version} failed: {str(e)}")
    
    async def rollback_migration(self, version: str) -> bool:
        """Rollback a specific migration (if rollback file exists)"""
        db = await get_db_manager()
        
        # Look for rollback file
        rollback_file = self.migrations_dir / f"{version}_rollback.sql"
        
        if not rollback_file.exists():
            raise DatabaseError(f"No rollback file found for migration {version}")
        
        self.logger.info(f"Rolling back migration: {version}")
        
        try:
            # Read rollback file
            with open(rollback_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # Execute rollback in a transaction
            async with db.transaction():
                # Execute the rollback SQL
                await db.execute(sql_content)
                
                # Remove migration record
                await db.execute(
                    "DELETE FROM schema_migrations WHERE version = $1",
                    version
                )
            
            self.logger.info(f"Successfully rolled back migration {version}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to rollback migration {version}: {str(e)}")
            raise DatabaseError(f"Rollback {version} failed: {str(e)}")
    
    async def migrate_to_latest(self) -> Dict[str, Any]:
        """Apply all pending migrations"""
        await self.initialize_migration_table()
        
        pending_migrations = await self.get_pending_migrations()
        
        if not pending_migrations:
            self.logger.info("No pending migrations to apply")
            return {
                'applied_count': 0,
                'migrations': [],
                'status': 'up_to_date'
            }
        
        applied_migrations = []
        failed_migrations = []
        
        for migration in pending_migrations:
            try:
                await self.apply_migration(migration)
                applied_migrations.append(migration.version)
            except Exception as e:
                failed_migrations.append({
                    'version': migration.version,
                    'error': str(e)
                })
                # Stop on first failure
                break
        
        result = {
            'applied_count': len(applied_migrations),
            'migrations': applied_migrations,
            'failed_migrations': failed_migrations,
            'status': 'completed' if not failed_migrations else 'failed'
        }
        
        if failed_migrations:
            self.logger.error(f"Migration failed at version {failed_migrations[0]['version']}")
        else:
            self.logger.info(f"Successfully applied {len(applied_migrations)} migrations")
        
        return result
    
    async def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status"""
        try:
            all_migrations = self.discover_migrations()
            applied_versions = await self.get_applied_migrations()
            pending_migrations = await self.get_pending_migrations()
            
            # Get latest applied migration info
            latest_applied = None
            if applied_versions:
                db = await get_db_manager()
                query = """
                    SELECT version, name, applied_at, execution_time_ms
                    FROM schema_migrations 
                    ORDER BY version DESC 
                    LIMIT 1
                """
                row = await db.fetchrow(query)
                if row:
                    latest_applied = dict(row)
            
            return {
                'total_migrations': len(all_migrations),
                'applied_count': len(applied_versions),
                'pending_count': len(pending_migrations),
                'latest_applied': latest_applied,
                'pending_migrations': [
                    {'version': m.version, 'name': m.name} 
                    for m in pending_migrations
                ],
                'status': 'up_to_date' if not pending_migrations else 'pending_migrations'
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get migration status: {str(e)}")
            raise DatabaseError(f"Failed to get migration status: {str(e)}")
    
    async def validate_migrations(self) -> Dict[str, Any]:
        """Validate migration files and applied migrations"""
        issues = []
        
        try:
            # Check for missing migration files
            applied_versions = await self.get_applied_migrations()
            all_migrations = self.discover_migrations()
            migration_versions = [m.version for m in all_migrations]
            
            for version in applied_versions:
                if version not in migration_versions:
                    issues.append(f"Applied migration {version} has no corresponding file")
            
            # Check for duplicate versions
            versions = [m.version for m in all_migrations]
            duplicates = [v for v in set(versions) if versions.count(v) > 1]
            for duplicate in duplicates:
                issues.append(f"Duplicate migration version: {duplicate}")
            
            # Check for gaps in version sequence
            if migration_versions:
                expected_versions = [f"{i:03d}" for i in range(1, len(migration_versions) + 1)]
                if migration_versions != expected_versions:
                    issues.append("Migration versions are not sequential")
            
            # Validate applied migration checksums
            db = await get_db_manager()
            for migration in all_migrations:
                if migration.version in applied_versions:
                    # Read current file content
                    with open(migration.file_path, 'r', encoding='utf-8') as f:
                        current_content = f.read()
                    
                    import hashlib
                    current_checksum = hashlib.sha256(current_content.encode()).hexdigest()
                    
                    # Get stored checksum
                    query = "SELECT checksum FROM schema_migrations WHERE version = $1"
                    row = await db.fetchrow(query, migration.version)
                    
                    if row and row['checksum'] and row['checksum'] != current_checksum:
                        issues.append(f"Migration {migration.version} file has been modified after application")
            
            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'total_migrations': len(all_migrations),
                'applied_migrations': len(applied_versions)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to validate migrations: {str(e)}")
            raise DatabaseError(f"Failed to validate migrations: {str(e)}")


# Global migration manager instance
migration_manager = MigrationManager()

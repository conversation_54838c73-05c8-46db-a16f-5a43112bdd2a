-- Initial database schema for Trend Platform
-- Migration: 001_initial_schema
-- Created: 2024-01-01

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- User profiles table (extends Supabase auth.users)
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    role TEXT CHECK (role IN ('admin', 'editor', 'viewer')) DEFAULT 'viewer',
    permissions TEXT[] DEFAULT '{}',
    full_name TEXT,
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trends table
CREATE TABLE trends (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'live', 'expired')) DEFAULT 'pending',
    region TEXT NOT NULL,
    category TEXT NOT NULL,
    search_volume INTEGER,
    growth_rate DECIMAL(5,2),
    score DECIMAL(5,2),
    source TEXT NOT NULL,
    raw_data JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expire_at TIMESTAMP WITH TIME ZONE,
    deployed_at TIMESTAMP WITH TIME ZONE,
    ads_enabled BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    approved_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    CONSTRAINT trends_keyword_region_unique UNIQUE(keyword, region),
    CONSTRAINT trends_score_range CHECK (score >= 0 AND score <= 100),
    CONSTRAINT trends_growth_rate_range CHECK (growth_rate >= -100 AND growth_rate <= 1000)
);

-- Content table
CREATE TABLE content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trend_id UUID REFERENCES trends(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    body TEXT NOT NULL,
    meta_tags JSONB DEFAULT '{}',
    hero_image_url TEXT,
    hero_image_alt TEXT,
    code_snippet TEXT,
    code_language TEXT,
    word_count INTEGER,
    readability_score DECIMAL(3,1),
    ai_model_used TEXT,
    generation_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    CONSTRAINT content_word_count_positive CHECK (word_count > 0),
    CONSTRAINT content_readability_range CHECK (readability_score >= 0 AND readability_score <= 100)
);

-- Deployments table
CREATE TABLE deployments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trend_id UUID REFERENCES trends(id),
    content_id UUID REFERENCES content(id),
    status TEXT CHECK (status IN ('pending', 'building', 'success', 'failed', 'cancelled')) DEFAULT 'pending',
    coolify_deployment_uuid TEXT,
    build_log TEXT,
    deploy_url TEXT,
    error_message TEXT,
    progress INTEGER DEFAULT 0,
    build_duration INTEGER, -- seconds
    deployment_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deployed_at TIMESTAMP WITH TIME ZONE,
    triggered_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    CONSTRAINT deployments_progress_range CHECK (progress >= 0 AND progress <= 100)
);

-- DNS records table
CREATE TABLE dns_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trend_id UUID REFERENCES trends(id),
    subdomain TEXT NOT NULL,
    target_url TEXT NOT NULL,
    cloudflare_record_id TEXT,
    status TEXT CHECK (status IN ('pending', 'active', 'updating', 'deleted', 'error')) DEFAULT 'pending',
    dns_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT dns_records_subdomain_unique UNIQUE(subdomain)
);

-- Analytics table
CREATE TABLE analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trend_id UUID REFERENCES trends(id),
    event_type TEXT NOT NULL, -- 'page_view', 'redirect', 'error', etc.
    event_data JSONB DEFAULT '{}',
    user_agent TEXT,
    ip_address INET,
    country TEXT,
    city TEXT,
    referrer TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System logs table
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    level TEXT CHECK (level IN ('debug', 'info', 'warning', 'error', 'critical')) NOT NULL,
    module TEXT NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    correlation_id TEXT,
    user_id UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API keys table (for external service management)
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_name TEXT NOT NULL,
    key_name TEXT NOT NULL,
    encrypted_key TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    CONSTRAINT api_keys_service_key_unique UNIQUE(service_name, key_name)
);

-- Scheduled tasks table (for Celery Beat)
CREATE TABLE scheduled_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    task TEXT NOT NULL,
    crontab_schedule TEXT,
    interval_schedule INTEGER, -- seconds
    is_enabled BOOLEAN DEFAULT true,
    last_run_at TIMESTAMP WITH TIME ZONE,
    next_run_at TIMESTAMP WITH TIME ZONE,
    total_run_count INTEGER DEFAULT 0,
    kwargs JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_trends_status ON trends(status);
CREATE INDEX idx_trends_region ON trends(region);
CREATE INDEX idx_trends_category ON trends(category);
CREATE INDEX idx_trends_created_at ON trends(created_at);
CREATE INDEX idx_trends_score ON trends(score DESC);
CREATE INDEX idx_trends_keyword_trgm ON trends USING gin(keyword gin_trgm_ops);

CREATE INDEX idx_content_trend_id ON content(trend_id);
CREATE INDEX idx_content_created_at ON content(created_at);

CREATE INDEX idx_deployments_trend_id ON deployments(trend_id);
CREATE INDEX idx_deployments_status ON deployments(status);
CREATE INDEX idx_deployments_created_at ON deployments(created_at);

CREATE INDEX idx_dns_records_trend_id ON dns_records(trend_id);
CREATE INDEX idx_dns_records_status ON dns_records(status);
CREATE INDEX idx_dns_records_subdomain ON dns_records(subdomain);

CREATE INDEX idx_analytics_trend_id ON analytics(trend_id);
CREATE INDEX idx_analytics_event_type ON analytics(event_type);
CREATE INDEX idx_analytics_created_at ON analytics(created_at);
CREATE INDEX idx_analytics_ip_address ON analytics(ip_address);

CREATE INDEX idx_system_logs_level ON system_logs(level);
CREATE INDEX idx_system_logs_module ON system_logs(module);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);

CREATE INDEX idx_api_keys_service_name ON api_keys(service_name);
CREATE INDEX idx_api_keys_is_active ON api_keys(is_active);

CREATE INDEX idx_scheduled_tasks_is_enabled ON scheduled_tasks(is_enabled);
CREATE INDEX idx_scheduled_tasks_next_run_at ON scheduled_tasks(next_run_at);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trends_updated_at BEFORE UPDATE ON trends FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_updated_at BEFORE UPDATE ON content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dns_records_updated_at BEFORE UPDATE ON dns_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_scheduled_tasks_updated_at BEFORE UPDATE ON scheduled_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for common queries
CREATE VIEW active_trends AS
SELECT 
    t.*,
    c.title as content_title,
    c.description as content_description,
    d.deploy_url,
    dr.subdomain
FROM trends t
LEFT JOIN content c ON t.id = c.trend_id
LEFT JOIN deployments d ON t.id = d.trend_id AND d.status = 'success'
LEFT JOIN dns_records dr ON t.id = dr.trend_id AND dr.status = 'active'
WHERE t.status = 'live' 
AND (t.expire_at IS NULL OR t.expire_at > NOW());

CREATE VIEW trend_analytics_summary AS
SELECT 
    t.id,
    t.keyword,
    t.category,
    t.region,
    COUNT(a.id) as total_views,
    COUNT(DISTINCT a.ip_address) as unique_visitors,
    COUNT(CASE WHEN a.event_type = 'page_view' THEN 1 END) as page_views,
    COUNT(CASE WHEN a.event_type = 'redirect' THEN 1 END) as redirects,
    MAX(a.created_at) as last_activity
FROM trends t
LEFT JOIN analytics a ON t.id = a.trend_id
WHERE t.status = 'live'
GROUP BY t.id, t.keyword, t.category, t.region;

-- Insert default scheduled tasks
INSERT INTO scheduled_tasks (name, task, crontab_schedule, is_enabled) VALUES
('scrape-trends', 'scraper.tasks.scrape_trends', '*/15 * * * *', true),
('cleanup-expired-content', 'housekeeping.tasks.cleanup_expired_content', '0 2 * * *', true),
('sync-analytics', 'analytics.tasks.sync_metrics', '0 * * * *', true),
('rotate-api-keys', 'security.tasks.rotate_keys', '0 3 1 * *', true),
('database-maintenance', 'housekeeping.tasks.database_maintenance', '0 1 * * 0', true);

-- Record migration
INSERT INTO schema_migrations (version, applied_at) VALUES ('001_initial_schema', NOW());

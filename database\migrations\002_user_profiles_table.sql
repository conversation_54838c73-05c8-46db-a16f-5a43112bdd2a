-- Migration 002: Create user_profiles table
-- Add user management and authentication support

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'viewer' CHECK (role IN ('admin', 'editor', 'viewer')),
    permissions TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    profile_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for user_profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_is_active ON user_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_user_profiles_last_login ON user_profiles(last_login);
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON user_profiles(created_at);

-- Create updated_at trigger for user_profiles
CREATE OR REPLACE FUNCTION update_user_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_user_profiles_updated_at();

-- Insert default admin user (password should be set via application)
INSERT INTO user_profiles (email, role, permissions, is_active, profile_data) VALUES
('<EMAIL>', 'admin', ARRAY[
    'read_trends', 'write_trends', 'approve_trends',
    'read_content', 'write_content', 'regenerate_content',
    'read_deployments', 'trigger_deployments', 'cancel_deployments',
    'read_dns', 'manage_dns',
    'read_analytics', 'export_analytics',
    'read_users', 'manage_users',
    'read_system', 'manage_system', 'trigger_maintenance'
], true, '{"name": "System Administrator", "created_by": "system"}')
ON CONFLICT (email) DO NOTHING;

-- Row Level Security (RLS) policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Policy: Users can read their own profile
CREATE POLICY user_profiles_read_own ON user_profiles
    FOR SELECT
    USING (id = current_setting('app.current_user_id')::uuid);

-- Policy: Admins can read all profiles
CREATE POLICY user_profiles_admin_read_all ON user_profiles
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.id = current_setting('app.current_user_id')::uuid 
            AND up.role = 'admin'
        )
    );

-- Policy: Users can update their own profile (limited fields)
CREATE POLICY user_profiles_update_own ON user_profiles
    FOR UPDATE
    USING (id = current_setting('app.current_user_id')::uuid)
    WITH CHECK (
        id = current_setting('app.current_user_id')::uuid
        AND role = OLD.role  -- Cannot change own role
        AND permissions = OLD.permissions  -- Cannot change own permissions
    );

-- Policy: Admins can update all profiles
CREATE POLICY user_profiles_admin_update_all ON user_profiles
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.id = current_setting('app.current_user_id')::uuid 
            AND up.role = 'admin'
        )
    );

-- Policy: Admins can insert new profiles
CREATE POLICY user_profiles_admin_insert ON user_profiles
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.id = current_setting('app.current_user_id')::uuid 
            AND up.role = 'admin'
        )
    );

-- Policy: Admins can delete profiles (soft delete via is_active)
CREATE POLICY user_profiles_admin_delete ON user_profiles
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.id = current_setting('app.current_user_id')::uuid 
            AND up.role = 'admin'
        )
    );

-- Create function to check user permissions
CREATE OR REPLACE FUNCTION user_has_permission(user_id UUID, permission_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    user_permissions TEXT[];
BEGIN
    SELECT role, permissions INTO user_role, user_permissions
    FROM user_profiles
    WHERE id = user_id AND is_active = true;
    
    -- Admin has all permissions
    IF user_role = 'admin' THEN
        RETURN true;
    END IF;
    
    -- Check if permission is in user's permissions array
    RETURN permission_name = ANY(user_permissions);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM user_profiles
    WHERE id = user_id AND is_active = true;
    
    RETURN COALESCE(user_role, 'viewer');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add user tracking to existing tables
ALTER TABLE trends ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES user_profiles(id);
ALTER TABLE trends ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES user_profiles(id);
ALTER TABLE trends ADD COLUMN IF NOT EXISTS rejected_by UUID REFERENCES user_profiles(id);

ALTER TABLE content ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES user_profiles(id);
ALTER TABLE content ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES user_profiles(id);

ALTER TABLE deployments ADD COLUMN IF NOT EXISTS triggered_by UUID REFERENCES user_profiles(id);

ALTER TABLE dns_records ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES user_profiles(id);

-- Create indexes for user tracking
CREATE INDEX IF NOT EXISTS idx_trends_created_by ON trends(created_by);
CREATE INDEX IF NOT EXISTS idx_trends_approved_by ON trends(approved_by);
CREATE INDEX IF NOT EXISTS idx_content_created_by ON content(created_by);
CREATE INDEX IF NOT EXISTS idx_deployments_triggered_by ON deployments(triggered_by);
CREATE INDEX IF NOT EXISTS idx_dns_records_created_by ON dns_records(created_by);

-- Record migration
INSERT INTO schema_migrations (version, applied_at) VALUES ('002', NOW())
ON CONFLICT (version) DO NOTHING;

-- Migration 003: Create analytics table
-- Add analytics and tracking support

-- Create analytics table
CREATE TABLE IF NOT EXISTS analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
        'page_view', 'user_interaction', 'system_event', 'performance_metric', 'error_event'
    )),
    trend_id UUID REFERENCES trends(id) ON DELETE SET NULL,
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    page_url TEXT,
    event_data JSONB DEFAULT '{}',
    metrics JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for analytics
CREATE INDEX IF NOT EXISTS idx_analytics_event_type ON analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_trend_id ON analytics(trend_id);
CREATE INDEX IF NOT EXISTS idx_analytics_user_id ON analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_session_id ON analytics(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_timestamp ON analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_created_at ON analytics(created_at);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_analytics_trend_event_timestamp ON analytics(trend_id, event_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_user_event_timestamp ON analytics(user_id, event_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_event_timestamp ON analytics(event_type, timestamp);

-- Create GIN indexes for JSONB fields
CREATE INDEX IF NOT EXISTS idx_analytics_event_data_gin ON analytics USING GIN(event_data);
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_gin ON analytics USING GIN(metrics);

-- Create updated_at trigger for analytics
CREATE OR REPLACE FUNCTION update_analytics_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_analytics_updated_at
    BEFORE UPDATE ON analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_updated_at();

-- Create function to record page view
CREATE OR REPLACE FUNCTION record_page_view(
    p_trend_id UUID DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_session_id VARCHAR(255) DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_referrer TEXT DEFAULT NULL,
    p_page_url TEXT DEFAULT NULL,
    p_additional_data JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    analytics_id UUID;
BEGIN
    INSERT INTO analytics (
        event_type, trend_id, user_id, session_id, ip_address,
        user_agent, referrer, page_url, event_data
    ) VALUES (
        'page_view', p_trend_id, p_user_id, p_session_id, p_ip_address,
        p_user_agent, p_referrer, p_page_url, p_additional_data
    ) RETURNING id INTO analytics_id;
    
    RETURN analytics_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to record user interaction
CREATE OR REPLACE FUNCTION record_user_interaction(
    p_interaction_type TEXT,
    p_trend_id UUID DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_session_id VARCHAR(255) DEFAULT NULL,
    p_interaction_data JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    analytics_id UUID;
BEGIN
    INSERT INTO analytics (
        event_type, trend_id, user_id, session_id, event_data
    ) VALUES (
        'user_interaction', p_trend_id, p_user_id, p_session_id,
        jsonb_build_object('interaction_type', p_interaction_type) || p_interaction_data
    ) RETURNING id INTO analytics_id;
    
    RETURN analytics_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to get trend analytics
CREATE OR REPLACE FUNCTION get_trend_analytics(
    p_trend_id UUID,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    page_views BIGINT,
    unique_visitors BIGINT,
    interactions BIGINT,
    avg_load_time NUMERIC,
    unique_ips BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) FILTER (WHERE event_type = 'page_view') as page_views,
        COUNT(DISTINCT session_id) FILTER (WHERE event_type = 'page_view') as unique_visitors,
        COUNT(*) FILTER (WHERE event_type = 'user_interaction') as interactions,
        AVG((metrics->>'page_load_time')::numeric) FILTER (WHERE metrics->>'page_load_time' IS NOT NULL) as avg_load_time,
        COUNT(DISTINCT ip_address) as unique_ips
    FROM analytics 
    WHERE trend_id = p_trend_id 
    AND timestamp BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql;

-- Create function to get system analytics
CREATE OR REPLACE FUNCTION get_system_analytics(
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '7 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    total_events BIGINT,
    total_page_views BIGINT,
    unique_sessions BIGINT,
    unique_visitors BIGINT,
    error_events BIGINT,
    avg_load_time NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_events,
        COUNT(*) FILTER (WHERE event_type = 'page_view') as total_page_views,
        COUNT(DISTINCT session_id) as unique_sessions,
        COUNT(DISTINCT ip_address) as unique_visitors,
        COUNT(*) FILTER (WHERE event_type = 'error_event') as error_events,
        AVG((metrics->>'page_load_time')::numeric) FILTER (WHERE metrics->>'page_load_time' IS NOT NULL) as avg_load_time
    FROM analytics 
    WHERE timestamp BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql;

-- Create function to get top trends by views
CREATE OR REPLACE FUNCTION get_top_trends_by_views(
    p_limit INTEGER DEFAULT 10,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '7 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    trend_id UUID,
    keyword TEXT,
    slug TEXT,
    page_views BIGINT,
    unique_visitors BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.trend_id,
        t.keyword,
        t.slug,
        COUNT(*) as page_views,
        COUNT(DISTINCT a.session_id) as unique_visitors
    FROM analytics a
    JOIN trends t ON a.trend_id = t.id
    WHERE a.event_type = 'page_view'
    AND a.trend_id IS NOT NULL
    AND a.timestamp BETWEEN p_start_date AND p_end_date
    GROUP BY a.trend_id, t.keyword, t.slug
    ORDER BY page_views DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Create materialized view for daily analytics summary
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_analytics_summary AS
SELECT 
    DATE(timestamp) as date,
    event_type,
    trend_id,
    COUNT(*) as event_count,
    COUNT(DISTINCT session_id) as unique_sessions,
    COUNT(DISTINCT ip_address) as unique_ips,
    AVG((metrics->>'page_load_time')::numeric) FILTER (WHERE metrics->>'page_load_time' IS NOT NULL) as avg_load_time
FROM analytics
WHERE timestamp >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(timestamp), event_type, trend_id;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_daily_analytics_summary_unique 
ON daily_analytics_summary(date, event_type, COALESCE(trend_id, '00000000-0000-0000-0000-000000000000'::uuid));

-- Create function to refresh analytics summary
CREATE OR REPLACE FUNCTION refresh_analytics_summary()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY daily_analytics_summary;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security (RLS) policies for analytics
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;

-- Policy: All authenticated users can read analytics
CREATE POLICY analytics_read_authenticated ON analytics
    FOR SELECT
    USING (
        current_setting('app.current_user_id', true) IS NOT NULL
        AND current_setting('app.current_user_id', true) != ''
    );

-- Policy: System can insert analytics (no user context required)
CREATE POLICY analytics_insert_system ON analytics
    FOR INSERT
    WITH CHECK (true);

-- Policy: Admins can update/delete analytics
CREATE POLICY analytics_admin_all ON analytics
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.id = current_setting('app.current_user_id')::uuid 
            AND up.role = 'admin'
        )
    );

-- Create cleanup function for old analytics data
CREATE OR REPLACE FUNCTION cleanup_old_analytics(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM analytics 
    WHERE timestamp < NOW() - (retention_days || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Record migration
INSERT INTO schema_migrations (version, applied_at) VALUES ('003', NOW())
ON CONFLICT (version) DO NOTHING;

-- Migration 004: Create system_logs table
-- Add centralized logging and audit trail support

-- Create system_logs table
CREATE TABLE IF NOT EXISTS system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level VARCHAR(20) NOT NULL CHECK (level IN ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')),
    category VARCHAR(50) NOT NULL DEFAULT 'application' CHECK (category IN (
        'application', 'security', 'performance', 'audit', 'system', 'api', 'database', 'celery'
    )),
    module VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    correlation_id VARCHAR(255),
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    ip_address INET,
    request_id VARCHAR(255),
    task_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    stack_trace TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for system_logs
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_category ON system_logs(category);
CREATE INDEX IF NOT EXISTS idx_system_logs_module ON system_logs(module);
CREATE INDEX IF NOT EXISTS idx_system_logs_correlation_id ON system_logs(correlation_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_system_logs_level_timestamp ON system_logs(level, timestamp);
CREATE INDEX IF NOT EXISTS idx_system_logs_module_level_timestamp ON system_logs(module, level, timestamp);
CREATE INDEX IF NOT EXISTS idx_system_logs_category_timestamp ON system_logs(category, timestamp);

-- Create GIN index for metadata JSONB field
CREATE INDEX IF NOT EXISTS idx_system_logs_metadata_gin ON system_logs USING GIN(metadata);

-- Create text search index for message content
CREATE INDEX IF NOT EXISTS idx_system_logs_message_text ON system_logs USING GIN(to_tsvector('english', message));

-- Create updated_at trigger for system_logs
CREATE OR REPLACE FUNCTION update_system_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_system_logs_updated_at
    BEFORE UPDATE ON system_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_system_logs_updated_at();

-- Create function to log events
CREATE OR REPLACE FUNCTION log_event(
    p_level VARCHAR(20),
    p_module VARCHAR(100),
    p_message TEXT,
    p_category VARCHAR(50) DEFAULT 'application',
    p_correlation_id VARCHAR(255) DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}',
    p_stack_trace TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO system_logs (
        level, category, module, message, correlation_id, user_id, metadata, stack_trace
    ) VALUES (
        p_level, p_category, p_module, p_message, p_correlation_id, p_user_id, p_metadata, p_stack_trace
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to log API requests
CREATE OR REPLACE FUNCTION log_api_request(
    p_method VARCHAR(10),
    p_path TEXT,
    p_status_code INTEGER,
    p_duration NUMERIC,
    p_user_id UUID DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_correlation_id VARCHAR(255) DEFAULT NULL,
    p_request_id VARCHAR(255) DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
    log_level VARCHAR(20);
    log_message TEXT;
BEGIN
    -- Determine log level based on status code
    log_level := CASE 
        WHEN p_status_code >= 500 THEN 'ERROR'
        WHEN p_status_code >= 400 THEN 'WARNING'
        ELSE 'INFO'
    END;
    
    log_message := p_method || ' ' || p_path || ' - ' || p_status_code || ' (' || p_duration || 's)';
    
    INSERT INTO system_logs (
        level, category, module, message, correlation_id, user_id, 
        ip_address, request_id, metadata
    ) VALUES (
        log_level, 'api', 'api', log_message, p_correlation_id, p_user_id,
        p_ip_address, p_request_id, jsonb_build_object(
            'method', p_method,
            'path', p_path,
            'status_code', p_status_code,
            'duration', p_duration
        )
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_event_type VARCHAR(100),
    p_message TEXT,
    p_user_id UUID DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_severity VARCHAR(20) DEFAULT 'WARNING',
    p_additional_data JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO system_logs (
        level, category, module, message, user_id, ip_address, metadata
    ) VALUES (
        p_severity, 'security', 'security', p_message, p_user_id, p_ip_address,
        jsonb_build_object('event_type', p_event_type) || p_additional_data
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to log Celery tasks
CREATE OR REPLACE FUNCTION log_celery_task(
    p_task_name VARCHAR(255),
    p_task_id VARCHAR(255),
    p_status VARCHAR(50),
    p_duration NUMERIC DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL,
    p_correlation_id VARCHAR(255) DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
    log_level VARCHAR(20);
    log_message TEXT;
BEGIN
    -- Determine log level based on status
    log_level := CASE 
        WHEN p_status = 'FAILURE' THEN 'ERROR'
        WHEN p_status = 'RETRY' THEN 'WARNING'
        ELSE 'INFO'
    END;
    
    log_message := 'Task ' || p_task_name || ' [' || p_task_id || '] - ' || p_status;
    IF p_duration IS NOT NULL THEN
        log_message := log_message || ' (' || p_duration || 's)';
    END IF;
    
    INSERT INTO system_logs (
        level, category, module, message, correlation_id, task_id, metadata
    ) VALUES (
        log_level, 'celery', 'celery', log_message, p_correlation_id, p_task_id,
        jsonb_build_object(
            'task_name', p_task_name,
            'status', p_status,
            'duration', p_duration,
            'error_message', p_error_message
        )
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to get error summary
CREATE OR REPLACE FUNCTION get_error_summary(
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '24 hours',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    error_count BIGINT,
    critical_count BIGINT,
    warning_count BIGINT,
    affected_modules BIGINT,
    unique_requests BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) FILTER (WHERE level = 'ERROR') as error_count,
        COUNT(*) FILTER (WHERE level = 'CRITICAL') as critical_count,
        COUNT(*) FILTER (WHERE level = 'WARNING') as warning_count,
        COUNT(DISTINCT module) as affected_modules,
        COUNT(DISTINCT correlation_id) FILTER (WHERE correlation_id IS NOT NULL) as unique_requests
    FROM system_logs 
    WHERE timestamp BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql;

-- Create function to get top errors
CREATE OR REPLACE FUNCTION get_top_errors(
    p_limit INTEGER DEFAULT 10,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '24 hours',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    message TEXT,
    module VARCHAR(100),
    level VARCHAR(20),
    occurrence_count BIGINT,
    last_occurrence TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sl.message,
        sl.module,
        sl.level,
        COUNT(*) as occurrence_count,
        MAX(sl.timestamp) as last_occurrence
    FROM system_logs sl
    WHERE sl.level IN ('ERROR', 'CRITICAL')
    AND sl.timestamp BETWEEN p_start_date AND p_end_date
    GROUP BY sl.message, sl.module, sl.level
    ORDER BY occurrence_count DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Create materialized view for log summary by hour
CREATE MATERIALIZED VIEW IF NOT EXISTS hourly_log_summary AS
SELECT 
    DATE_TRUNC('hour', timestamp) as hour,
    level,
    category,
    module,
    COUNT(*) as log_count
FROM system_logs
WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', timestamp), level, category, module;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_hourly_log_summary_unique 
ON hourly_log_summary(hour, level, category, module);

-- Create function to refresh log summary
CREATE OR REPLACE FUNCTION refresh_log_summary()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY hourly_log_summary;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security (RLS) policies for system_logs
ALTER TABLE system_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Admins can read all logs
CREATE POLICY system_logs_admin_read_all ON system_logs
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.id = current_setting('app.current_user_id')::uuid 
            AND up.role = 'admin'
        )
    );

-- Policy: Users can read their own logs
CREATE POLICY system_logs_read_own ON system_logs
    FOR SELECT
    USING (user_id = current_setting('app.current_user_id')::uuid);

-- Policy: System can insert logs (no user context required)
CREATE POLICY system_logs_insert_system ON system_logs
    FOR INSERT
    WITH CHECK (true);

-- Policy: Admins can update/delete logs
CREATE POLICY system_logs_admin_modify ON system_logs
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.id = current_setting('app.current_user_id')::uuid 
            AND up.role = 'admin'
        )
    );

-- Create cleanup function for old logs
CREATE OR REPLACE FUNCTION cleanup_old_logs(retention_days INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Keep ERROR and CRITICAL logs longer
    DELETE FROM system_logs 
    WHERE timestamp < NOW() - (retention_days || ' days')::INTERVAL
    AND level NOT IN ('ERROR', 'CRITICAL');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to archive old logs to separate table
CREATE OR REPLACE FUNCTION archive_old_logs(archive_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER;
BEGIN
    -- Create archive table if it doesn't exist
    CREATE TABLE IF NOT EXISTS system_logs_archive (LIKE system_logs INCLUDING ALL);
    
    -- Move old logs to archive
    WITH moved_logs AS (
        DELETE FROM system_logs 
        WHERE timestamp < NOW() - (archive_days || ' days')::INTERVAL
        RETURNING *
    )
    INSERT INTO system_logs_archive SELECT * FROM moved_logs;
    
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    RETURN archived_count;
END;
$$ LANGUAGE plpgsql;

-- Record migration
INSERT INTO schema_migrations (version, applied_at) VALUES ('004', NOW())
ON CONFLICT (version) DO NOTHING;

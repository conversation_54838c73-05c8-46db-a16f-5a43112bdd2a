"""
Analytics model and repository for tracking system metrics and user behavior
Handles page views, user interactions, and system performance data
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator
from enum import Enum

from database.models.base import BaseEntity, BaseRepository
from shared.exceptions import DatabaseError


class AnalyticsEventType(str, Enum):
    """Analytics event type enumeration"""
    PAGE_VIEW = "page_view"
    USER_INTERACTION = "user_interaction"
    SYSTEM_EVENT = "system_event"
    PERFORMANCE_METRIC = "performance_metric"
    ERROR_EVENT = "error_event"


class AnalyticsEntity(BaseEntity):
    """Analytics entity model"""
    event_type: AnalyticsEventType
    trend_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    referrer: Optional[str] = None
    page_url: Optional[str] = None
    event_data: Dict[str, Any] = Field(default_factory=dict)
    metrics: Dict[str, float] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    @validator('ip_address')
    def validate_ip_address(cls, v):
        if v and not (v.count('.') == 3 or ':' in v):  # Basic IPv4/IPv6 check
            raise ValueError('Invalid IP address format')
        return v


class AnalyticsCreateRequest(BaseModel):
    """Request model for creating analytics records"""
    event_type: AnalyticsEventType
    trend_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    referrer: Optional[str] = None
    page_url: Optional[str] = None
    event_data: Dict[str, Any] = Field(default_factory=dict)
    metrics: Dict[str, float] = Field(default_factory=dict)


class AnalyticsRepository(BaseRepository[AnalyticsEntity]):
    """Repository for analytics operations"""
    
    def __init__(self):
        super().__init__('analytics', AnalyticsEntity)
    
    async def record_page_view(
        self, 
        trend_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        referrer: Optional[str] = None,
        page_url: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> AnalyticsEntity:
        """Record a page view event"""
        
        analytics_data = {
            'event_type': AnalyticsEventType.PAGE_VIEW,
            'trend_id': trend_id,
            'user_id': user_id,
            'session_id': session_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'referrer': referrer,
            'page_url': page_url,
            'event_data': additional_data or {},
            'metrics': {
                'page_load_time': 0.0,  # Would be populated from frontend
                'bounce_rate': 0.0
            }
        }
        
        return await self.create(analytics_data)
    
    async def record_user_interaction(
        self,
        interaction_type: str,
        trend_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        interaction_data: Optional[Dict[str, Any]] = None
    ) -> AnalyticsEntity:
        """Record a user interaction event"""
        
        analytics_data = {
            'event_type': AnalyticsEventType.USER_INTERACTION,
            'trend_id': trend_id,
            'user_id': user_id,
            'session_id': session_id,
            'event_data': {
                'interaction_type': interaction_type,
                **(interaction_data or {})
            }
        }
        
        return await self.create(analytics_data)
    
    async def record_system_event(
        self,
        event_name: str,
        event_data: Optional[Dict[str, Any]] = None,
        metrics: Optional[Dict[str, float]] = None
    ) -> AnalyticsEntity:
        """Record a system event"""
        
        analytics_data = {
            'event_type': AnalyticsEventType.SYSTEM_EVENT,
            'event_data': {
                'event_name': event_name,
                **(event_data or {})
            },
            'metrics': metrics or {}
        }
        
        return await self.create(analytics_data)
    
    async def record_performance_metric(
        self,
        metric_name: str,
        metric_value: float,
        additional_metrics: Optional[Dict[str, float]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AnalyticsEntity:
        """Record a performance metric"""
        
        analytics_data = {
            'event_type': AnalyticsEventType.PERFORMANCE_METRIC,
            'event_data': {
                'metric_name': metric_name,
                **(context or {})
            },
            'metrics': {
                metric_name: metric_value,
                **(additional_metrics or {})
            }
        }
        
        return await self.create(analytics_data)
    
    async def get_trend_analytics(
        self, 
        trend_id: str, 
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get analytics data for a specific trend"""
        db = await self.get_db_manager()
        
        # Default to last 30 days if no dates provided
        if not start_date:
            start_date = datetime.utcnow() - timedelta(days=30)
        if not end_date:
            end_date = datetime.utcnow()
        
        query = """
            SELECT 
                COUNT(*) FILTER (WHERE event_type = 'page_view') as page_views,
                COUNT(DISTINCT session_id) FILTER (WHERE event_type = 'page_view') as unique_visitors,
                COUNT(*) FILTER (WHERE event_type = 'user_interaction') as interactions,
                AVG(metrics->>'page_load_time') FILTER (WHERE metrics->>'page_load_time' IS NOT NULL) as avg_load_time,
                COUNT(DISTINCT ip_address) as unique_ips
            FROM analytics 
            WHERE trend_id = $1 
            AND timestamp BETWEEN $2 AND $3
        """
        
        try:
            row = await db.fetchrow(query, trend_id, start_date, end_date)
            return dict(row) if row else {}
        except Exception as e:
            raise DatabaseError(f"Failed to get trend analytics: {str(e)}")
    
    async def get_system_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get system-wide analytics"""
        db = await self.get_db_manager()
        
        # Default to last 7 days if no dates provided
        if not start_date:
            start_date = datetime.utcnow() - timedelta(days=7)
        if not end_date:
            end_date = datetime.utcnow()
        
        query = """
            SELECT 
                COUNT(*) as total_events,
                COUNT(*) FILTER (WHERE event_type = 'page_view') as total_page_views,
                COUNT(DISTINCT session_id) as unique_sessions,
                COUNT(DISTINCT ip_address) as unique_visitors,
                COUNT(*) FILTER (WHERE event_type = 'error_event') as error_events,
                AVG(metrics->>'page_load_time') FILTER (WHERE metrics->>'page_load_time' IS NOT NULL) as avg_load_time
            FROM analytics 
            WHERE timestamp BETWEEN $1 AND $2
        """
        
        try:
            row = await db.fetchrow(query, start_date, end_date)
            return dict(row) if row else {}
        except Exception as e:
            raise DatabaseError(f"Failed to get system analytics: {str(e)}")
    
    async def get_top_trends_by_views(
        self,
        limit: int = 10,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """Get top trends by page views"""
        db = await self.get_db_manager()
        
        # Default to last 7 days if no dates provided
        if not start_date:
            start_date = datetime.utcnow() - timedelta(days=7)
        if not end_date:
            end_date = datetime.utcnow()
        
        query = """
            SELECT 
                a.trend_id,
                t.keyword,
                t.slug,
                COUNT(*) as page_views,
                COUNT(DISTINCT a.session_id) as unique_visitors
            FROM analytics a
            JOIN trends t ON a.trend_id = t.id
            WHERE a.event_type = 'page_view'
            AND a.trend_id IS NOT NULL
            AND a.timestamp BETWEEN $1 AND $2
            GROUP BY a.trend_id, t.keyword, t.slug
            ORDER BY page_views DESC
            LIMIT $3
        """
        
        try:
            rows = await db.fetch(query, start_date, end_date, limit)
            return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get top trends by views: {str(e)}")
    
    async def get_performance_metrics(
        self,
        metric_name: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """Get performance metrics over time"""
        db = await self.get_db_manager()
        
        # Default to last 24 hours if no dates provided
        if not start_date:
            start_date = datetime.utcnow() - timedelta(hours=24)
        if not end_date:
            end_date = datetime.utcnow()
        
        conditions = ["event_type = 'performance_metric'", "timestamp BETWEEN $1 AND $2"]
        params = [start_date, end_date]
        
        if metric_name:
            conditions.append("event_data->>'metric_name' = $3")
            params.append(metric_name)
        
        query = f"""
            SELECT 
                event_data->>'metric_name' as metric_name,
                AVG((metrics->>event_data->>'metric_name')::float) as avg_value,
                MIN((metrics->>event_data->>'metric_name')::float) as min_value,
                MAX((metrics->>event_data->>'metric_name')::float) as max_value,
                COUNT(*) as sample_count,
                DATE_TRUNC('hour', timestamp) as hour
            FROM analytics 
            WHERE {' AND '.join(conditions)}
            GROUP BY event_data->>'metric_name', DATE_TRUNC('hour', timestamp)
            ORDER BY hour DESC
        """
        
        try:
            rows = await db.fetch(query, *params)
            return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get performance metrics: {str(e)}")
    
    async def cleanup_old_analytics(self, retention_days: int = 90) -> int:
        """Clean up analytics data older than retention period"""
        db = await self.get_db_manager()
        
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        query = "DELETE FROM analytics WHERE timestamp < $1"
        
        try:
            result = await db.execute(query, cutoff_date)
            deleted_count = int(result.split()[-1]) if result else 0
            return deleted_count
        except Exception as e:
            raise DatabaseError(f"Failed to cleanup old analytics: {str(e)}")


# Global repository instance
analytics_repository = AnalyticsRepository()

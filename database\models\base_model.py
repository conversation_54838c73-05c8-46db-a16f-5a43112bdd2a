"""
Base model and repository pattern implementation
Provides common functionality for all data models
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, TypeVar, Generic, Union
from datetime import datetime
import uuid
from pydantic import BaseModel, Field
from database.connection import DatabaseManager, get_db_manager
from shared.exceptions import DatabaseError

T = TypeVar('T', bound=BaseModel)


class BaseEntity(BaseModel):
    """Base entity model with common fields"""
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class BaseRepository(Generic[T], ABC):
    """Base repository with common CRUD operations"""
    
    def __init__(self, table_name: str, entity_class: type[T]):
        self.table_name = table_name
        self.entity_class = entity_class
        self._db_manager: Optional[DatabaseManager] = None
    
    async def get_db_manager(self) -> DatabaseManager:
        """Get database manager instance"""
        if not self._db_manager:
            self._db_manager = await get_db_manager()
        return self._db_manager
    
    async def create(self, data: Dict[str, Any]) -> str:
        """Create new record and return ID"""
        db = await self.get_db_manager()
        
        # Ensure ID is present
        if 'id' not in data:
            data['id'] = str(uuid.uuid4())
        
        # Add timestamps
        data['created_at'] = datetime.utcnow()
        data['updated_at'] = datetime.utcnow()
        
        columns = list(data.keys())
        placeholders = [f"${i+1}" for i in range(len(columns))]
        values = list(data.values())
        
        query = f"""
            INSERT INTO {self.table_name} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            RETURNING id
        """
        
        try:
            result = await db.fetchval(query, *values)
            return str(result)
        except Exception as e:
            raise DatabaseError(f"Failed to create {self.table_name}: {str(e)}")
    
    async def get_by_id(self, record_id: str) -> Optional[T]:
        """Get record by ID"""
        db = await self.get_db_manager()
        
        query = f"SELECT * FROM {self.table_name} WHERE id = $1"
        
        try:
            row = await db.fetchrow(query, record_id)
            return self.entity_class(**row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get {self.table_name} by ID: {str(e)}")
    
    async def update(self, record_id: str, data: Dict[str, Any]) -> bool:
        """Update record by ID"""
        if not data:
            return False
        
        db = await self.get_db_manager()
        
        # Add updated timestamp
        data['updated_at'] = datetime.utcnow()
        
        set_clauses = [f"{key} = ${i+2}" for i, key in enumerate(data.keys())]
        values = [record_id] + list(data.values())
        
        query = f"""
            UPDATE {self.table_name} 
            SET {', '.join(set_clauses)}
            WHERE id = $1
        """
        
        try:
            result = await db.execute(query, *values)
            return result.split()[-1] == '1'
        except Exception as e:
            raise DatabaseError(f"Failed to update {self.table_name}: {str(e)}")
    
    async def delete(self, record_id: str) -> bool:
        """Delete record by ID"""
        db = await self.get_db_manager()
        
        query = f"DELETE FROM {self.table_name} WHERE id = $1"
        
        try:
            result = await db.execute(query, record_id)
            return result.split()[-1] == '1'
        except Exception as e:
            raise DatabaseError(f"Failed to delete {self.table_name}: {str(e)}")
    
    async def list_with_pagination(
        self, 
        page: int = 1, 
        page_size: int = 20,
        filters: Optional[Dict[str, Any]] = None,
        order_by: str = 'created_at DESC'
    ) -> Dict[str, Any]:
        """List records with pagination and filtering"""
        db = await self.get_db_manager()
        
        offset = (page - 1) * page_size
        where_clauses = []
        values = []
        
        if filters:
            for key, value in filters.items():
                if value is not None:
                    where_clauses.append(f"{key} = ${len(values) + 1}")
                    values.append(value)
        
        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        
        # Count total records
        count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_sql}"
        
        # Get paginated records
        data_query = f"""
            SELECT * FROM {self.table_name} 
            {where_sql}
            ORDER BY {order_by}
            LIMIT ${len(values) + 1} OFFSET ${len(values) + 2}
        """
        
        try:
            total_count = await db.fetchval(count_query, *values)
            rows = await db.fetch(data_query, *values, page_size, offset)
            
            entities = [self.entity_class(**row) for row in rows]
            
            return {
                'data': entities,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size,
                    'has_next': page * page_size < total_count,
                    'has_prev': page > 1
                }
            }
        except Exception as e:
            raise DatabaseError(f"Failed to list {self.table_name}: {str(e)}")
    
    async def exists(self, record_id: str) -> bool:
        """Check if record exists"""
        db = await self.get_db_manager()
        
        query = f"SELECT EXISTS(SELECT 1 FROM {self.table_name} WHERE id = $1)"
        
        try:
            return await db.fetchval(query, record_id)
        except Exception as e:
            raise DatabaseError(f"Failed to check existence in {self.table_name}: {str(e)}")
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count records with optional filters"""
        db = await self.get_db_manager()
        
        where_clauses = []
        values = []
        
        if filters:
            for key, value in filters.items():
                if value is not None:
                    where_clauses.append(f"{key} = ${len(values) + 1}")
                    values.append(value)
        
        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        query = f"SELECT COUNT(*) FROM {self.table_name} {where_sql}"
        
        try:
            return await db.fetchval(query, *values)
        except Exception as e:
            raise DatabaseError(f"Failed to count {self.table_name}: {str(e)}")
    
    async def bulk_create(self, data_list: List[Dict[str, Any]]) -> List[str]:
        """Create multiple records in a single transaction"""
        if not data_list:
            return []
        
        db = await self.get_db_manager()
        
        # Prepare data
        for data in data_list:
            if 'id' not in data:
                data['id'] = str(uuid.uuid4())
            data['created_at'] = datetime.utcnow()
            data['updated_at'] = datetime.utcnow()
        
        # Get columns from first record
        columns = list(data_list[0].keys())
        placeholders = [f"${i+1}" for i in range(len(columns))]
        
        query = f"""
            INSERT INTO {self.table_name} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            RETURNING id
        """
        
        try:
            async with db.transaction() as conn:
                ids = []
                for data in data_list:
                    values = [data[col] for col in columns]
                    result = await conn.fetchval(query, *values)
                    ids.append(str(result))
                return ids
        except Exception as e:
            raise DatabaseError(f"Failed to bulk create {self.table_name}: {str(e)}")
    
    async def search(self, search_term: str, search_fields: List[str], limit: int = 50) -> List[T]:
        """Search records using text search"""
        db = await self.get_db_manager()
        
        # Build search conditions
        search_conditions = []
        for field in search_fields:
            search_conditions.append(f"{field} ILIKE $1")
        
        where_clause = " OR ".join(search_conditions)
        search_pattern = f"%{search_term}%"
        
        query = f"""
            SELECT * FROM {self.table_name}
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT $2
        """
        
        try:
            rows = await db.fetch(query, search_pattern, limit)
            return [self.entity_class(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to search {self.table_name}: {str(e)}")


class TimestampMixin:
    """Mixin for models with timestamp fields"""
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class SoftDeleteMixin:
    """Mixin for models with soft delete functionality"""
    deleted_at: Optional[datetime] = None
    is_deleted: bool = False

"""
Content model and repository implementation
Handles all content-related database operations
"""
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from database.models.base_model import BaseEntity, BaseRepository
from shared.exceptions import DatabaseError


class ContentEntity(BaseEntity):
    """Content entity model"""
    trend_id: str
    title: str
    description: Optional[str] = None
    body: str
    meta_tags: Dict[str, str] = Field(default_factory=dict)
    hero_image_url: Optional[str] = None
    hero_image_alt: Optional[str] = None
    code_snippet: Optional[str] = None
    code_language: Optional[str] = None
    word_count: Optional[int] = None
    readability_score: Optional[float] = None
    ai_model_used: Optional[str] = None
    generation_metadata: Dict[str, Any] = Field(default_factory=dict)
    created_by: Optional[str] = None
    
    @validator('word_count')
    def validate_word_count(cls, v):
        if v is not None and v < 0:
            raise ValueError('Word count must be positive')
        return v
    
    @validator('readability_score')
    def validate_readability_score(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError('Readability score must be between 0 and 100')
        return v


class ContentCreateRequest(BaseModel):
    """Request model for creating content"""
    trend_id: str
    title: str
    description: Optional[str] = None
    body: str
    meta_tags: Dict[str, str] = Field(default_factory=dict)
    hero_image_url: Optional[str] = None
    hero_image_alt: Optional[str] = None
    code_snippet: Optional[str] = None
    code_language: Optional[str] = None


class ContentUpdateRequest(BaseModel):
    """Request model for updating content"""
    title: Optional[str] = None
    description: Optional[str] = None
    body: Optional[str] = None
    meta_tags: Optional[Dict[str, str]] = None
    hero_image_url: Optional[str] = None
    hero_image_alt: Optional[str] = None
    code_snippet: Optional[str] = None
    code_language: Optional[str] = None


class ContentRepository(BaseRepository[ContentEntity]):
    """Repository for content operations"""
    
    def __init__(self):
        super().__init__('content', ContentEntity)
    
    async def get_by_trend_id(self, trend_id: str) -> Optional[ContentEntity]:
        """Get content by trend ID"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM content WHERE trend_id = $1"
        
        try:
            row = await db.fetchrow(query, trend_id)
            return ContentEntity(**row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get content by trend ID: {str(e)}")
    
    async def get_content_with_trend(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Get content with associated trend data"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                c.*,
                t.keyword,
                t.slug,
                t.region,
                t.category,
                t.status as trend_status
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            WHERE c.id = $1
        """
        
        try:
            row = await db.fetchrow(query, content_id)
            return dict(row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get content with trend: {str(e)}")
    
    async def get_ready_for_deployment(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get content ready for deployment"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                c.*,
                t.keyword,
                t.slug,
                t.region,
                t.category
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            WHERE t.status = 'approved'
            AND NOT EXISTS (
                SELECT 1 FROM deployments d 
                WHERE d.content_id = c.id 
                AND d.status IN ('pending', 'building', 'success')
            )
            ORDER BY c.created_at ASC
            LIMIT $1
        """
        
        try:
            rows = await db.fetch(query, limit)
            return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get content ready for deployment: {str(e)}")
    
    async def search_content(self, search_term: str, limit: int = 50) -> List[ContentEntity]:
        """Search content by title and body"""
        return await self.search(search_term, ['title', 'body'], limit)
    
    async def get_content_statistics(self) -> Dict[str, Any]:
        """Get content statistics"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                COUNT(*) as total_content,
                AVG(word_count) as avg_word_count,
                AVG(readability_score) as avg_readability_score,
                COUNT(CASE WHEN hero_image_url IS NOT NULL THEN 1 END) as content_with_images,
                COUNT(CASE WHEN code_snippet IS NOT NULL THEN 1 END) as content_with_code
            FROM content
        """
        
        try:
            row = await db.fetchrow(query)
            return dict(row) if row else {}
        except Exception as e:
            raise DatabaseError(f"Failed to get content statistics: {str(e)}")


# Global repository instance
content_repository = ContentRepository()

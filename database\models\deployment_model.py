"""
Deployment model and repository implementation
Handles all deployment-related database operations
"""
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum
from database.models.base_model import BaseEntity, BaseRepository
from shared.exceptions import DatabaseError


class DeploymentStatus(str, Enum):
    PENDING = "pending"
    BUILDING = "building"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DeploymentEntity(BaseEntity):
    """Deployment entity model"""
    trend_id: str
    content_id: str
    status: DeploymentStatus = DeploymentStatus.PENDING
    coolify_deployment_uuid: Optional[str] = None
    build_log: Optional[str] = None
    deploy_url: Optional[str] = None
    error_message: Optional[str] = None
    progress: int = 0
    build_duration: Optional[int] = None  # seconds
    deployment_metadata: Dict[str, Any] = Field(default_factory=dict)
    deployed_at: Optional[datetime] = None
    triggered_by: Optional[str] = None
    
    @validator('progress')
    def validate_progress(cls, v):
        if v < 0 or v > 100:
            raise ValueError('Progress must be between 0 and 100')
        return v


class DeploymentCreateRequest(BaseModel):
    """Request model for creating deployments"""
    trend_id: str
    content_id: str
    triggered_by: Optional[str] = None


class DeploymentUpdateRequest(BaseModel):
    """Request model for updating deployments"""
    status: Optional[DeploymentStatus] = None
    coolify_deployment_uuid: Optional[str] = None
    build_log: Optional[str] = None
    deploy_url: Optional[str] = None
    error_message: Optional[str] = None
    progress: Optional[int] = None
    build_duration: Optional[int] = None
    deployment_metadata: Optional[Dict[str, Any]] = None


class DeploymentRepository(BaseRepository[DeploymentEntity]):
    """Repository for deployment operations"""
    
    def __init__(self):
        super().__init__('deployments', DeploymentEntity)
    
    async def get_by_trend_id(self, trend_id: str) -> List[DeploymentEntity]:
        """Get deployments by trend ID"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM deployments 
            WHERE trend_id = $1 
            ORDER BY created_at DESC
        """
        
        try:
            rows = await db.fetch(query, trend_id)
            return [DeploymentEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get deployments by trend ID: {str(e)}")
    
    async def get_by_content_id(self, content_id: str) -> List[DeploymentEntity]:
        """Get deployments by content ID"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM deployments 
            WHERE content_id = $1 
            ORDER BY created_at DESC
        """
        
        try:
            rows = await db.fetch(query, content_id)
            return [DeploymentEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get deployments by content ID: {str(e)}")
    
    async def get_active_deployments(self) -> List[DeploymentEntity]:
        """Get currently active deployments"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM deployments 
            WHERE status IN ('pending', 'building')
            ORDER BY created_at ASC
        """
        
        try:
            rows = await db.fetch(query)
            return [DeploymentEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get active deployments: {str(e)}")
    
    async def get_successful_deployments(self, limit: int = 50) -> List[DeploymentEntity]:
        """Get successful deployments"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM deployments 
            WHERE status = 'success'
            ORDER BY deployed_at DESC
            LIMIT $1
        """
        
        try:
            rows = await db.fetch(query, limit)
            return [DeploymentEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get successful deployments: {str(e)}")
    
    async def get_failed_deployments(self, limit: int = 50) -> List[DeploymentEntity]:
        """Get failed deployments"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM deployments 
            WHERE status = 'failed'
            ORDER BY created_at DESC
            LIMIT $1
        """
        
        try:
            rows = await db.fetch(query, limit)
            return [DeploymentEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get failed deployments: {str(e)}")
    
    async def get_deployment_statistics(self) -> Dict[str, Any]:
        """Get deployment statistics"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                COUNT(*) as total_deployments,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_deployments,
                COUNT(CASE WHEN status = 'building' THEN 1 END) as building_deployments,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_deployments,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_deployments,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_deployments,
                AVG(build_duration) as avg_build_duration,
                MAX(build_duration) as max_build_duration,
                MIN(build_duration) as min_build_duration
            FROM deployments
        """
        
        try:
            row = await db.fetchrow(query)
            return dict(row) if row else {}
        except Exception as e:
            raise DatabaseError(f"Failed to get deployment statistics: {str(e)}")
    
    async def get_deployments_with_trend_info(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get deployments with associated trend information"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                d.*,
                t.keyword,
                t.slug,
                t.category,
                t.region,
                c.title as content_title
            FROM deployments d
            JOIN trends t ON d.trend_id = t.id
            JOIN content c ON d.content_id = c.id
            ORDER BY d.created_at DESC
            LIMIT $1
        """
        
        try:
            rows = await db.fetch(query, limit)
            return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get deployments with trend info: {str(e)}")


# Global repository instance
deployment_repository = DeploymentRepository()

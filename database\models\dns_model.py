"""
DNS model and repository implementation
Handles all DNS-related database operations
"""
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum
from database.models.base_model import BaseEntity, BaseRepository
from shared.exceptions import DatabaseError


class DNSStatus(str, Enum):
    PENDING = "pending"
    ACTIVE = "active"
    UPDATING = "updating"
    DELETED = "deleted"
    ERROR = "error"


class DNSEntity(BaseEntity):
    """DNS entity model"""
    trend_id: str
    subdomain: str
    target_url: str
    cloudflare_record_id: Optional[str] = None
    status: DNSStatus = DNSStatus.PENDING
    dns_metadata: Dict[str, Any] = Field(default_factory=dict)
    deleted_at: Optional[datetime] = None
    
    @validator('subdomain')
    def validate_subdomain(cls, v):
        if not v or len(v) < 3:
            raise ValueError('Subdomain must be at least 3 characters')
        return v.lower()


class DNSCreateRequest(BaseModel):
    """Request model for creating DNS records"""
    trend_id: str
    subdomain: str
    target_url: str


class DNSUpdateRequest(BaseModel):
    """Request model for updating DNS records"""
    target_url: Optional[str] = None
    status: Optional[DNSStatus] = None
    dns_metadata: Optional[Dict[str, Any]] = None


class DNSRepository(BaseRepository[DNSEntity]):
    """Repository for DNS operations"""
    
    def __init__(self):
        super().__init__('dns_records', DNSEntity)
    
    async def get_by_trend_id(self, trend_id: str) -> Optional[DNSEntity]:
        """Get DNS record by trend ID"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM dns_records WHERE trend_id = $1 AND status != 'deleted'"
        
        try:
            row = await db.fetchrow(query, trend_id)
            return DNSEntity(**row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get DNS record by trend ID: {str(e)}")
    
    async def get_by_subdomain(self, subdomain: str) -> Optional[DNSEntity]:
        """Get DNS record by subdomain"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM dns_records WHERE subdomain = $1 AND status != 'deleted'"
        
        try:
            row = await db.fetchrow(query, subdomain)
            return DNSEntity(**row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get DNS record by subdomain: {str(e)}")
    
    async def get_by_cloudflare_record_id(self, cloudflare_record_id: str) -> Optional[DNSEntity]:
        """Get DNS record by Cloudflare record ID"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM dns_records WHERE cloudflare_record_id = $1"
        
        try:
            row = await db.fetchrow(query, cloudflare_record_id)
            return DNSEntity(**row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get DNS record by Cloudflare ID: {str(e)}")
    
    async def get_active_records(self) -> List[DNSEntity]:
        """Get all active DNS records"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM dns_records 
            WHERE status = 'active'
            ORDER BY created_at DESC
        """
        
        try:
            rows = await db.fetch(query)
            return [DNSEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get active DNS records: {str(e)}")
    
    async def get_pending_records(self) -> List[DNSEntity]:
        """Get pending DNS records"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM dns_records 
            WHERE status = 'pending'
            ORDER BY created_at ASC
        """
        
        try:
            rows = await db.fetch(query)
            return [DNSEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get pending DNS records: {str(e)}")
    
    async def get_error_records(self) -> List[DNSEntity]:
        """Get DNS records with errors"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM dns_records 
            WHERE status = 'error'
            ORDER BY updated_at DESC
        """
        
        try:
            rows = await db.fetch(query)
            return [DNSEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get error DNS records: {str(e)}")
    
    async def get_records_for_cleanup(self, days_old: int = 30) -> List[DNSEntity]:
        """Get DNS records that can be cleaned up"""
        db = await self.get_db_manager()
        
        query = """
            SELECT dr.* FROM dns_records dr
            JOIN trends t ON dr.trend_id = t.id
            WHERE t.status = 'expired'
            AND t.expire_at < NOW() - INTERVAL '%s days'
            AND dr.status = 'active'
            ORDER BY t.expire_at ASC
        """ % days_old
        
        try:
            rows = await db.fetch(query)
            return [DNSEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get DNS records for cleanup: {str(e)}")
    
    async def get_dns_statistics(self) -> Dict[str, Any]:
        """Get DNS statistics"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_records,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_records,
                COUNT(CASE WHEN status = 'error' THEN 1 END) as error_records,
                COUNT(CASE WHEN status = 'deleted' THEN 1 END) as deleted_records,
                COUNT(CASE WHEN cloudflare_record_id IS NOT NULL THEN 1 END) as cloudflare_synced
            FROM dns_records
        """
        
        try:
            row = await db.fetchrow(query)
            return dict(row) if row else {}
        except Exception as e:
            raise DatabaseError(f"Failed to get DNS statistics: {str(e)}")
    
    async def get_records_with_trend_info(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get DNS records with associated trend information"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                dr.*,
                t.keyword,
                t.slug,
                t.category,
                t.region,
                t.status as trend_status
            FROM dns_records dr
            JOIN trends t ON dr.trend_id = t.id
            WHERE dr.status != 'deleted'
            ORDER BY dr.created_at DESC
            LIMIT $1
        """
        
        try:
            rows = await db.fetch(query, limit)
            return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get DNS records with trend info: {str(e)}")


# Global repository instance
dns_repository = DNSRepository()

"""
System logs model and repository for centralized logging
Handles application logs, error tracking, and audit trails
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator
from enum import Enum

from database.models.base import BaseEntity, BaseRepository
from shared.exceptions import DatabaseError


class LogLevel(str, Enum):
    """Log level enumeration"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(str, Enum):
    """Log category enumeration"""
    APPLICATION = "application"
    SECURITY = "security"
    PERFORMANCE = "performance"
    AUDIT = "audit"
    SYSTEM = "system"
    API = "api"
    DATABASE = "database"
    CELERY = "celery"


class SystemLogEntity(BaseEntity):
    """System log entity model"""
    level: LogLevel
    category: LogCategory = LogCategory.APPLICATION
    module: str
    message: str
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    request_id: Optional[str] = None
    task_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    stack_trace: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    @validator('message')
    def validate_message(cls, v):
        if len(v) > 10000:  # Limit message length
            return v[:10000] + "... [truncated]"
        return v


class SystemLogCreateRequest(BaseModel):
    """Request model for creating system logs"""
    level: LogLevel
    category: LogCategory = LogCategory.APPLICATION
    module: str
    message: str
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    request_id: Optional[str] = None
    task_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    stack_trace: Optional[str] = None


class SystemLogRepository(BaseRepository[SystemLogEntity]):
    """Repository for system log operations"""
    
    def __init__(self):
        super().__init__('system_logs', SystemLogEntity)
    
    async def log_event(
        self,
        level: LogLevel,
        module: str,
        message: str,
        category: LogCategory = LogCategory.APPLICATION,
        correlation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        stack_trace: Optional[str] = None
    ) -> SystemLogEntity:
        """Log a system event"""
        
        log_data = {
            'level': level,
            'category': category,
            'module': module,
            'message': message,
            'correlation_id': correlation_id,
            'user_id': user_id,
            'metadata': metadata or {},
            'stack_trace': stack_trace
        }
        
        return await self.create(log_data)
    
    async def log_api_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration: float,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        correlation_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> SystemLogEntity:
        """Log an API request"""
        
        level = LogLevel.ERROR if status_code >= 400 else LogLevel.INFO
        message = f"{method} {path} - {status_code} ({duration:.3f}s)"
        
        log_data = {
            'level': level,
            'category': LogCategory.API,
            'module': 'api',
            'message': message,
            'correlation_id': correlation_id,
            'user_id': user_id,
            'ip_address': ip_address,
            'request_id': request_id,
            'metadata': {
                'method': method,
                'path': path,
                'status_code': status_code,
                'duration': duration
            }
        }
        
        return await self.create(log_data)
    
    async def log_security_event(
        self,
        event_type: str,
        message: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        severity: LogLevel = LogLevel.WARNING,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> SystemLogEntity:
        """Log a security event"""
        
        log_data = {
            'level': severity,
            'category': LogCategory.SECURITY,
            'module': 'security',
            'message': message,
            'user_id': user_id,
            'ip_address': ip_address,
            'metadata': {
                'event_type': event_type,
                **(additional_data or {})
            }
        }
        
        return await self.create(log_data)
    
    async def log_celery_task(
        self,
        task_name: str,
        task_id: str,
        status: str,
        duration: Optional[float] = None,
        error_message: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> SystemLogEntity:
        """Log a Celery task event"""
        
        level = LogLevel.ERROR if status == 'FAILURE' else LogLevel.INFO
        message = f"Task {task_name} [{task_id}] - {status}"
        if duration:
            message += f" ({duration:.3f}s)"
        
        log_data = {
            'level': level,
            'category': LogCategory.CELERY,
            'module': 'celery',
            'message': message,
            'correlation_id': correlation_id,
            'task_id': task_id,
            'metadata': {
                'task_name': task_name,
                'status': status,
                'duration': duration,
                'error_message': error_message
            }
        }
        
        return await self.create(log_data)
    
    async def get_logs_by_level(
        self,
        level: LogLevel,
        limit: int = 100,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[SystemLogEntity]:
        """Get logs by level"""
        db = await self.get_db_manager()
        
        conditions = ["level = $1"]
        params = [level.value]
        param_count = 1
        
        if start_date:
            param_count += 1
            conditions.append(f"timestamp >= ${param_count}")
            params.append(start_date)
        
        if end_date:
            param_count += 1
            conditions.append(f"timestamp <= ${param_count}")
            params.append(end_date)
        
        param_count += 1
        limit_param = f"${param_count}"
        params.append(limit)
        
        query = f"""
            SELECT * FROM system_logs 
            WHERE {' AND '.join(conditions)}
            ORDER BY timestamp DESC 
            LIMIT {limit_param}
        """
        
        try:
            rows = await db.fetch(query, *params)
            return [SystemLogEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get logs by level: {str(e)}")
    
    async def get_logs_by_module(
        self,
        module: str,
        limit: int = 100,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[SystemLogEntity]:
        """Get logs by module"""
        db = await self.get_db_manager()
        
        conditions = ["module = $1"]
        params = [module]
        param_count = 1
        
        if start_date:
            param_count += 1
            conditions.append(f"timestamp >= ${param_count}")
            params.append(start_date)
        
        if end_date:
            param_count += 1
            conditions.append(f"timestamp <= ${param_count}")
            params.append(end_date)
        
        param_count += 1
        limit_param = f"${param_count}"
        params.append(limit)
        
        query = f"""
            SELECT * FROM system_logs 
            WHERE {' AND '.join(conditions)}
            ORDER BY timestamp DESC 
            LIMIT {limit_param}
        """
        
        try:
            rows = await db.fetch(query, *params)
            return [SystemLogEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get logs by module: {str(e)}")
    
    async def get_logs_by_correlation_id(self, correlation_id: str) -> List[SystemLogEntity]:
        """Get all logs for a specific correlation ID"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM system_logs 
            WHERE correlation_id = $1 
            ORDER BY timestamp ASC
        """
        
        try:
            rows = await db.fetch(query, correlation_id)
            return [SystemLogEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get logs by correlation ID: {str(e)}")
    
    async def get_error_summary(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get error summary statistics"""
        db = await self.get_db_manager()
        
        # Default to last 24 hours if no dates provided
        if not start_date:
            start_date = datetime.utcnow() - timedelta(hours=24)
        if not end_date:
            end_date = datetime.utcnow()
        
        query = """
            SELECT 
                COUNT(*) FILTER (WHERE level = 'ERROR') as error_count,
                COUNT(*) FILTER (WHERE level = 'CRITICAL') as critical_count,
                COUNT(*) FILTER (WHERE level = 'WARNING') as warning_count,
                COUNT(DISTINCT module) as affected_modules,
                COUNT(DISTINCT correlation_id) FILTER (WHERE correlation_id IS NOT NULL) as unique_requests
            FROM system_logs 
            WHERE timestamp BETWEEN $1 AND $2
        """
        
        try:
            row = await db.fetchrow(query, start_date, end_date)
            return dict(row) if row else {}
        except Exception as e:
            raise DatabaseError(f"Failed to get error summary: {str(e)}")
    
    async def get_top_errors(
        self,
        limit: int = 10,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """Get most frequent error messages"""
        db = await self.get_db_manager()
        
        # Default to last 24 hours if no dates provided
        if not start_date:
            start_date = datetime.utcnow() - timedelta(hours=24)
        if not end_date:
            end_date = datetime.utcnow()
        
        query = """
            SELECT 
                message,
                module,
                level,
                COUNT(*) as occurrence_count,
                MAX(timestamp) as last_occurrence
            FROM system_logs 
            WHERE level IN ('ERROR', 'CRITICAL')
            AND timestamp BETWEEN $1 AND $2
            GROUP BY message, module, level
            ORDER BY occurrence_count DESC
            LIMIT $3
        """
        
        try:
            rows = await db.fetch(query, start_date, end_date, limit)
            return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get top errors: {str(e)}")
    
    async def cleanup_old_logs(self, retention_days: int = 30) -> int:
        """Clean up logs older than retention period"""
        db = await self.get_db_manager()
        
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        # Keep ERROR and CRITICAL logs longer
        query = """
            DELETE FROM system_logs 
            WHERE timestamp < $1 
            AND level NOT IN ('ERROR', 'CRITICAL')
        """
        
        try:
            result = await db.execute(query, cutoff_date)
            deleted_count = int(result.split()[-1]) if result else 0
            return deleted_count
        except Exception as e:
            raise DatabaseError(f"Failed to cleanup old logs: {str(e)}")
    
    async def search_logs(
        self,
        search_term: str,
        limit: int = 100,
        level: Optional[LogLevel] = None,
        module: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[SystemLogEntity]:
        """Search logs by message content"""
        db = await self.get_db_manager()
        
        conditions = ["message ILIKE $1"]
        params = [f"%{search_term}%"]
        param_count = 1
        
        if level:
            param_count += 1
            conditions.append(f"level = ${param_count}")
            params.append(level.value)
        
        if module:
            param_count += 1
            conditions.append(f"module = ${param_count}")
            params.append(module)
        
        if start_date:
            param_count += 1
            conditions.append(f"timestamp >= ${param_count}")
            params.append(start_date)
        
        if end_date:
            param_count += 1
            conditions.append(f"timestamp <= ${param_count}")
            params.append(end_date)
        
        param_count += 1
        limit_param = f"${param_count}"
        params.append(limit)
        
        query = f"""
            SELECT * FROM system_logs 
            WHERE {' AND '.join(conditions)}
            ORDER BY timestamp DESC 
            LIMIT {limit_param}
        """
        
        try:
            rows = await db.fetch(query, *params)
            return [SystemLogEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to search logs: {str(e)}")


# Global repository instance
system_log_repository = SystemLogRepository()

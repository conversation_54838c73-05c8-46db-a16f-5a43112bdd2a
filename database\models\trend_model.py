"""
Trend model and repository implementation
Handles all trend-related database operations
"""
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator
from enum import Enum
from database.models.base_model import BaseEntity, BaseRepository
from shared.exceptions import DatabaseError


class TrendStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    LIVE = "live"
    EXPIRED = "expired"


class TrendEntity(BaseEntity):
    """Trend entity model"""
    keyword: str
    slug: str
    status: TrendStatus = TrendStatus.PENDING
    region: str
    category: str
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    score: Optional[float] = None
    source: str
    raw_data: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    expire_at: Optional[datetime] = None
    deployed_at: Optional[datetime] = None
    ads_enabled: bool = True
    created_by: Optional[str] = None
    approved_by: Optional[str] = None
    
    @validator('score')
    def validate_score(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError('Score must be between 0 and 100')
        return v
    
    @validator('growth_rate')
    def validate_growth_rate(cls, v):
        if v is not None and (v < -100 or v > 1000):
            raise ValueError('Growth rate must be between -100 and 1000')
        return v


class TrendCreateRequest(BaseModel):
    """Request model for creating trends"""
    keyword: str
    region: str
    category: str
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    source: str
    raw_data: Dict[str, Any] = Field(default_factory=dict)


class TrendUpdateRequest(BaseModel):
    """Request model for updating trends"""
    keyword: Optional[str] = None
    status: Optional[TrendStatus] = None
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    score: Optional[float] = None
    ads_enabled: Optional[bool] = None
    expire_at: Optional[datetime] = None


class TrendRepository(BaseRepository[TrendEntity]):
    """Repository for trend operations"""
    
    def __init__(self):
        super().__init__('trends', TrendEntity)
    
    async def get_by_slug(self, slug: str) -> Optional[TrendEntity]:
        """Get trend by slug"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM trends WHERE slug = $1"
        
        try:
            row = await db.fetchrow(query, slug)
            return TrendEntity(**row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get trend by slug: {str(e)}")
    
    async def get_by_keyword_and_region(self, keyword: str, region: str) -> Optional[TrendEntity]:
        """Get trend by keyword and region combination"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM trends WHERE keyword = $1 AND region = $2"
        
        try:
            row = await db.fetchrow(query, keyword, region)
            return TrendEntity(**row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get trend by keyword and region: {str(e)}")
    
    async def get_pending_trends(self, limit: int = 50) -> List[TrendEntity]:
        """Get pending trends for approval"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM trends 
            WHERE status = 'pending'
            ORDER BY score DESC NULLS LAST, created_at ASC
            LIMIT $1
        """
        
        try:
            rows = await db.fetch(query, limit)
            return [TrendEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get pending trends: {str(e)}")
    
    async def get_live_trends(self, region: str = None, category: str = None) -> List[TrendEntity]:
        """Get currently live trends"""
        db = await self.get_db_manager()
        
        where_clauses = ["status = 'live'", "(expire_at IS NULL OR expire_at > NOW())"]
        values = []
        
        if region:
            where_clauses.append(f"region = ${len(values) + 1}")
            values.append(region)
        
        if category:
            where_clauses.append(f"category = ${len(values) + 1}")
            values.append(category)
        
        query = f"""
            SELECT * FROM trends 
            WHERE {' AND '.join(where_clauses)}
            ORDER BY deployed_at DESC
        """
        
        try:
            rows = await db.fetch(query, *values)
            return [TrendEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get live trends: {str(e)}")
    
    async def get_expired_trends(self) -> List[TrendEntity]:
        """Get trends that have expired"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM trends 
            WHERE status = 'live' 
            AND expire_at IS NOT NULL 
            AND expire_at <= NOW()
            ORDER BY expire_at ASC
        """
        
        try:
            rows = await db.fetch(query)
            return [TrendEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get expired trends: {str(e)}")
    
    async def approve_trend(self, trend_id: str, approved_by: str) -> bool:
        """Approve a pending trend"""
        return await self.update(trend_id, {
            'status': TrendStatus.APPROVED,
            'approved_by': approved_by
        })
    
    async def reject_trend(self, trend_id: str, approved_by: str) -> bool:
        """Reject a pending trend"""
        return await self.update(trend_id, {
            'status': TrendStatus.REJECTED,
            'approved_by': approved_by
        })
    
    async def mark_as_live(self, trend_id: str, deploy_url: str = None) -> bool:
        """Mark trend as live after successful deployment"""
        expire_at = datetime.utcnow() + timedelta(days=14)  # 14-day TTL
        
        update_data = {
            'status': TrendStatus.LIVE,
            'deployed_at': datetime.utcnow(),
            'expire_at': expire_at
        }
        
        return await self.update(trend_id, update_data)
    
    async def mark_as_expired(self, trend_id: str) -> bool:
        """Mark trend as expired"""
        return await self.update(trend_id, {
            'status': TrendStatus.EXPIRED
        })
    
    async def get_trending_keywords(self, days: int = 7, limit: int = 100) -> List[Dict[str, Any]]:
        """Get trending keywords analytics"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                keyword,
                category,
                region,
                AVG(search_volume) as avg_search_volume,
                AVG(growth_rate) as avg_growth_rate,
                AVG(score) as avg_score,
                COUNT(*) as frequency,
                MAX(created_at) as latest_occurrence
            FROM trends 
            WHERE created_at >= NOW() - INTERVAL '%s days'
            AND status IN ('approved', 'live')
            GROUP BY keyword, category, region
            HAVING COUNT(*) > 1
            ORDER BY avg_score DESC NULLS LAST, frequency DESC
            LIMIT $1
        """ % days
        
        try:
            rows = await db.fetch(query, limit)
            return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get trending keywords: {str(e)}")
    
    async def get_trends_by_category(self, category: str, status: TrendStatus = None, limit: int = 50) -> List[TrendEntity]:
        """Get trends by category"""
        db = await self.get_db_manager()
        
        where_clauses = ["category = $1"]
        values = [category]
        
        if status:
            where_clauses.append(f"status = ${len(values) + 1}")
            values.append(status.value)
        
        query = f"""
            SELECT * FROM trends 
            WHERE {' AND '.join(where_clauses)}
            ORDER BY created_at DESC
            LIMIT ${len(values) + 1}
        """
        
        try:
            rows = await db.fetch(query, *values, limit)
            return [TrendEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get trends by category: {str(e)}")
    
    async def get_trends_by_region(self, region: str, status: TrendStatus = None, limit: int = 50) -> List[TrendEntity]:
        """Get trends by region"""
        db = await self.get_db_manager()
        
        where_clauses = ["region = $1"]
        values = [region]
        
        if status:
            where_clauses.append(f"status = ${len(values) + 1}")
            values.append(status.value)
        
        query = f"""
            SELECT * FROM trends 
            WHERE {' AND '.join(where_clauses)}
            ORDER BY created_at DESC
            LIMIT ${len(values) + 1}
        """
        
        try:
            rows = await db.fetch(query, *values, limit)
            return [TrendEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get trends by region: {str(e)}")
    
    async def get_top_scoring_trends(self, limit: int = 20, status: TrendStatus = None) -> List[TrendEntity]:
        """Get top scoring trends"""
        db = await self.get_db_manager()
        
        where_clauses = ["score IS NOT NULL"]
        values = []
        
        if status:
            where_clauses.append(f"status = ${len(values) + 1}")
            values.append(status.value)
        
        query = f"""
            SELECT * FROM trends 
            WHERE {' AND '.join(where_clauses)}
            ORDER BY score DESC
            LIMIT ${len(values) + 1}
        """
        
        try:
            rows = await db.fetch(query, *values, limit)
            return [TrendEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get top scoring trends: {str(e)}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get trend statistics"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                COUNT(*) as total_trends,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_trends,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_trends,
                COUNT(CASE WHEN status = 'live' THEN 1 END) as live_trends,
                COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_trends,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_trends,
                AVG(score) as avg_score,
                AVG(search_volume) as avg_search_volume,
                AVG(growth_rate) as avg_growth_rate
            FROM trends
        """
        
        try:
            row = await db.fetchrow(query)
            return dict(row) if row else {}
        except Exception as e:
            raise DatabaseError(f"Failed to get trend statistics: {str(e)}")


# Global repository instance
trend_repository = TrendRepository()

"""
User model and repository for user management
Handles user profiles, roles, and permissions
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from database.models.base import BaseEntity, BaseRepository
from shared.exceptions import DatabaseError


class UserRole(str, Enum):
    """User role enumeration"""
    ADMIN = "admin"
    EDITOR = "editor"
    VIEWER = "viewer"


class UserEntity(BaseEntity):
    """User entity model"""
    email: str
    role: UserRole = UserRole.VIEWER
    permissions: List[str] = Field(default_factory=list)
    is_active: bool = True
    last_login: Optional[datetime] = None
    profile_data: Dict[str, Any] = Field(default_factory=dict)
    
    @validator('email')
    def validate_email(cls, v):
        if '@' not in v:
            raise ValueError('Invalid email format')
        return v.lower()
    
    @validator('permissions')
    def validate_permissions(cls, v):
        valid_permissions = {
            'read_trends', 'write_trends', 'approve_trends',
            'read_content', 'write_content', 'regenerate_content',
            'read_deployments', 'trigger_deployments', 'cancel_deployments',
            'read_dns', 'manage_dns',
            'read_analytics', 'export_analytics',
            'read_users', 'manage_users',
            'read_system', 'manage_system', 'trigger_maintenance'
        }
        
        for permission in v:
            if permission not in valid_permissions:
                raise ValueError(f'Invalid permission: {permission}')
        
        return v


class UserCreateRequest(BaseModel):
    """Request model for creating users"""
    email: str
    role: UserRole = UserRole.VIEWER
    permissions: List[str] = Field(default_factory=list)
    is_active: bool = True
    profile_data: Dict[str, Any] = Field(default_factory=dict)


class UserUpdateRequest(BaseModel):
    """Request model for updating users"""
    email: Optional[str] = None
    role: Optional[UserRole] = None
    permissions: Optional[List[str]] = None
    is_active: Optional[bool] = None
    profile_data: Optional[Dict[str, Any]] = None


class UserRepository(BaseRepository[UserEntity]):
    """Repository for user operations"""
    
    def __init__(self):
        super().__init__('user_profiles', UserEntity)
    
    async def get_by_email(self, email: str) -> Optional[UserEntity]:
        """Get user by email address"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM user_profiles WHERE email = $1"
        
        try:
            row = await db.fetchrow(query, email.lower())
            return UserEntity(**row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get user by email: {str(e)}")
    
    async def get_users_by_role(self, role: UserRole) -> List[UserEntity]:
        """Get all users with a specific role"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM user_profiles WHERE role = $1 ORDER BY created_at DESC"
        
        try:
            rows = await db.fetch(query, role.value)
            return [UserEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get users by role: {str(e)}")
    
    async def get_active_users(self) -> List[UserEntity]:
        """Get all active users"""
        db = await self.get_db_manager()
        
        query = "SELECT * FROM user_profiles WHERE is_active = true ORDER BY last_login DESC"
        
        try:
            rows = await db.fetch(query)
            return [UserEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get active users: {str(e)}")
    
    async def update_last_login(self, user_id: str) -> bool:
        """Update user's last login timestamp"""
        db = await self.get_db_manager()
        
        query = """
            UPDATE user_profiles 
            SET last_login = NOW(), updated_at = NOW() 
            WHERE id = $1
        """
        
        try:
            result = await db.execute(query, user_id)
            return result == "UPDATE 1"
        except Exception as e:
            raise DatabaseError(f"Failed to update last login: {str(e)}")
    
    async def deactivate_user(self, user_id: str) -> bool:
        """Deactivate a user account"""
        db = await self.get_db_manager()
        
        query = """
            UPDATE user_profiles 
            SET is_active = false, updated_at = NOW() 
            WHERE id = $1
        """
        
        try:
            result = await db.execute(query, user_id)
            return result == "UPDATE 1"
        except Exception as e:
            raise DatabaseError(f"Failed to deactivate user: {str(e)}")
    
    async def activate_user(self, user_id: str) -> bool:
        """Activate a user account"""
        db = await self.get_db_manager()
        
        query = """
            UPDATE user_profiles 
            SET is_active = true, updated_at = NOW() 
            WHERE id = $1
        """
        
        try:
            result = await db.execute(query, user_id)
            return result == "UPDATE 1"
        except Exception as e:
            raise DatabaseError(f"Failed to activate user: {str(e)}")
    
    async def update_permissions(self, user_id: str, permissions: List[str]) -> bool:
        """Update user permissions"""
        db = await self.get_db_manager()
        
        query = """
            UPDATE user_profiles 
            SET permissions = $1, updated_at = NOW() 
            WHERE id = $2
        """
        
        try:
            result = await db.execute(query, permissions, user_id)
            return result == "UPDATE 1"
        except Exception as e:
            raise DatabaseError(f"Failed to update permissions: {str(e)}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get user statistics"""
        db = await self.get_db_manager()
        
        query = """
            SELECT 
                COUNT(*) as total_users,
                COUNT(*) FILTER (WHERE is_active = true) as active_users,
                COUNT(*) FILTER (WHERE role = 'admin') as admin_users,
                COUNT(*) FILTER (WHERE role = 'editor') as editor_users,
                COUNT(*) FILTER (WHERE role = 'viewer') as viewer_users,
                COUNT(*) FILTER (WHERE last_login > NOW() - INTERVAL '30 days') as recent_logins
            FROM user_profiles
        """
        
        try:
            row = await db.fetchrow(query)
            return dict(row) if row else {}
        except Exception as e:
            raise DatabaseError(f"Failed to get user statistics: {str(e)}")
    
    async def search_users(self, search_term: str, limit: int = 50) -> List[UserEntity]:
        """Search users by email or profile data"""
        db = await self.get_db_manager()
        
        query = """
            SELECT * FROM user_profiles 
            WHERE email ILIKE $1 
            OR profile_data::text ILIKE $1
            ORDER BY created_at DESC
            LIMIT $2
        """
        
        try:
            search_pattern = f"%{search_term}%"
            rows = await db.fetch(query, search_pattern, limit)
            return [UserEntity(**row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to search users: {str(e)}")


# Global repository instance
user_repository = UserRepository()


def get_default_permissions_for_role(role: UserRole) -> List[str]:
    """Get default permissions for a user role"""
    permissions_map = {
        UserRole.ADMIN: [
            'read_trends', 'write_trends', 'approve_trends',
            'read_content', 'write_content', 'regenerate_content',
            'read_deployments', 'trigger_deployments', 'cancel_deployments',
            'read_dns', 'manage_dns',
            'read_analytics', 'export_analytics',
            'read_users', 'manage_users',
            'read_system', 'manage_system', 'trigger_maintenance'
        ],
        UserRole.EDITOR: [
            'read_trends', 'write_trends', 'approve_trends',
            'read_content', 'write_content', 'regenerate_content',
            'read_deployments', 'trigger_deployments',
            'read_dns',
            'read_analytics', 'export_analytics'
        ],
        UserRole.VIEWER: [
            'read_trends',
            'read_content',
            'read_deployments',
            'read_dns',
            'read_analytics'
        ]
    }
    
    return permissions_map.get(role, [])


def has_permission(user: UserEntity, permission: str) -> bool:
    """Check if user has a specific permission"""
    if user.role == UserRole.ADMIN:
        return True  # Admins have all permissions
    
    return permission in user.permissions


def can_access_resource(user: UserEntity, resource: str, action: str) -> bool:
    """Check if user can perform an action on a resource"""
    permission = f"{action}_{resource}"
    return has_permission(user, permission)

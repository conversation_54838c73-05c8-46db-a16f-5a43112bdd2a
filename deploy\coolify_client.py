"""
Coolify API client for deployment orchestration
Handles site deployment, monitoring, and management through Coolify
"""
import asyncio
import aiohttp
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from shared.config import settings, DEPLOY_CONFIG
from shared.utils import AsyncHTTPClient
from shared.exceptions import DeploymentError
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics

logger = get_logger('deploy.coolify')


class CoolifyClient:
    """Client for interacting with Coolify API"""
    
    def __init__(self):
        self.api_url = settings.coolify_api_url
        self.api_token = settings.coolify_api_token
        self.team_id = settings.coolify_team_id
        self.headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    async def create_application(self, app_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new application in Coolify"""
        try:
            payload = {
                'name': app_config['name'],
                'description': app_config.get('description', ''),
                'git_repository': app_config['git_repository'],
                'git_branch': app_config.get('git_branch', 'main'),
                'build_pack': app_config.get('build_pack', 'nixpacks'),
                'environment_variables': app_config.get('environment_variables', {}),
                'domains': app_config.get('domains', []),
                'team_id': self.team_id
            }
            
            async with AsyncHTTPClient() as client:
                response = await client.post(
                    f"{self.api_url}/api/v1/applications",
                    json=payload,
                    headers=self.headers
                )
                
                if response['status'] not in [200, 201]:
                    raise DeploymentError(f"Failed to create application: {response['status']}")
                
                app_data = response['data']
                
                logger.info(
                    f"Created Coolify application: {app_config['name']}",
                    app_name=app_config['name'],
                    app_uuid=app_data.get('uuid')
                )
                
                return app_data
                
        except Exception as e:
            logger.error(f"Failed to create Coolify application: {str(e)}")
            raise DeploymentError(f"Application creation failed: {str(e)}")
    
    async def deploy_application(self, app_uuid: str, force_rebuild: bool = False) -> Dict[str, Any]:
        """Deploy an application"""
        try:
            payload = {
                'force_rebuild': force_rebuild
            }
            
            async with AsyncHTTPClient() as client:
                response = await client.post(
                    f"{self.api_url}/api/v1/applications/{app_uuid}/deploy",
                    json=payload,
                    headers=self.headers
                )
                
                if response['status'] not in [200, 202]:
                    raise DeploymentError(f"Failed to deploy application: {response['status']}")
                
                deployment_data = response['data']
                
                logger.info(
                    f"Started deployment for application: {app_uuid}",
                    app_uuid=app_uuid,
                    deployment_uuid=deployment_data.get('uuid')
                )
                
                return deployment_data
                
        except Exception as e:
            logger.error(f"Failed to deploy application {app_uuid}: {str(e)}")
            raise DeploymentError(f"Deployment failed: {str(e)}")
    
    async def get_deployment_status(self, deployment_uuid: str) -> Dict[str, Any]:
        """Get deployment status"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.get(
                    f"{self.api_url}/api/v1/deployments/{deployment_uuid}",
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    raise DeploymentError(f"Failed to get deployment status: {response['status']}")
                
                return response['data']
                
        except Exception as e:
            logger.error(f"Failed to get deployment status {deployment_uuid}: {str(e)}")
            raise DeploymentError(f"Status check failed: {str(e)}")
    
    async def get_deployment_logs(self, deployment_uuid: str) -> str:
        """Get deployment logs"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.get(
                    f"{self.api_url}/api/v1/deployments/{deployment_uuid}/logs",
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    return "Failed to retrieve logs"
                
                return response['data'].get('logs', '')
                
        except Exception as e:
            logger.error(f"Failed to get deployment logs {deployment_uuid}: {str(e)}")
            return f"Error retrieving logs: {str(e)}"
    
    async def update_environment_variables(self, app_uuid: str, env_vars: Dict[str, str]) -> bool:
        """Update application environment variables"""
        try:
            payload = {
                'environment_variables': env_vars
            }
            
            async with AsyncHTTPClient() as client:
                response = await client.patch(
                    f"{self.api_url}/api/v1/applications/{app_uuid}/environment",
                    json=payload,
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    raise DeploymentError(f"Failed to update environment variables: {response['status']}")
                
                logger.info(f"Updated environment variables for application: {app_uuid}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update environment variables for {app_uuid}: {str(e)}")
            return False
    
    async def delete_application(self, app_uuid: str) -> bool:
        """Delete an application"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.delete(
                    f"{self.api_url}/api/v1/applications/{app_uuid}",
                    headers=self.headers
                )
                
                if response['status'] not in [200, 204]:
                    raise DeploymentError(f"Failed to delete application: {response['status']}")
                
                logger.info(f"Deleted Coolify application: {app_uuid}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to delete application {app_uuid}: {str(e)}")
            return False
    
    async def get_application_info(self, app_uuid: str) -> Optional[Dict[str, Any]]:
        """Get application information"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.get(
                    f"{self.api_url}/api/v1/applications/{app_uuid}",
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    return None
                
                return response['data']
                
        except Exception as e:
            logger.error(f"Failed to get application info {app_uuid}: {str(e)}")
            return None
    
    async def list_applications(self) -> List[Dict[str, Any]]:
        """List all applications"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.get(
                    f"{self.api_url}/api/v1/applications",
                    headers=self.headers,
                    params={'team_id': self.team_id}
                )
                
                if response['status'] != 200:
                    raise DeploymentError(f"Failed to list applications: {response['status']}")
                
                return response['data'].get('applications', [])
                
        except Exception as e:
            logger.error(f"Failed to list applications: {str(e)}")
            return []


class SiteGenerator:
    """Generates static sites for trends"""
    
    def __init__(self):
        self.logger = logger
    
    def generate_next_js_site(self, trend_data: Dict[str, Any], content_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate Next.js site files for a trend"""
        
        # Generate package.json
        package_json = {
            "name": f"trend-{trend_data['slug']}",
            "version": "1.0.0",
            "private": True,
            "scripts": {
                "dev": "next dev",
                "build": "next build",
                "start": "next start",
                "lint": "next lint"
            },
            "dependencies": {
                "next": "14.0.0",
                "react": "^18",
                "react-dom": "^18"
            },
            "devDependencies": {
                "@types/node": "^20",
                "@types/react": "^18",
                "@types/react-dom": "^18",
                "eslint": "^8",
                "eslint-config-next": "14.0.0",
                "typescript": "^5"
            }
        }
        
        # Generate main page component
        page_tsx = f'''import Head from 'next/head'
import {{ GetStaticProps }} from 'next'

interface PageProps {{
  trend: {{
    keyword: string
    category: string
    region: string
  }}
  content: {{
    title: string
    description: string
    body: string
    heroImageUrl?: string
    codeSnippet?: string
    codeLanguage?: string
  }}
}}

export default function TrendPage({{ trend, content }}: PageProps) {{
  return (
    <>
      <Head>
        <title>{{content.title}}</title>
        <meta name="description" content={{content.description}} />
        <meta name="keywords" content="{trend_data['keyword']}, {trend_data['category']}, trending" />
        <meta property="og:title" content={{content.title}} />
        <meta property="og:description" content={{content.description}} />
        <meta property="og:type" content="article" />
        {{content.heroImageUrl && (
          <meta property="og:image" content={{content.heroImageUrl}} />
        )}}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <header className="mb-8">
          <h1 className="text-4xl font-bold mb-4">{{content.title}}</h1>
          <p className="text-xl text-gray-600 mb-4">{{content.description}}</p>
          <div className="flex gap-4 text-sm text-gray-500">
            <span>Category: {{trend.category}}</span>
            <span>Region: {{trend.region}}</span>
            <span>Trending: {{trend.keyword}}</span>
          </div>
        </header>

        {{content.heroImageUrl && (
          <div className="mb-8">
            <img 
              src={{content.heroImageUrl}} 
              alt="Hero image" 
              className="w-full h-64 object-cover rounded-lg"
            />
          </div>
        )}}

        <article className="prose prose-lg max-w-none">
          <div dangerouslySetInnerHTML={{{{ __html: content.body }}}} />
        </article>

        {{content.codeSnippet && (
          <div className="mt-8">
            <h3 className="text-xl font-semibold mb-4">Code Example</h3>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto">
              <code className={{`language-${{content.codeLanguage || 'text'}}`}}>
                {{content.codeSnippet}}
              </code>
            </pre>
          </div>
        )}}

        <footer className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-gray-600 text-center">
            This content was generated based on trending topic: <strong>{{trend.keyword}}</strong>
          </p>
        </footer>
      </main>
    </>
  )
}}

export const getStaticProps: GetStaticProps = async () => {{
  return {{
    props: {{
      trend: {json.dumps(trend_data)},
      content: {json.dumps(content_data)}
    }}
  }}
}}'''
        
        # Generate Tailwind CSS config
        tailwind_config = '''/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}'''
        
        # Generate Next.js config
        next_config = '''/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}

module.exports = nextConfig'''
        
        return {
            'package.json': json.dumps(package_json, indent=2),
            'pages/index.tsx': page_tsx,
            'tailwind.config.js': tailwind_config,
            'next.config.js': next_config,
            'styles/globals.css': '@tailwind base;\n@tailwind components;\n@tailwind utilities;'
        }


# Global client instance
coolify_client = CoolifyClient()
site_generator = SiteGenerator()

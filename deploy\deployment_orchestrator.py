"""
Deployment orchestration system
Manages the complete deployment workflow from content to live site
"""
import asyncio
import tempfile
import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from shared.config import settings, DEPLOY_CONFIG
from shared.utils import generate_slug
from shared.exceptions import DeploymentError
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from deploy.coolify_client import coolify_client, site_generator
from database.models.deployment_model import DeploymentRepository, DeploymentStatus
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository

logger = get_logger('deploy.orchestrator')


class DeploymentOrchestrator:
    """Orchestrates the complete deployment process"""
    
    def __init__(self):
        self.coolify = coolify_client
        self.site_gen = site_generator
        self.deployment_repo = DeploymentRepository()
        self.trend_repo = TrendRepository()
        self.content_repo = ContentRepository()
        self.logger = logger
        self.metrics = app_metrics
    
    async def deploy_trend_site(self, trend_id: str, content_id: str) -> Dict[str, Any]:
        """Deploy a complete site for a trend"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Get trend and content data
            trend = await self.trend_repo.get_by_id(trend_id)
            content = await self.content_repo.get_by_id(content_id)
            
            if not trend or not content:
                raise DeploymentError("Trend or content not found")
            
            self.logger.info(
                f"Starting deployment for trend: {trend.keyword}",
                trend_id=trend_id,
                content_id=content_id,
                keyword=trend.keyword
            )
            
            # Create deployment record
            deployment_data = {
                'trend_id': trend_id,
                'content_id': content_id,
                'status': DeploymentStatus.PENDING.value,
                'progress': 0
            }
            
            deployment_id = await self.deployment_repo.create(deployment_data)
            
            try:
                # Update status to building
                await self.deployment_repo.update(deployment_id, {
                    'status': DeploymentStatus.BUILDING.value,
                    'progress': 10
                })
                
                # Generate site files
                site_files = await self._generate_site_files(trend, content)
                
                await self.deployment_repo.update(deployment_id, {'progress': 30})
                
                # Create Git repository with files
                repo_url = await self._create_git_repository(trend, site_files)
                
                await self.deployment_repo.update(deployment_id, {'progress': 50})
                
                # Create Coolify application
                app_config = self._prepare_app_config(trend, repo_url)
                coolify_app = await self.coolify.create_application(app_config)
                
                await self.deployment_repo.update(deployment_id, {
                    'progress': 70,
                    'coolify_deployment_uuid': coolify_app['uuid']
                })
                
                # Deploy application
                deployment_result = await self.coolify.deploy_application(coolify_app['uuid'])
                
                await self.deployment_repo.update(deployment_id, {'progress': 80})
                
                # Wait for deployment completion
                deploy_url = await self._wait_for_deployment(
                    deployment_result['uuid'], 
                    deployment_id
                )
                
                # Update deployment as successful
                await self.deployment_repo.update(deployment_id, {
                    'status': DeploymentStatus.SUCCESS.value,
                    'progress': 100,
                    'deploy_url': deploy_url,
                    'deployed_at': datetime.utcnow()
                })
                
                # Mark trend as live
                await self.trend_repo.mark_as_live(trend_id, deploy_url)
                
                duration = asyncio.get_event_loop().time() - start_time
                
                # Record metrics
                self.metrics.record_deployment('success', duration)
                
                self.logger.info(
                    f"Deployment completed successfully: {trend.keyword}",
                    trend_id=trend_id,
                    deployment_id=deployment_id,
                    deploy_url=deploy_url,
                    duration=duration
                )
                
                return {
                    'deployment_id': deployment_id,
                    'status': 'success',
                    'deploy_url': deploy_url,
                    'duration': duration
                }
                
            except Exception as e:
                # Update deployment as failed
                await self.deployment_repo.update(deployment_id, {
                    'status': DeploymentStatus.FAILED.value,
                    'error_message': str(e)
                })
                raise
                
        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            self.metrics.record_deployment('failed', duration)
            
            self.logger.error(
                f"Deployment failed for trend: {trend.keyword if 'trend' in locals() else 'unknown'} - {str(e)}",
                trend_id=trend_id,
                duration=duration,
                error=str(e)
            )
            
            raise DeploymentError(f"Deployment failed: {str(e)}")
    
    async def _generate_site_files(self, trend, content) -> Dict[str, str]:
        """Generate all site files"""
        try:
            # Prepare data for site generation
            trend_data = {
                'keyword': trend.keyword,
                'slug': trend.slug,
                'category': trend.category,
                'region': trend.region
            }
            
            content_data = {
                'title': content.title,
                'description': content.description,
                'body': content.body,
                'heroImageUrl': content.hero_image_url,
                'codeSnippet': content.code_snippet,
                'codeLanguage': content.code_language
            }
            
            # Generate Next.js site
            site_files = self.site_gen.generate_next_js_site(trend_data, content_data)
            
            # Add additional files
            site_files.update({
                'README.md': f'# {content.title}\n\nGenerated site for trending topic: {trend.keyword}',
                '.gitignore': 'node_modules/\n.next/\nout/\n.env.local\n',
                'public/favicon.ico': '',  # Would include actual favicon
            })
            
            return site_files
            
        except Exception as e:
            raise DeploymentError(f"Site generation failed: {str(e)}")
    
    async def _create_git_repository(self, trend, site_files: Dict[str, str]) -> str:
        """Create Git repository with site files"""
        try:
            # In a real implementation, this would:
            # 1. Create a new GitHub repository
            # 2. Push the generated files
            # 3. Return the repository URL
            
            # For now, return a mock repository URL
            repo_name = f"trend-{trend.slug}"
            repo_url = f"https://github.com/trend-platform/{repo_name}.git"
            
            self.logger.info(f"Created Git repository: {repo_url}", trend_id=trend.id)
            
            return repo_url
            
        except Exception as e:
            raise DeploymentError(f"Git repository creation failed: {str(e)}")
    
    def _prepare_app_config(self, trend, repo_url: str) -> Dict[str, Any]:
        """Prepare Coolify application configuration"""
        return {
            'name': f"trend-{trend.slug}",
            'description': f"Site for trending topic: {trend.keyword}",
            'git_repository': repo_url,
            'git_branch': 'main',
            'build_pack': 'nixpacks',
            'environment_variables': {
                'NODE_ENV': 'production',
                'NEXT_TELEMETRY_DISABLED': '1'
            },
            'domains': [f"{trend.slug}.{settings.dns_config['domains']['trends_subdomain']}.{settings.dns_config['domains']['main_domain']}"]
        }
    
    async def _wait_for_deployment(self, deployment_uuid: str, deployment_id: str) -> str:
        """Wait for deployment to complete and return URL"""
        max_wait_time = DEPLOY_CONFIG['build']['timeout']
        check_interval = 10
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            try:
                status = await self.coolify.get_deployment_status(deployment_uuid)
                
                if status['status'] == 'success':
                    deploy_url = status.get('url', '')
                    return deploy_url
                elif status['status'] == 'failed':
                    logs = await self.coolify.get_deployment_logs(deployment_uuid)
                    await self.deployment_repo.update(deployment_id, {'build_log': logs})
                    raise DeploymentError(f"Deployment failed: {status.get('error', 'Unknown error')}")
                
                # Update progress based on status
                progress = min(90, 80 + (elapsed_time / max_wait_time) * 10)
                await self.deployment_repo.update(deployment_id, {'progress': int(progress)})
                
                await asyncio.sleep(check_interval)
                elapsed_time += check_interval
                
            except Exception as e:
                if elapsed_time > max_wait_time / 2:  # Only fail after waiting a reasonable time
                    raise DeploymentError(f"Deployment monitoring failed: {str(e)}")
                await asyncio.sleep(check_interval)
                elapsed_time += check_interval
        
        raise DeploymentError("Deployment timeout")
    
    async def deploy_ready_content(self) -> Dict[str, Any]:
        """Deploy all content that's ready for deployment"""
        try:
            # Get content ready for deployment
            ready_content = await self.content_repo.get_ready_for_deployment(limit=5)
            
            results = {
                'processed': 0,
                'successful': 0,
                'failed': 0,
                'deployments': []
            }
            
            for content_data in ready_content:
                try:
                    results['processed'] += 1
                    
                    deployment_result = await self.deploy_trend_site(
                        content_data['trend_id'],
                        content_data['id']
                    )
                    
                    results['successful'] += 1
                    results['deployments'].append(deployment_result)
                    
                    # Small delay between deployments
                    await asyncio.sleep(5)
                    
                except Exception as e:
                    results['failed'] += 1
                    self.logger.error(f"Failed to deploy content {content_data['id']}: {str(e)}")
            
            self.logger.info(
                "Batch deployment completed",
                processed=results['processed'],
                successful=results['successful'],
                failed=results['failed']
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Batch deployment failed: {str(e)}")
            raise DeploymentError(f"Batch deployment failed: {str(e)}")
    
    async def get_deployment_status(self, deployment_id: str) -> Optional[Dict[str, Any]]:
        """Get deployment status"""
        try:
            deployment = await self.deployment_repo.get_by_id(deployment_id)
            if not deployment:
                return None
            
            result = deployment.dict()
            
            # If deployment is in progress, get live status from Coolify
            if (deployment.status in [DeploymentStatus.BUILDING, DeploymentStatus.PENDING] and 
                deployment.coolify_deployment_uuid):
                
                try:
                    coolify_status = await self.coolify.get_deployment_status(
                        deployment.coolify_deployment_uuid
                    )
                    result['coolify_status'] = coolify_status
                except Exception:
                    pass  # Continue with database status if Coolify check fails
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to get deployment status: {str(e)}")
            return None


# Global orchestrator instance
deployment_orchestrator = DeploymentOrchestrator()

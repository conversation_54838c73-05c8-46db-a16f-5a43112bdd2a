"""
Deployment hooks system for pre/post deployment actions
Handles webhook processing, notifications, and custom deployment logic
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Callable, Awaitable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import aiohttp

from monitoring.logger import get_logger
from database.models.deployment_model import DeploymentRepository, DeploymentStatus
from database.models.dns_model import DNSRepository
from dns.dns_manager import DNSManager
from shared.exceptions import DeploymentError


class HookType(str, Enum):
    """Hook type enumeration"""
    PRE_DEPLOYMENT = "pre_deployment"
    POST_DEPLOYMENT = "post_deployment"
    DEPLOYMENT_SUCCESS = "deployment_success"
    DEPLOYMENT_FAILURE = "deployment_failure"
    HEALTH_CHECK = "health_check"


class HookStatus(str, Enum):
    """Hook execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class HookContext:
    """Context information passed to hooks"""
    deployment_id: str
    trend_id: str
    content_id: Optional[str]
    app_name: str
    app_url: Optional[str]
    deployment_status: DeploymentStatus
    metadata: Dict[str, Any]
    started_at: datetime
    completed_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'deployment_id': self.deployment_id,
            'trend_id': self.trend_id,
            'content_id': self.content_id,
            'app_name': self.app_name,
            'app_url': self.app_url,
            'deployment_status': self.deployment_status,
            'metadata': self.metadata,
            'started_at': self.started_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }


@dataclass
class HookResult:
    """Result of hook execution"""
    status: HookStatus
    message: Optional[str] = None
    data: Dict[str, Any] = None
    execution_time: Optional[float] = None
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}


class DeploymentHook:
    """Base class for deployment hooks"""
    
    def __init__(self, name: str, hook_type: HookType, priority: int = 100):
        self.name = name
        self.hook_type = hook_type
        self.priority = priority  # Lower numbers execute first
        self.logger = get_logger(f'deploy.hooks.{name}')
    
    async def execute(self, context: HookContext) -> HookResult:
        """Execute the hook"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            self.logger.info(f"Executing hook: {self.name}", deployment_id=context.deployment_id)
            
            result = await self._execute_hook(context)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            result.execution_time = execution_time
            
            if result.status == HookStatus.COMPLETED:
                self.logger.info(
                    f"Hook completed successfully: {self.name}",
                    deployment_id=context.deployment_id,
                    execution_time=execution_time
                )
            else:
                self.logger.warning(
                    f"Hook failed: {self.name} - {result.error}",
                    deployment_id=context.deployment_id,
                    execution_time=execution_time
                )
            
            return result
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(
                f"Hook execution error: {self.name} - {str(e)}",
                deployment_id=context.deployment_id,
                execution_time=execution_time
            )
            
            return HookResult(
                status=HookStatus.FAILED,
                error=str(e),
                execution_time=execution_time
            )
    
    async def _execute_hook(self, context: HookContext) -> HookResult:
        """Override this method to implement hook logic"""
        return HookResult(status=HookStatus.SKIPPED, message="Hook not implemented")


class DNSSetupHook(DeploymentHook):
    """Hook to set up DNS records for deployed applications"""
    
    def __init__(self):
        super().__init__("dns_setup", HookType.POST_DEPLOYMENT, priority=10)
        self.dns_manager = DNSManager()
        self.dns_repo = DNSRepository()
    
    async def _execute_hook(self, context: HookContext) -> HookResult:
        """Set up DNS record for the deployed application"""
        try:
            if not context.app_url:
                return HookResult(
                    status=HookStatus.SKIPPED,
                    message="No app URL available for DNS setup"
                )
            
            # Generate subdomain for the trend
            from database.models.trend_model import TrendRepository
            trend_repo = TrendRepository()
            trend = await trend_repo.get_by_id(context.trend_id)
            
            if not trend:
                return HookResult(
                    status=HookStatus.FAILED,
                    error="Trend not found for DNS setup"
                )
            
            subdomain = f"{trend.slug}.trends.yourdomain.com"
            
            # Create DNS record
            dns_result = await self.dns_manager.create_trend_dns(
                trend_id=context.trend_id,
                subdomain=subdomain,
                target_url=context.app_url,
                ttl=3600,
                proxied=True
            )
            
            if dns_result['success']:
                # Save DNS record to database
                dns_data = {
                    'trend_id': context.trend_id,
                    'subdomain': subdomain,
                    'record_type': 'CNAME',
                    'record_value': context.app_url,
                    'status': 'active',
                    'cloudflare_record_id': dns_result.get('record_id'),
                    'ttl': 3600,
                    'proxied': True,
                    'dns_metadata': dns_result.get('metadata', {})
                }
                
                await self.dns_repo.create(dns_data)
                
                return HookResult(
                    status=HookStatus.COMPLETED,
                    message=f"DNS record created: {subdomain}",
                    data={
                        'subdomain': subdomain,
                        'record_id': dns_result.get('record_id')
                    }
                )
            else:
                return HookResult(
                    status=HookStatus.FAILED,
                    error=f"DNS creation failed: {dns_result.get('error')}"
                )
                
        except Exception as e:
            return HookResult(
                status=HookStatus.FAILED,
                error=f"DNS setup error: {str(e)}"
            )


class HealthCheckHook(DeploymentHook):
    """Hook to perform health checks on deployed applications"""
    
    def __init__(self):
        super().__init__("health_check", HookType.POST_DEPLOYMENT, priority=20)
        self.max_attempts = 5
        self.check_interval = 10  # seconds
    
    async def _execute_hook(self, context: HookContext) -> HookResult:
        """Perform health check on the deployed application"""
        try:
            if not context.app_url:
                return HookResult(
                    status=HookStatus.SKIPPED,
                    message="No app URL available for health check"
                )
            
            # Perform multiple health check attempts
            for attempt in range(self.max_attempts):
                try:
                    start_time = asyncio.get_event_loop().time()
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            context.app_url,
                            timeout=aiohttp.ClientTimeout(total=30)
                        ) as response:
                            response_time = asyncio.get_event_loop().time() - start_time
                            
                            if response.status == 200:
                                return HookResult(
                                    status=HookStatus.COMPLETED,
                                    message=f"Health check passed (attempt {attempt + 1})",
                                    data={
                                        'status_code': response.status,
                                        'response_time': response_time,
                                        'attempts': attempt + 1
                                    }
                                )
                
                except Exception as e:
                    self.logger.warning(
                        f"Health check attempt {attempt + 1} failed: {str(e)}",
                        deployment_id=context.deployment_id
                    )
                
                # Wait before next attempt (except for last attempt)
                if attempt < self.max_attempts - 1:
                    await asyncio.sleep(self.check_interval)
            
            return HookResult(
                status=HookStatus.FAILED,
                error=f"Health check failed after {self.max_attempts} attempts"
            )
            
        except Exception as e:
            return HookResult(
                status=HookStatus.FAILED,
                error=f"Health check error: {str(e)}"
            )


class NotificationHook(DeploymentHook):
    """Hook to send deployment notifications"""
    
    def __init__(self, webhook_url: Optional[str] = None):
        super().__init__("notification", HookType.DEPLOYMENT_SUCCESS, priority=90)
        self.webhook_url = webhook_url
    
    async def _execute_hook(self, context: HookContext) -> HookResult:
        """Send deployment notification"""
        try:
            # Prepare notification data
            notification_data = {
                'event': 'deployment_completed',
                'deployment': context.to_dict(),
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Send webhook notification if configured
            if self.webhook_url:
                await self._send_webhook(notification_data)
            
            # Log notification
            self.logger.info(
                f"Deployment completed for trend: {context.trend_id}",
                deployment_id=context.deployment_id,
                app_url=context.app_url
            )
            
            return HookResult(
                status=HookStatus.COMPLETED,
                message="Notification sent successfully",
                data=notification_data
            )
            
        except Exception as e:
            return HookResult(
                status=HookStatus.FAILED,
                error=f"Notification error: {str(e)}"
            )
    
    async def _send_webhook(self, data: Dict[str, Any]):
        """Send webhook notification"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status >= 400:
                        self.logger.warning(f"Webhook returned HTTP {response.status}")
        except Exception as e:
            self.logger.error(f"Webhook send failed: {str(e)}")


class AnalyticsTrackingHook(DeploymentHook):
    """Hook to track deployment analytics"""
    
    def __init__(self):
        super().__init__("analytics_tracking", HookType.POST_DEPLOYMENT, priority=80)
    
    async def _execute_hook(self, context: HookContext) -> HookResult:
        """Track deployment analytics"""
        try:
            from database.models.analytics_model import AnalyticsRepository
            analytics_repo = AnalyticsRepository()
            
            # Record deployment event
            await analytics_repo.record_system_event(
                event_name="deployment_completed",
                event_data={
                    'deployment_id': context.deployment_id,
                    'trend_id': context.trend_id,
                    'app_name': context.app_name,
                    'app_url': context.app_url,
                    'deployment_duration': (
                        context.completed_at - context.started_at
                    ).total_seconds() if context.completed_at else None
                },
                metrics={
                    'deployment_count': 1,
                    'deployment_duration': (
                        context.completed_at - context.started_at
                    ).total_seconds() if context.completed_at else 0
                }
            )
            
            return HookResult(
                status=HookStatus.COMPLETED,
                message="Analytics tracking completed"
            )
            
        except Exception as e:
            return HookResult(
                status=HookStatus.FAILED,
                error=f"Analytics tracking error: {str(e)}"
            )


class DeploymentHookManager:
    """Manages deployment hooks and their execution"""
    
    def __init__(self):
        self.hooks: Dict[HookType, List[DeploymentHook]] = {
            hook_type: [] for hook_type in HookType
        }
        self.logger = get_logger('deploy.hook_manager')
    
    def register_hook(self, hook: DeploymentHook):
        """Register a deployment hook"""
        self.hooks[hook.hook_type].append(hook)
        # Sort by priority (lower numbers first)
        self.hooks[hook.hook_type].sort(key=lambda h: h.priority)
        
        self.logger.info(f"Registered hook: {hook.name} ({hook.hook_type})")
    
    def unregister_hook(self, hook_name: str, hook_type: HookType):
        """Unregister a deployment hook"""
        self.hooks[hook_type] = [
            h for h in self.hooks[hook_type] if h.name != hook_name
        ]
        self.logger.info(f"Unregistered hook: {hook_name} ({hook_type})")
    
    async def execute_hooks(
        self, 
        hook_type: HookType, 
        context: HookContext
    ) -> List[HookResult]:
        """Execute all hooks of a specific type"""
        hooks = self.hooks.get(hook_type, [])
        
        if not hooks:
            return []
        
        self.logger.info(
            f"Executing {len(hooks)} {hook_type} hooks",
            deployment_id=context.deployment_id
        )
        
        results = []
        
        for hook in hooks:
            try:
                result = await hook.execute(context)
                results.append(result)
                
                # Stop execution if a critical hook fails
                if result.status == HookStatus.FAILED and hook.priority < 50:
                    self.logger.error(
                        f"Critical hook failed, stopping execution: {hook.name}",
                        deployment_id=context.deployment_id
                    )
                    break
                    
            except Exception as e:
                self.logger.error(
                    f"Hook execution error: {hook.name} - {str(e)}",
                    deployment_id=context.deployment_id
                )
                results.append(HookResult(
                    status=HookStatus.FAILED,
                    error=str(e)
                ))
        
        return results
    
    def get_registered_hooks(self) -> Dict[str, List[str]]:
        """Get list of registered hooks by type"""
        return {
            hook_type.value: [hook.name for hook in hooks]
            for hook_type, hooks in self.hooks.items()
        }


# Global hook manager instance
deployment_hook_manager = DeploymentHookManager()

# Register default hooks
deployment_hook_manager.register_hook(DNSSetupHook())
deployment_hook_manager.register_hook(HealthCheckHook())
deployment_hook_manager.register_hook(NotificationHook())
deployment_hook_manager.register_hook(AnalyticsTrackingHook())

"""
Deployment monitoring system
Tracks deployment health, performance, and provides alerting
"""
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import aiohttp

from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from database.models.deployment_model import DeploymentRepository, DeploymentStatus
from database.models.analytics_model import AnalyticsRepository
from shared.exceptions import MonitoringError


class AlertSeverity(str, Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MonitoringStatus(str, Enum):
    """Monitoring status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthMetrics:
    """Health metrics for a deployment"""
    status_code: Optional[int] = None
    response_time: Optional[float] = None
    uptime_percentage: Optional[float] = None
    error_rate: Optional[float] = None
    last_check: Optional[datetime] = None
    consecutive_failures: int = 0
    total_checks: int = 0
    successful_checks: int = 0


@dataclass
class DeploymentAlert:
    """Deployment alert information"""
    deployment_id: str
    app_name: str
    severity: AlertSeverity
    message: str
    timestamp: datetime
    resolved: bool = False
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class DeploymentMonitor:
    """Monitors individual deployment health and performance"""
    
    def __init__(self, deployment_id: str, app_url: str, config: Dict[str, Any] = None):
        self.deployment_id = deployment_id
        self.app_url = app_url
        self.config = config or {}
        self.logger = get_logger(f'deploy.monitor.{deployment_id}')
        
        # Configuration
        self.check_interval = self.config.get('check_interval', 60)  # seconds
        self.timeout = self.config.get('timeout', 30)
        self.max_consecutive_failures = self.config.get('max_consecutive_failures', 3)
        self.alert_threshold = self.config.get('alert_threshold', 5000)  # ms
        
        # State
        self.metrics = HealthMetrics()
        self.is_monitoring = False
        self._monitor_task = None
        self._alerts: List[DeploymentAlert] = []
    
    async def start_monitoring(self):
        """Start monitoring the deployment"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self._monitor_task = asyncio.create_task(self._monitoring_loop())
        
        self.logger.info(f"Started monitoring deployment: {self.deployment_id}")
    
    async def stop_monitoring(self):
        """Stop monitoring the deployment"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            self._monitor_task = None
        
        self.logger.info(f"Stopped monitoring deployment: {self.deployment_id}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Monitoring error: {str(e)}")
                await asyncio.sleep(self.check_interval)
    
    async def _perform_health_check(self):
        """Perform health check on the deployment"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.app_url,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    response_time = (asyncio.get_event_loop().time() - start_time) * 1000  # ms
                    
                    # Update metrics
                    self.metrics.status_code = response.status
                    self.metrics.response_time = response_time
                    self.metrics.last_check = datetime.utcnow()
                    self.metrics.total_checks += 1
                    
                    if response.status == 200:
                        self.metrics.successful_checks += 1
                        self.metrics.consecutive_failures = 0
                        
                        # Check for performance alerts
                        if response_time > self.alert_threshold:
                            await self._create_alert(
                                AlertSeverity.WARNING,
                                f"High response time: {response_time:.0f}ms",
                                {'response_time': response_time}
                            )
                    else:
                        self.metrics.consecutive_failures += 1
                        
                        await self._create_alert(
                            AlertSeverity.ERROR,
                            f"HTTP {response.status} error",
                            {'status_code': response.status}
                        )
                    
                    # Update uptime percentage
                    self.metrics.uptime_percentage = (
                        self.metrics.successful_checks / self.metrics.total_checks * 100
                        if self.metrics.total_checks > 0 else 0
                    )
                    
                    # Calculate error rate
                    failed_checks = self.metrics.total_checks - self.metrics.successful_checks
                    self.metrics.error_rate = (
                        failed_checks / self.metrics.total_checks * 100
                        if self.metrics.total_checks > 0 else 0
                    )
                    
                    # Record metrics
                    app_metrics.record_deployment_health_check(
                        deployment_id=self.deployment_id,
                        status_code=response.status,
                        response_time=response_time,
                        success=response.status == 200
                    )
                    
                    # Check for consecutive failure alerts
                    if self.metrics.consecutive_failures >= self.max_consecutive_failures:
                        await self._create_alert(
                            AlertSeverity.CRITICAL,
                            f"Deployment unhealthy: {self.metrics.consecutive_failures} consecutive failures",
                            {'consecutive_failures': self.metrics.consecutive_failures}
                        )
        
        except asyncio.TimeoutError:
            self.metrics.consecutive_failures += 1
            self.metrics.total_checks += 1
            self.metrics.last_check = datetime.utcnow()
            
            await self._create_alert(
                AlertSeverity.ERROR,
                f"Health check timeout after {self.timeout}s",
                {'timeout': self.timeout}
            )
        
        except Exception as e:
            self.metrics.consecutive_failures += 1
            self.metrics.total_checks += 1
            self.metrics.last_check = datetime.utcnow()
            
            await self._create_alert(
                AlertSeverity.ERROR,
                f"Health check failed: {str(e)}",
                {'error': str(e)}
            )
    
    async def _create_alert(self, severity: AlertSeverity, message: str, metadata: Dict[str, Any] = None):
        """Create and store an alert"""
        alert = DeploymentAlert(
            deployment_id=self.deployment_id,
            app_name=f"deployment-{self.deployment_id}",
            severity=severity,
            message=message,
            timestamp=datetime.utcnow(),
            metadata=metadata or {}
        )
        
        self._alerts.append(alert)
        
        # Log alert
        log_method = {
            AlertSeverity.INFO: self.logger.info,
            AlertSeverity.WARNING: self.logger.warning,
            AlertSeverity.ERROR: self.logger.error,
            AlertSeverity.CRITICAL: self.logger.critical
        }.get(severity, self.logger.info)
        
        log_method(f"Deployment alert: {message}", deployment_id=self.deployment_id)
        
        # Record alert in analytics
        try:
            analytics_repo = AnalyticsRepository()
            await analytics_repo.record_system_event(
                event_name="deployment_alert",
                event_data={
                    'deployment_id': self.deployment_id,
                    'severity': severity,
                    'message': message,
                    'metadata': metadata or {}
                }
            )
        except Exception as e:
            self.logger.error(f"Failed to record alert in analytics: {str(e)}")
    
    def get_status(self) -> MonitoringStatus:
        """Get current monitoring status"""
        if self.metrics.total_checks == 0:
            return MonitoringStatus.UNKNOWN
        
        if self.metrics.consecutive_failures >= self.max_consecutive_failures:
            return MonitoringStatus.UNHEALTHY
        
        if self.metrics.error_rate > 20:  # More than 20% error rate
            return MonitoringStatus.DEGRADED
        
        if self.metrics.uptime_percentage < 95:  # Less than 95% uptime
            return MonitoringStatus.DEGRADED
        
        return MonitoringStatus.HEALTHY
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        return {
            'deployment_id': self.deployment_id,
            'app_url': self.app_url,
            'status': self.get_status(),
            'metrics': {
                'status_code': self.metrics.status_code,
                'response_time': self.metrics.response_time,
                'uptime_percentage': self.metrics.uptime_percentage,
                'error_rate': self.metrics.error_rate,
                'last_check': self.metrics.last_check.isoformat() if self.metrics.last_check else None,
                'consecutive_failures': self.metrics.consecutive_failures,
                'total_checks': self.metrics.total_checks,
                'successful_checks': self.metrics.successful_checks
            },
            'alerts': [
                {
                    'severity': alert.severity,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat(),
                    'resolved': alert.resolved
                }
                for alert in self._alerts[-10:]  # Last 10 alerts
            ]
        }


class DeploymentMonitoringManager:
    """Manages monitoring for all deployments"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = get_logger('deploy.monitoring_manager')
        
        # Active monitors
        self.monitors: Dict[str, DeploymentMonitor] = {}
        
        # Configuration
        self.cleanup_interval = self.config.get('cleanup_interval', 3600)  # 1 hour
        self.max_monitor_age = self.config.get('max_monitor_age', 86400)  # 24 hours
        
        # Background tasks
        self._cleanup_task = None
        self._is_running = False
    
    async def start(self):
        """Start the monitoring manager"""
        if self._is_running:
            return
        
        self._is_running = True
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        # Start monitoring for existing active deployments
        await self._start_existing_monitors()
        
        self.logger.info("Deployment monitoring manager started")
    
    async def stop(self):
        """Stop the monitoring manager"""
        if not self._is_running:
            return
        
        self._is_running = False
        
        # Stop cleanup task
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Stop all monitors
        for monitor in list(self.monitors.values()):
            await monitor.stop_monitoring()
        
        self.monitors.clear()
        
        self.logger.info("Deployment monitoring manager stopped")
    
    async def add_deployment_monitor(self, deployment_id: str, app_url: str) -> bool:
        """Add monitoring for a deployment"""
        try:
            if deployment_id in self.monitors:
                self.logger.warning(f"Monitor already exists for deployment: {deployment_id}")
                return False
            
            monitor = DeploymentMonitor(deployment_id, app_url, self.config)
            await monitor.start_monitoring()
            
            self.monitors[deployment_id] = monitor
            
            self.logger.info(f"Added monitor for deployment: {deployment_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add monitor for deployment {deployment_id}: {str(e)}")
            return False
    
    async def remove_deployment_monitor(self, deployment_id: str) -> bool:
        """Remove monitoring for a deployment"""
        try:
            if deployment_id not in self.monitors:
                return False
            
            monitor = self.monitors[deployment_id]
            await monitor.stop_monitoring()
            
            del self.monitors[deployment_id]
            
            self.logger.info(f"Removed monitor for deployment: {deployment_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to remove monitor for deployment {deployment_id}: {str(e)}")
            return False
    
    async def get_deployment_status(self, deployment_id: str) -> Optional[Dict[str, Any]]:
        """Get status for a specific deployment"""
        monitor = self.monitors.get(deployment_id)
        return monitor.get_metrics() if monitor else None
    
    async def get_all_deployment_status(self) -> Dict[str, Any]:
        """Get status for all monitored deployments"""
        status = {
            'total_deployments': len(self.monitors),
            'healthy_deployments': 0,
            'degraded_deployments': 0,
            'unhealthy_deployments': 0,
            'unknown_deployments': 0,
            'deployments': {}
        }
        
        for deployment_id, monitor in self.monitors.items():
            metrics = monitor.get_metrics()
            status['deployments'][deployment_id] = metrics
            
            # Count by status
            monitor_status = metrics['status']
            if monitor_status == MonitoringStatus.HEALTHY:
                status['healthy_deployments'] += 1
            elif monitor_status == MonitoringStatus.DEGRADED:
                status['degraded_deployments'] += 1
            elif monitor_status == MonitoringStatus.UNHEALTHY:
                status['unhealthy_deployments'] += 1
            else:
                status['unknown_deployments'] += 1
        
        return status
    
    async def _start_existing_monitors(self):
        """Start monitoring for existing active deployments"""
        try:
            deployment_repo = DeploymentRepository()
            active_deployments = await deployment_repo.get_active_deployments()
            
            for deployment in active_deployments:
                if deployment.deploy_url:
                    await self.add_deployment_monitor(deployment.id, deployment.deploy_url)
            
            self.logger.info(f"Started monitoring for {len(active_deployments)} existing deployments")
            
        except Exception as e:
            self.logger.error(f"Failed to start existing monitors: {str(e)}")
    
    async def _cleanup_loop(self):
        """Cleanup old monitors periodically"""
        while self._is_running:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_old_monitors()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Cleanup error: {str(e)}")
    
    async def _cleanup_old_monitors(self):
        """Remove monitors for completed/failed deployments"""
        try:
            deployment_repo = DeploymentRepository()
            
            # Get list of deployments to remove monitoring for
            to_remove = []
            
            for deployment_id in self.monitors.keys():
                deployment = await deployment_repo.get_by_id(deployment_id)
                
                if not deployment:
                    to_remove.append(deployment_id)
                    continue
                
                # Remove monitoring for completed/failed deployments
                if deployment.status in [DeploymentStatus.COMPLETED, DeploymentStatus.FAILED, DeploymentStatus.CANCELLED]:
                    # Check if deployment is old enough to stop monitoring
                    if deployment.completed_at:
                        age = (datetime.utcnow() - deployment.completed_at).total_seconds()
                        if age > self.max_monitor_age:
                            to_remove.append(deployment_id)
            
            # Remove old monitors
            for deployment_id in to_remove:
                await self.remove_deployment_monitor(deployment_id)
            
            if to_remove:
                self.logger.info(f"Cleaned up {len(to_remove)} old monitors")
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup old monitors: {str(e)}")


# Global monitoring manager instance
deployment_monitoring_manager = DeploymentMonitoringManager()

"""
Base deployment provider abstraction
Provides common interface for different deployment platforms
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from monitoring.logger import get_logger
from shared.exceptions import DeploymentError


class DeploymentStatus(str, Enum):
    """Deployment status enumeration"""
    PENDING = "pending"
    BUILDING = "building"
    DEPLOYING = "deploying"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class HealthStatus(str, Enum):
    """Health check status enumeration"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class DeploymentConfig:
    """Deployment configuration"""
    app_name: str
    repository_url: str
    branch: str = "main"
    build_command: Optional[str] = None
    start_command: Optional[str] = None
    environment_variables: Dict[str, str] = None
    domain: Optional[str] = None
    ssl_enabled: bool = True
    auto_deploy: bool = True
    
    def __post_init__(self):
        if self.environment_variables is None:
            self.environment_variables = {}


@dataclass
class DeploymentResult:
    """Result of a deployment operation"""
    status: DeploymentStatus
    deployment_id: str
    app_url: Optional[str] = None
    build_logs: Optional[str] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class HealthCheckResult:
    """Result of a health check"""
    status: HealthStatus
    response_time: Optional[float] = None
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseDeploymentProvider(ABC):
    """Abstract base class for deployment providers"""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.logger = get_logger(f'deploy.{name}')
        self.timeout = self.config.get('timeout', 300)  # 5 minutes default
        self.max_retries = self.config.get('max_retries', 3)
    
    @abstractmethod
    async def deploy_application(
        self, 
        deployment_config: DeploymentConfig
    ) -> DeploymentResult:
        """
        Deploy an application
        
        Args:
            deployment_config: Configuration for the deployment
            
        Returns:
            DeploymentResult with deployment details
        """
        pass
    
    @abstractmethod
    async def get_deployment_status(self, deployment_id: str) -> DeploymentResult:
        """
        Get status of a deployment
        
        Args:
            deployment_id: ID of the deployment
            
        Returns:
            DeploymentResult with current status
        """
        pass
    
    @abstractmethod
    async def cancel_deployment(self, deployment_id: str) -> bool:
        """
        Cancel a running deployment
        
        Args:
            deployment_id: ID of the deployment to cancel
            
        Returns:
            True if cancellation was successful
        """
        pass
    
    @abstractmethod
    async def delete_application(self, app_name: str) -> bool:
        """
        Delete an application
        
        Args:
            app_name: Name of the application to delete
            
        Returns:
            True if deletion was successful
        """
        pass
    
    @abstractmethod
    async def get_application_logs(
        self, 
        app_name: str, 
        lines: int = 100
    ) -> str:
        """
        Get application logs
        
        Args:
            app_name: Name of the application
            lines: Number of log lines to retrieve
            
        Returns:
            Log content as string
        """
        pass
    
    @abstractmethod
    async def health_check(self, app_url: str) -> HealthCheckResult:
        """
        Perform health check on deployed application
        
        Args:
            app_url: URL of the deployed application
            
        Returns:
            HealthCheckResult with health status
        """
        pass
    
    @abstractmethod
    async def list_applications(self) -> List[Dict[str, Any]]:
        """
        List all applications managed by this provider
        
        Returns:
            List of application information dictionaries
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """
        Validate connection to the deployment provider
        
        Returns:
            True if connection is valid
        """
        pass
    
    @abstractmethod
    def get_supported_features(self) -> List[str]:
        """
        Get list of supported features
        
        Returns:
            List of feature names supported by this provider
        """
        pass
    
    async def _retry_on_failure(self, operation, *args, **kwargs):
        """Retry operation on failure with exponential backoff"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return await operation(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    backoff_time = 2 ** attempt
                    self.logger.warning(
                        f"Attempt {attempt + 1} failed, retrying in {backoff_time}s: {str(e)}"
                    )
                    await asyncio.sleep(backoff_time)
                else:
                    self.logger.error(f"All {self.max_retries + 1} attempts failed")
        
        raise last_exception
    
    async def _wait_for_deployment(
        self, 
        deployment_id: str, 
        timeout: Optional[int] = None
    ) -> DeploymentResult:
        """Wait for deployment to complete"""
        timeout = timeout or self.timeout
        start_time = asyncio.get_event_loop().time()
        
        while True:
            result = await self.get_deployment_status(deployment_id)
            
            if result.status in [DeploymentStatus.COMPLETED, DeploymentStatus.FAILED, DeploymentStatus.CANCELLED]:
                return result
            
            # Check timeout
            elapsed = asyncio.get_event_loop().time() - start_time
            if elapsed > timeout:
                raise DeploymentError(f"Deployment {deployment_id} timed out after {timeout}s")
            
            # Wait before next check
            await asyncio.sleep(5)
    
    def _generate_app_name(self, trend_keyword: str) -> str:
        """Generate application name from trend keyword"""
        import re
        from slugify import slugify
        
        # Create a URL-safe app name
        app_name = slugify(trend_keyword, max_length=50)
        
        # Ensure it starts with a letter
        if app_name and not app_name[0].isalpha():
            app_name = f"trend-{app_name}"
        
        # Add timestamp suffix to ensure uniqueness
        timestamp = datetime.utcnow().strftime("%m%d%H%M")
        app_name = f"{app_name}-{timestamp}"
        
        return app_name
    
    def _validate_deployment_config(self, config: DeploymentConfig) -> List[str]:
        """Validate deployment configuration"""
        issues = []
        
        if not config.app_name:
            issues.append("App name is required")
        
        if not config.repository_url:
            issues.append("Repository URL is required")
        
        # Validate app name format
        if config.app_name:
            import re
            if not re.match(r'^[a-z][a-z0-9-]*[a-z0-9]$', config.app_name):
                issues.append("App name must start with a letter, contain only lowercase letters, numbers, and hyphens, and end with a letter or number")
        
        # Validate repository URL
        if config.repository_url:
            if not (config.repository_url.startswith('https://') or config.repository_url.startswith('git@')):
                issues.append("Repository URL must be a valid Git URL")
        
        return issues
    
    async def get_provider_status(self) -> Dict[str, Any]:
        """Get provider status and statistics"""
        try:
            connection_valid = await self.validate_connection()
            applications = await self.list_applications() if connection_valid else []
            
            return {
                'name': self.name,
                'status': 'healthy' if connection_valid else 'unhealthy',
                'connection_valid': connection_valid,
                'application_count': len(applications),
                'supported_features': self.get_supported_features(),
                'config': {
                    'timeout': self.timeout,
                    'max_retries': self.max_retries
                }
            }
        except Exception as e:
            return {
                'name': self.name,
                'status': 'error',
                'error': str(e),
                'connection_valid': False
            }
    
    async def cleanup(self):
        """Cleanup resources"""
        self.logger.info(f"Provider {self.name} cleaned up")
    
    def __str__(self):
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self):
        return f"{self.__class__.__name__}(name='{self.name}', config={self.config})"


class DeploymentProviderRegistry:
    """Registry for managing multiple deployment providers"""
    
    def __init__(self):
        self._providers: Dict[str, BaseDeploymentProvider] = {}
        self.logger = get_logger('deploy.registry')
    
    def register(self, provider: BaseDeploymentProvider):
        """Register a deployment provider"""
        self._providers[provider.name] = provider
        self.logger.info(f"Registered deployment provider: {provider.name}")
    
    def unregister(self, name: str):
        """Unregister a deployment provider"""
        if name in self._providers:
            del self._providers[name]
            self.logger.info(f"Unregistered deployment provider: {name}")
    
    def get_provider(self, name: str) -> Optional[BaseDeploymentProvider]:
        """Get provider by name"""
        return self._providers.get(name)
    
    def get_all_providers(self) -> List[BaseDeploymentProvider]:
        """Get all registered providers"""
        return list(self._providers.values())
    
    def get_provider_names(self) -> List[str]:
        """Get names of all registered providers"""
        return list(self._providers.keys())
    
    def get_default_provider(self) -> Optional[BaseDeploymentProvider]:
        """Get the default provider (first available)"""
        providers = self.get_all_providers()
        return providers[0] if providers else None
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all providers"""
        status = {
            'total_providers': len(self._providers),
            'providers': {}
        }
        
        for name, provider in self._providers.items():
            try:
                provider_status = await provider.get_provider_status()
                status['providers'][name] = provider_status
            except Exception as e:
                status['providers'][name] = {
                    'name': name,
                    'status': 'error',
                    'error': str(e)
                }
        
        return status
    
    async def cleanup_all(self):
        """Cleanup all providers"""
        for provider in self._providers.values():
            try:
                await provider.cleanup()
            except Exception as e:
                self.logger.error(f"Error cleaning up provider {provider.name}: {str(e)}")


# Global deployment provider registry
deployment_provider_registry = DeploymentProviderRegistry()

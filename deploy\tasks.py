"""
Celery tasks for deployment operations
"""
from typing import Dict, Any
from shared.celery_app import celery_app
from deploy.deployment_orchestrator import deployment_orchestrator
from monitoring.logger import get_logger, set_correlation_id
from monitoring.metrics import app_metrics

logger = get_logger('deploy.tasks')


@celery_app.task(bind=True, max_retries=2)
def deploy_site(self, trend_id: str, content_id: str, correlation_id: str = None):
    """
    Celery task to deploy a site for a trend
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info(
        f"Starting site deployment task",
        task_id=self.request.id,
        trend_id=trend_id,
        content_id=content_id
    )
    
    try:
        # Run the deployment
        import asyncio
        result = asyncio.run(deployment_orchestrator.deploy_trend_site(trend_id, content_id))
        
        # Record metrics
        app_metrics.record_celery_task('deploy_site', 'success')
        
        logger.info(
            f"Site deployment completed successfully",
            task_id=self.request.id,
            trend_id=trend_id,
            deployment_id=result['deployment_id'],
            deploy_url=result['deploy_url']
        )
        
        return result
        
    except Exception as exc:
        logger.error(
            f"Site deployment failed: {str(exc)}",
            task_id=self.request.id,
            trend_id=trend_id,
            content_id=content_id,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('deploy_site', 'failed')
        
        # Retry with exponential backoff
        raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)


@celery_app.task(bind=True)
def deploy_ready_content(self, correlation_id: str = None):
    """
    Celery task to deploy all ready content
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info("Starting batch deployment task", task_id=self.request.id)
    
    try:
        import asyncio
        results = asyncio.run(deployment_orchestrator.deploy_ready_content())
        
        app_metrics.record_celery_task('deploy_ready_content', 'success')
        
        logger.info(
            "Batch deployment completed",
            task_id=self.request.id,
            processed=results['processed'],
            successful=results['successful'],
            failed=results['failed']
        )
        
        return results
        
    except Exception as exc:
        logger.error(
            f"Batch deployment failed: {str(exc)}",
            task_id=self.request.id,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('deploy_ready_content', 'failed')
        raise exc


@celery_app.task(bind=True)
def get_deployment_status(self, deployment_id: str, correlation_id: str = None):
    """
    Celery task to get deployment status
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    try:
        import asyncio
        status = asyncio.run(deployment_orchestrator.get_deployment_status(deployment_id))
        
        app_metrics.record_celery_task('get_deployment_status', 'success')
        
        return status
        
    except Exception as exc:
        logger.error(
            f"Failed to get deployment status: {str(exc)}",
            task_id=self.request.id,
            deployment_id=deployment_id,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('get_deployment_status', 'failed')
        raise exc

"""
Cloudflare DNS management client
Handles DNS record creation, updates, and deletions
"""
import asyncio
from typing import Dict, List, Any, Optional
from shared.config import settings, DNS_CONFIG
from shared.utils import AsyncHTTPClient
from shared.exceptions import DNSError
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics

logger = get_logger('dns.cloudflare')


class CloudflareClient:
    """Client for managing DNS records via Cloudflare API"""
    
    def __init__(self):
        self.api_token = settings.cloudflare_api_token
        self.zone_id = settings.cloudflare_zone_id
        self.base_url = "https://api.cloudflare.com/client/v4"
        self.headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
    
    async def create_dns_record(self, record_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new DNS record"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.post(
                    f"{self.base_url}/zones/{self.zone_id}/dns_records",
                    json=record_data,
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    raise DNSError(f"Failed to create DNS record: {response['status']}")
                
                data = response['data']
                
                if not data.get('success'):
                    errors = data.get('errors', [])
                    error_msg = ', '.join([err.get('message', 'Unknown error') for err in errors])
                    raise DNSError(f"Cloudflare API error: {error_msg}")
                
                record = data['result']
                
                logger.info(
                    f"Created DNS record: {record['name']} -> {record['content']}",
                    record_id=record['id'],
                    record_type=record['type'],
                    record_name=record['name']
                )
                
                return record
                
        except Exception as e:
            logger.error(f"Failed to create DNS record: {str(e)}")
            raise DNSError(f"DNS record creation failed: {str(e)}")
    
    async def update_dns_record(self, record_id: str, record_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing DNS record"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.put(
                    f"{self.base_url}/zones/{self.zone_id}/dns_records/{record_id}",
                    json=record_data,
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    raise DNSError(f"Failed to update DNS record: {response['status']}")
                
                data = response['data']
                
                if not data.get('success'):
                    errors = data.get('errors', [])
                    error_msg = ', '.join([err.get('message', 'Unknown error') for err in errors])
                    raise DNSError(f"Cloudflare API error: {error_msg}")
                
                record = data['result']
                
                logger.info(
                    f"Updated DNS record: {record['name']} -> {record['content']}",
                    record_id=record['id']
                )
                
                return record
                
        except Exception as e:
            logger.error(f"Failed to update DNS record {record_id}: {str(e)}")
            raise DNSError(f"DNS record update failed: {str(e)}")
    
    async def delete_dns_record(self, record_id: str) -> bool:
        """Delete a DNS record"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.delete(
                    f"{self.base_url}/zones/{self.zone_id}/dns_records/{record_id}",
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    raise DNSError(f"Failed to delete DNS record: {response['status']}")
                
                data = response['data']
                
                if not data.get('success'):
                    errors = data.get('errors', [])
                    error_msg = ', '.join([err.get('message', 'Unknown error') for err in errors])
                    raise DNSError(f"Cloudflare API error: {error_msg}")
                
                logger.info(f"Deleted DNS record: {record_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to delete DNS record {record_id}: {str(e)}")
            return False
    
    async def get_dns_record(self, record_id: str) -> Optional[Dict[str, Any]]:
        """Get DNS record details"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.get(
                    f"{self.base_url}/zones/{self.zone_id}/dns_records/{record_id}",
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    return None
                
                data = response['data']
                
                if not data.get('success'):
                    return None
                
                return data['result']
                
        except Exception as e:
            logger.error(f"Failed to get DNS record {record_id}: {str(e)}")
            return None
    
    async def list_dns_records(self, record_type: str = None, name: str = None) -> List[Dict[str, Any]]:
        """List DNS records with optional filtering"""
        try:
            params = {}
            if record_type:
                params['type'] = record_type
            if name:
                params['name'] = name
            
            async with AsyncHTTPClient() as client:
                response = await client.get(
                    f"{self.base_url}/zones/{self.zone_id}/dns_records",
                    headers=self.headers,
                    params=params
                )
                
                if response['status'] != 200:
                    raise DNSError(f"Failed to list DNS records: {response['status']}")
                
                data = response['data']
                
                if not data.get('success'):
                    errors = data.get('errors', [])
                    error_msg = ', '.join([err.get('message', 'Unknown error') for err in errors])
                    raise DNSError(f"Cloudflare API error: {error_msg}")
                
                return data['result']
                
        except Exception as e:
            logger.error(f"Failed to list DNS records: {str(e)}")
            return []
    
    async def purge_cache(self, urls: List[str] = None) -> bool:
        """Purge Cloudflare cache"""
        try:
            payload = {}
            if urls:
                payload['files'] = urls
            else:
                payload['purge_everything'] = True
            
            async with AsyncHTTPClient() as client:
                response = await client.post(
                    f"{self.base_url}/zones/{self.zone_id}/purge_cache",
                    json=payload,
                    headers=self.headers
                )
                
                if response['status'] != 200:
                    raise DNSError(f"Failed to purge cache: {response['status']}")
                
                data = response['data']
                
                if not data.get('success'):
                    errors = data.get('errors', [])
                    error_msg = ', '.join([err.get('message', 'Unknown error') for err in errors])
                    raise DNSError(f"Cloudflare API error: {error_msg}")
                
                logger.info("Cache purged successfully")
                return True
                
        except Exception as e:
            logger.error(f"Failed to purge cache: {str(e)}")
            return False


class DNSManager:
    """High-level DNS management"""
    
    def __init__(self):
        self.cloudflare = CloudflareClient()
        self.logger = logger
        self.metrics = app_metrics
    
    async def create_trend_subdomain(self, trend_slug: str, target_url: str) -> Dict[str, Any]:
        """Create subdomain for a trend"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Generate subdomain
            subdomain = f"{trend_slug}.{DNS_CONFIG['domains']['trends_subdomain']}.{DNS_CONFIG['domains']['main_domain']}"
            
            # Create CNAME record
            record_data = {
                'type': 'CNAME',
                'name': subdomain,
                'content': target_url,
                'ttl': DNS_CONFIG['caching']['ttl'],
                'proxied': True  # Enable Cloudflare proxy for caching and security
            }
            
            record = await self.cloudflare.create_dns_record(record_data)
            
            duration = asyncio.get_event_loop().time() - start_time
            
            # Record metrics
            self.metrics.record_dns_operation('create_subdomain', 'success', duration)
            
            self.logger.info(
                f"Created subdomain: {subdomain} -> {target_url}",
                subdomain=subdomain,
                target_url=target_url,
                record_id=record['id'],
                duration=duration
            )
            
            return {
                'subdomain': subdomain,
                'target_url': target_url,
                'record_id': record['id'],
                'cloudflare_record': record
            }
            
        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            self.metrics.record_dns_operation('create_subdomain', 'failed', duration)
            
            self.logger.error(
                f"Failed to create subdomain for {trend_slug}: {str(e)}",
                trend_slug=trend_slug,
                target_url=target_url,
                duration=duration
            )
            
            raise DNSError(f"Subdomain creation failed: {str(e)}")
    
    async def update_trend_subdomain(self, record_id: str, new_target_url: str) -> bool:
        """Update subdomain target"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Get current record
            current_record = await self.cloudflare.get_dns_record(record_id)
            if not current_record:
                raise DNSError("DNS record not found")
            
            # Update record
            record_data = {
                'type': current_record['type'],
                'name': current_record['name'],
                'content': new_target_url,
                'ttl': DNS_CONFIG['caching']['ttl'],
                'proxied': current_record.get('proxied', True)
            }
            
            updated_record = await self.cloudflare.update_dns_record(record_id, record_data)
            
            # Purge cache if enabled
            if DNS_CONFIG['caching']['purge_on_update']:
                await self.cloudflare.purge_cache([f"https://{current_record['name']}"])
            
            duration = asyncio.get_event_loop().time() - start_time
            
            # Record metrics
            self.metrics.record_dns_operation('update_subdomain', 'success', duration)
            
            self.logger.info(
                f"Updated subdomain: {current_record['name']} -> {new_target_url}",
                record_id=record_id,
                old_target=current_record['content'],
                new_target=new_target_url,
                duration=duration
            )
            
            return True
            
        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            self.metrics.record_dns_operation('update_subdomain', 'failed', duration)
            
            self.logger.error(
                f"Failed to update subdomain {record_id}: {str(e)}",
                record_id=record_id,
                new_target_url=new_target_url,
                duration=duration
            )
            
            return False
    
    async def delete_trend_subdomain(self, record_id: str) -> bool:
        """Delete subdomain"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            success = await self.cloudflare.delete_dns_record(record_id)
            
            duration = asyncio.get_event_loop().time() - start_time
            
            # Record metrics
            status = 'success' if success else 'failed'
            self.metrics.record_dns_operation('delete_subdomain', status, duration)
            
            if success:
                self.logger.info(
                    f"Deleted subdomain record: {record_id}",
                    record_id=record_id,
                    duration=duration
                )
            
            return success
            
        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            self.metrics.record_dns_operation('delete_subdomain', 'failed', duration)
            
            self.logger.error(
                f"Failed to delete subdomain {record_id}: {str(e)}",
                record_id=record_id,
                duration=duration
            )
            
            return False
    
    async def get_trend_subdomains(self) -> List[Dict[str, Any]]:
        """Get all trend subdomains"""
        try:
            # List CNAME records for trends subdomain
            pattern = f"*.{DNS_CONFIG['domains']['trends_subdomain']}.{DNS_CONFIG['domains']['main_domain']}"
            records = await self.cloudflare.list_dns_records(record_type='CNAME')
            
            # Filter for trend subdomains
            trend_records = []
            for record in records:
                if DNS_CONFIG['domains']['trends_subdomain'] in record['name']:
                    trend_records.append({
                        'id': record['id'],
                        'subdomain': record['name'],
                        'target_url': record['content'],
                        'proxied': record.get('proxied', False),
                        'ttl': record.get('ttl', 0),
                        'created_on': record.get('created_on'),
                        'modified_on': record.get('modified_on')
                    })
            
            self.logger.info(f"Retrieved {len(trend_records)} trend subdomains")
            
            return trend_records
            
        except Exception as e:
            self.logger.error(f"Failed to get trend subdomains: {str(e)}")
            return []


# Global DNS manager instance
dns_manager = DNSManager()

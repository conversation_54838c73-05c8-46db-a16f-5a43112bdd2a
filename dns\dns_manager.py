"""
DNS Manager - High-level DNS operations orchestrator
Coordinates DNS record management across providers
"""
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime

from dns.cloudflare_client import CloudflareClient
from database.models.dns_model import DNSRepository, DNSStatus
from monitoring.logger import get_logger
from shared.exceptions import DNSError

logger = get_logger('dns.dns_manager')


class DNSManager:
    """High-level DNS operations manager"""
    
    def __init__(self):
        self.cloudflare_client = CloudflareClient()
        self.dns_repo = DNSRepository()
        self.logger = logger
    
    async def create_trend_dns(
        self,
        trend_id: str,
        subdomain: str,
        target_url: str,
        ttl: int = 3600,
        proxied: bool = True
    ) -> Dict[str, Any]:
        """
        Create DNS record for a trend
        """
        try:
            self.logger.info(f"Creating DNS record for trend {trend_id}: {subdomain}")
            
            # Create DNS record via Cloudflare
            cf_result = await self.cloudflare_client.create_dns_record(
                name=subdomain,
                record_type="CNAME",
                content=target_url,
                ttl=ttl,
                proxied=proxied
            )
            
            if not cf_result.get('success'):
                raise DNSError(f"Failed to create Cloudflare DNS record: {cf_result.get('error')}")
            
            record_id = cf_result['result']['id']
            
            self.logger.info(
                f"Successfully created DNS record: {subdomain} -> {target_url}",
                trend_id=trend_id,
                record_id=record_id
            )
            
            return {
                'success': True,
                'record_id': record_id,
                'subdomain': subdomain,
                'target_url': target_url,
                'metadata': {
                    'cloudflare_zone_id': cf_result['result'].get('zone_id'),
                    'created_at': datetime.utcnow().isoformat(),
                    'ttl': ttl,
                    'proxied': proxied
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to create DNS record for trend {trend_id}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'subdomain': subdomain,
                'target_url': target_url
            }
    
    async def update_dns_record(
        self,
        record_id: str,
        target_url: Optional[str] = None,
        ttl: Optional[int] = None,
        proxied: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        Update existing DNS record
        """
        try:
            self.logger.info(f"Updating DNS record {record_id}")
            
            # Get current record details
            record_details = await self.cloudflare_client.get_dns_record(record_id)
            if not record_details.get('success'):
                raise DNSError(f"Failed to get DNS record details: {record_details.get('error')}")
            
            current_record = record_details['result']
            
            # Prepare update data
            update_data = {
                'name': current_record['name'],
                'type': current_record['type'],
                'content': target_url or current_record['content'],
                'ttl': ttl or current_record['ttl'],
                'proxied': proxied if proxied is not None else current_record.get('proxied', False)
            }
            
            # Update DNS record via Cloudflare
            cf_result = await self.cloudflare_client.update_dns_record(record_id, update_data)
            
            if not cf_result.get('success'):
                raise DNSError(f"Failed to update Cloudflare DNS record: {cf_result.get('error')}")
            
            self.logger.info(f"Successfully updated DNS record {record_id}")
            
            return {
                'success': True,
                'record_id': record_id,
                'updated_fields': update_data,
                'metadata': {
                    'updated_at': datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to update DNS record {record_id}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'record_id': record_id
            }
    
    async def delete_dns_record(self, record_id: str) -> Dict[str, Any]:
        """
        Delete DNS record
        """
        try:
            self.logger.info(f"Deleting DNS record {record_id}")
            
            # Delete DNS record via Cloudflare
            cf_result = await self.cloudflare_client.delete_dns_record(record_id)
            
            if not cf_result.get('success'):
                raise DNSError(f"Failed to delete Cloudflare DNS record: {cf_result.get('error')}")
            
            self.logger.info(f"Successfully deleted DNS record {record_id}")
            
            return {
                'success': True,
                'record_id': record_id,
                'metadata': {
                    'deleted_at': datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to delete DNS record {record_id}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'record_id': record_id
            }
    
    async def get_trend_subdomains(self) -> List[Dict[str, Any]]:
        """
        Get all trend-related DNS records from Cloudflare
        """
        try:
            self.logger.info("Retrieving all trend subdomains from Cloudflare")
            
            # Get all DNS records from Cloudflare
            cf_result = await self.cloudflare_client.list_dns_records()
            
            if not cf_result.get('success'):
                raise DNSError(f"Failed to list Cloudflare DNS records: {cf_result.get('error')}")
            
            # Filter for trend-related records (assuming they contain 'trends' in the name)
            trend_records = []
            for record in cf_result['result']:
                if 'trends' in record.get('name', '').lower() or record.get('type') == 'CNAME':
                    trend_records.append({
                        'id': record['id'],
                        'name': record['name'],
                        'type': record['type'],
                        'content': record['content'],
                        'ttl': record['ttl'],
                        'proxied': record.get('proxied', False),
                        'zone_id': record['zone_id'],
                        'created_on': record.get('created_on'),
                        'modified_on': record.get('modified_on')
                    })
            
            self.logger.info(f"Retrieved {len(trend_records)} trend DNS records")
            
            return trend_records
            
        except Exception as e:
            self.logger.error(f"Failed to get trend subdomains: {str(e)}")
            raise DNSError(f"Failed to get trend subdomains: {str(e)}")
    
    async def purge_cache(self, urls: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Purge Cloudflare cache for specific URLs or entire zone
        """
        try:
            self.logger.info("Purging Cloudflare cache")
            
            # Purge cache via Cloudflare
            cf_result = await self.cloudflare_client.purge_cache(urls)
            
            if not cf_result.get('success'):
                raise DNSError(f"Failed to purge Cloudflare cache: {cf_result.get('error')}")
            
            self.logger.info("Successfully purged Cloudflare cache")
            
            return {
                'success': True,
                'purged_urls': urls or ['all'],
                'metadata': {
                    'purged_at': datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to purge cache: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def validate_dns_record(self, subdomain: str, expected_target: str) -> Dict[str, Any]:
        """
        Validate that a DNS record resolves correctly
        """
        try:
            self.logger.info(f"Validating DNS record: {subdomain}")
            
            # This would implement actual DNS resolution validation
            # For now, return a mock validation
            
            return {
                'valid': True,
                'subdomain': subdomain,
                'resolved_target': expected_target,
                'validation_time': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to validate DNS record {subdomain}: {str(e)}")
            return {
                'valid': False,
                'error': str(e),
                'subdomain': subdomain
            }
    
    async def sync_dns_records(self) -> Dict[str, Any]:
        """
        Sync DNS records between Cloudflare and database
        """
        try:
            self.logger.info("Starting DNS records sync")
            
            # Get records from both sources
            cf_records = await self.get_trend_subdomains()
            db_records = await self.dns_repo.get_active_records()
            
            # Create lookup maps
            cf_records_map = {record['id']: record for record in cf_records}
            db_records_map = {record.cloudflare_record_id: record for record in db_records}
            
            sync_results = {
                'cloudflare_records': len(cf_records),
                'database_records': len(db_records),
                'synced': 0,
                'orphaned_cf': 0,
                'orphaned_db': 0,
                'errors': []
            }
            
            # Find orphaned Cloudflare records (in CF but not in DB)
            for cf_id, cf_record in cf_records_map.items():
                if cf_id not in db_records_map:
                    sync_results['orphaned_cf'] += 1
                    self.logger.warning(f"Orphaned Cloudflare record: {cf_record['name']}")
            
            # Find orphaned database records (in DB but not in CF)
            for cf_id, db_record in db_records_map.items():
                if cf_id and cf_id not in cf_records_map:
                    sync_results['orphaned_db'] += 1
                    self.logger.warning(f"Orphaned database record: {db_record.subdomain}")
                    
                    # Mark as deleted in database
                    try:
                        await self.dns_repo.update(db_record.id, {
                            'status': DNSStatus.DELETED
                        })
                        sync_results['synced'] += 1
                    except Exception as e:
                        sync_results['errors'].append(f"Failed to update orphaned record {db_record.id}: {str(e)}")
            
            self.logger.info(f"DNS sync completed: {sync_results}")
            
            return sync_results
            
        except Exception as e:
            self.logger.error(f"Failed to sync DNS records: {str(e)}")
            raise DNSError(f"Failed to sync DNS records: {str(e)}")


# Global DNS manager instance
dns_manager = DNSManager()

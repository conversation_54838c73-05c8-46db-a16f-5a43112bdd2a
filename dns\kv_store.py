"""
Cloudflare KV Store integration for DNS management
Manages trend redirects and analytics data in Cloudflare Workers KV
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import aiohttp

from monitoring.logger import get_logger
from shared.exceptions import DNSError


class CloudflareKVClient:
    """Client for interacting with Cloudflare Workers KV"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger('dns.kv_client')
        
        # Configuration
        self.account_id = config.get('account_id')
        self.api_token = config.get('api_token')
        self.redirects_namespace_id = config.get('redirects_namespace_id')
        self.analytics_namespace_id = config.get('analytics_namespace_id')
        
        if not all([self.account_id, self.api_token, self.redirects_namespace_id]):
            raise DNSError("Missing required Cloudflare KV configuration")
        
        self.base_url = f"https://api.cloudflare.com/client/v4/accounts/{self.account_id}/storage/kv/namespaces"
        self.headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
    
    async def put_redirect(self, trend_slug: str, redirect_data: Dict[str, Any]) -> bool:
        """Store redirect mapping in KV"""
        try:
            url = f"{self.base_url}/{self.redirects_namespace_id}/values/{trend_slug}"
            
            # Prepare redirect data
            kv_data = {
                'url': redirect_data['url'],
                'trend_id': redirect_data['trend_id'],
                'created_at': datetime.utcnow().isoformat(),
                'metadata': redirect_data.get('metadata', {})
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.put(
                    url,
                    headers=self.headers,
                    data=json.dumps(kv_data)
                ) as response:
                    if response.status == 200:
                        self.logger.info(f"Stored redirect mapping: {trend_slug} -> {redirect_data['url']}")
                        return True
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to store redirect: HTTP {response.status} - {error_text}")
                        return False
        
        except Exception as e:
            self.logger.error(f"Error storing redirect for {trend_slug}: {str(e)}")
            return False
    
    async def get_redirect(self, trend_slug: str) -> Optional[Dict[str, Any]]:
        """Get redirect mapping from KV"""
        try:
            url = f"{self.base_url}/{self.redirects_namespace_id}/values/{trend_slug}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.logger.debug(f"Retrieved redirect mapping: {trend_slug}")
                        return data
                    elif response.status == 404:
                        return None
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to get redirect: HTTP {response.status} - {error_text}")
                        return None
        
        except Exception as e:
            self.logger.error(f"Error getting redirect for {trend_slug}: {str(e)}")
            return None
    
    async def delete_redirect(self, trend_slug: str) -> bool:
        """Delete redirect mapping from KV"""
        try:
            url = f"{self.base_url}/{self.redirects_namespace_id}/values/{trend_slug}"
            
            async with aiohttp.ClientSession() as session:
                async with session.delete(url, headers=self.headers) as response:
                    if response.status == 200:
                        self.logger.info(f"Deleted redirect mapping: {trend_slug}")
                        return True
                    elif response.status == 404:
                        self.logger.warning(f"Redirect mapping not found: {trend_slug}")
                        return True  # Consider it successful if already gone
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to delete redirect: HTTP {response.status} - {error_text}")
                        return False
        
        except Exception as e:
            self.logger.error(f"Error deleting redirect for {trend_slug}: {str(e)}")
            return False
    
    async def list_redirects(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """List all redirect mappings"""
        try:
            url = f"{self.base_url}/{self.redirects_namespace_id}/keys"
            params = {'limit': limit}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        keys = data.get('result', [])
                        
                        # Get the actual values for each key
                        redirects = []
                        for key_info in keys:
                            key_name = key_info['name']
                            redirect_data = await self.get_redirect(key_name)
                            if redirect_data:
                                redirect_data['trend_slug'] = key_name
                                redirects.append(redirect_data)
                        
                        self.logger.info(f"Listed {len(redirects)} redirect mappings")
                        return redirects
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to list redirects: HTTP {response.status} - {error_text}")
                        return []
        
        except Exception as e:
            self.logger.error(f"Error listing redirects: {str(e)}")
            return []
    
    async def get_analytics_data(self, trend_slug: str, days: int = 7) -> Dict[str, Any]:
        """Get analytics data for a trend from KV"""
        try:
            # Get daily counters
            daily_visits = {}
            total_visits = 0
            
            for i in range(days):
                date = datetime.utcnow() - timedelta(days=i)
                date_str = date.strftime('%Y-%m-%d')
                counter_key = f"daily:{trend_slug}:{date_str}"
                
                count = await self._get_analytics_value(counter_key)
                daily_count = int(count) if count else 0
                daily_visits[date_str] = daily_count
                total_visits += daily_count
            
            # Get recent detailed analytics (last 100 records)
            recent_analytics = await self._get_recent_analytics(trend_slug, 100)
            
            # Aggregate country and referrer data
            countries = {}
            referrers = {}
            
            for record in recent_analytics:
                if record.get('country') and record['country'] != 'unknown':
                    countries[record['country']] = countries.get(record['country'], 0) + 1
                
                referrer_domain = self._extract_domain(record.get('referer', ''))
                if referrer_domain and referrer_domain != 'direct':
                    referrers[referrer_domain] = referrers.get(referrer_domain, 0) + 1
            
            return {
                'trend_slug': trend_slug,
                'period_days': days,
                'total_visits': total_visits,
                'daily_visits': daily_visits,
                'countries': countries,
                'referrers': referrers,
                'recent_records_count': len(recent_analytics),
                'generated_at': datetime.utcnow().isoformat()
            }
        
        except Exception as e:
            self.logger.error(f"Error getting analytics for {trend_slug}: {str(e)}")
            return {
                'trend_slug': trend_slug,
                'error': str(e),
                'generated_at': datetime.utcnow().isoformat()
            }
    
    async def _get_analytics_value(self, key: str) -> Optional[str]:
        """Get a single analytics value from KV"""
        try:
            if not self.analytics_namespace_id:
                return None
            
            url = f"{self.base_url}/{self.analytics_namespace_id}/values/{key}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        return await response.text()
                    else:
                        return None
        
        except Exception:
            return None
    
    async def _get_recent_analytics(self, trend_slug: str, limit: int) -> List[Dict[str, Any]]:
        """Get recent analytics records for a trend"""
        try:
            if not self.analytics_namespace_id:
                return []
            
            # List keys with the trend slug prefix
            url = f"{self.base_url}/{self.analytics_namespace_id}/keys"
            params = {
                'prefix': f"{trend_slug}:",
                'limit': limit
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        keys = data.get('result', [])
                        
                        # Get the actual records
                        records = []
                        for key_info in keys:
                            record = await self._get_analytics_record(key_info['name'])
                            if record:
                                records.append(record)
                        
                        return records
                    else:
                        return []
        
        except Exception as e:
            self.logger.error(f"Error getting recent analytics for {trend_slug}: {str(e)}")
            return []
    
    async def _get_analytics_record(self, key: str) -> Optional[Dict[str, Any]]:
        """Get a single analytics record"""
        try:
            url = f"{self.base_url}/{self.analytics_namespace_id}/values/{key}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return None
        
        except Exception:
            return None
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        if not url or url == 'direct':
            return 'direct'
        
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc or 'unknown'
        except:
            return 'unknown'
    
    async def cleanup_old_analytics(self, days_to_keep: int = 30) -> int:
        """Clean up old analytics data"""
        try:
            if not self.analytics_namespace_id:
                return 0
            
            cutoff_timestamp = (datetime.utcnow() - timedelta(days=days_to_keep)).timestamp() * 1000
            
            # List all analytics keys
            url = f"{self.base_url}/{self.analytics_namespace_id}/keys"
            params = {'limit': 10000}  # Get all keys
            
            deleted_count = 0
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        keys = data.get('result', [])
                        
                        # Filter keys to delete
                        keys_to_delete = []
                        for key_info in keys:
                            key_name = key_info['name']
                            
                            # Extract timestamp from key (format: trend-slug:timestamp:random)
                            parts = key_name.split(':')
                            if len(parts) >= 2:
                                try:
                                    timestamp = int(parts[1])
                                    if timestamp < cutoff_timestamp:
                                        keys_to_delete.append(key_name)
                                except ValueError:
                                    continue
                        
                        # Delete old keys
                        for key_name in keys_to_delete:
                            delete_url = f"{self.base_url}/{self.analytics_namespace_id}/values/{key_name}"
                            async with session.delete(delete_url, headers=self.headers) as delete_response:
                                if delete_response.status == 200:
                                    deleted_count += 1
            
            self.logger.info(f"Cleaned up {deleted_count} old analytics records")
            return deleted_count
        
        except Exception as e:
            self.logger.error(f"Error cleaning up analytics: {str(e)}")
            return 0
    
    async def get_kv_stats(self) -> Dict[str, Any]:
        """Get KV namespace statistics"""
        stats = {
            'redirects_namespace': {
                'id': self.redirects_namespace_id,
                'key_count': 0,
                'status': 'unknown'
            },
            'analytics_namespace': {
                'id': self.analytics_namespace_id,
                'key_count': 0,
                'status': 'unknown'
            }
        }
        
        try:
            # Get redirects namespace stats
            redirects_url = f"{self.base_url}/{self.redirects_namespace_id}/keys"
            async with aiohttp.ClientSession() as session:
                async with session.get(redirects_url, headers=self.headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        stats['redirects_namespace']['key_count'] = len(data.get('result', []))
                        stats['redirects_namespace']['status'] = 'healthy'
                    else:
                        stats['redirects_namespace']['status'] = 'error'
            
            # Get analytics namespace stats if configured
            if self.analytics_namespace_id:
                analytics_url = f"{self.base_url}/{self.analytics_namespace_id}/keys"
                async with session.get(analytics_url, headers=self.headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        stats['analytics_namespace']['key_count'] = len(data.get('result', []))
                        stats['analytics_namespace']['status'] = 'healthy'
                    else:
                        stats['analytics_namespace']['status'] = 'error'
        
        except Exception as e:
            self.logger.error(f"Error getting KV stats: {str(e)}")
        
        return stats


class KVStoreManager:
    """High-level manager for KV store operations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger('dns.kv_manager')
        self.kv_client = CloudflareKVClient(config)
    
    async def create_trend_redirect(
        self, 
        trend_slug: str, 
        trend_id: str, 
        target_url: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Create a trend redirect mapping"""
        try:
            redirect_data = {
                'url': target_url,
                'trend_id': trend_id,
                'metadata': metadata or {}
            }
            
            success = await self.kv_client.put_redirect(trend_slug, redirect_data)
            
            if success:
                self.logger.info(f"Created trend redirect: {trend_slug} -> {target_url}")
            else:
                self.logger.error(f"Failed to create trend redirect: {trend_slug}")
            
            return success
        
        except Exception as e:
            self.logger.error(f"Error creating trend redirect: {str(e)}")
            return False
    
    async def update_trend_redirect(
        self, 
        trend_slug: str, 
        target_url: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update an existing trend redirect"""
        try:
            # Get existing redirect data
            existing_data = await self.kv_client.get_redirect(trend_slug)
            
            if not existing_data:
                self.logger.warning(f"Redirect not found for update: {trend_slug}")
                return False
            
            # Update the data
            existing_data['url'] = target_url
            existing_data['updated_at'] = datetime.utcnow().isoformat()
            
            if metadata:
                existing_data['metadata'].update(metadata)
            
            success = await self.kv_client.put_redirect(trend_slug, existing_data)
            
            if success:
                self.logger.info(f"Updated trend redirect: {trend_slug} -> {target_url}")
            
            return success
        
        except Exception as e:
            self.logger.error(f"Error updating trend redirect: {str(e)}")
            return False
    
    async def delete_trend_redirect(self, trend_slug: str) -> bool:
        """Delete a trend redirect mapping"""
        try:
            success = await self.kv_client.delete_redirect(trend_slug)
            
            if success:
                self.logger.info(f"Deleted trend redirect: {trend_slug}")
            
            return success
        
        except Exception as e:
            self.logger.error(f"Error deleting trend redirect: {str(e)}")
            return False
    
    async def get_trend_analytics(self, trend_slug: str, days: int = 7) -> Dict[str, Any]:
        """Get analytics data for a trend"""
        try:
            analytics = await self.kv_client.get_analytics_data(trend_slug, days)
            
            self.logger.info(f"Retrieved analytics for trend: {trend_slug}")
            
            return analytics
        
        except Exception as e:
            self.logger.error(f"Error getting trend analytics: {str(e)}")
            return {
                'trend_slug': trend_slug,
                'error': str(e),
                'generated_at': datetime.utcnow().isoformat()
            }
    
    async def get_all_redirects(self) -> List[Dict[str, Any]]:
        """Get all trend redirects"""
        try:
            redirects = await self.kv_client.list_redirects()
            
            self.logger.info(f"Retrieved {len(redirects)} trend redirects")
            
            return redirects
        
        except Exception as e:
            self.logger.error(f"Error getting all redirects: {str(e)}")
            return []
    
    async def sync_redirects_with_database(self) -> Dict[str, Any]:
        """Sync KV redirects with database records"""
        try:
            from database.models.dns_model import DNSRepository
            dns_repo = DNSRepository()
            
            # Get active DNS records from database
            db_records = await dns_repo.get_active_records()
            
            # Get KV redirects
            kv_redirects = await self.get_all_redirects()
            
            # Create lookup maps
            db_slugs = set()
            kv_slugs = set()
            
            for record in db_records:
                # Extract slug from subdomain
                if record.subdomain:
                    slug = record.subdomain.split('.')[0]
                    db_slugs.add(slug)
            
            for redirect in kv_redirects:
                kv_slugs.add(redirect['trend_slug'])
            
            # Find mismatches
            missing_in_kv = db_slugs - kv_slugs
            missing_in_db = kv_slugs - db_slugs
            
            sync_result = {
                'database_records': len(db_records),
                'kv_redirects': len(kv_redirects),
                'missing_in_kv': list(missing_in_kv),
                'missing_in_db': list(missing_in_db),
                'synced': True
            }
            
            self.logger.info(f"KV sync completed: {sync_result}")
            
            return sync_result
        
        except Exception as e:
            self.logger.error(f"Error syncing redirects: {str(e)}")
            return {
                'error': str(e),
                'synced': False
            }


# Global KV store manager instance (initialized with config)
kv_store_manager = None

def get_kv_store_manager(config: Dict[str, Any]) -> KVStoreManager:
    """Get or create KV store manager instance"""
    global kv_store_manager
    if kv_store_manager is None:
        kv_store_manager = KVStoreManager(config)
    return kv_store_manager

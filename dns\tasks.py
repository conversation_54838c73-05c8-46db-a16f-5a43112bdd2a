"""
Celery tasks for DNS operations
"""
from typing import Dict, Any
from shared.celery_app import celery_app
from dns.cloudflare_client import dns_manager
from database.models.dns_model import DNSRepository
from monitoring.logger import get_logger, set_correlation_id
from monitoring.metrics import app_metrics

logger = get_logger('dns.tasks')


@celery_app.task(bind=True, max_retries=3)
def create_dns_record(self, trend_id: str, subdomain: str, target_url: str, correlation_id: str = None):
    """
    Celery task to create DNS record for a trend
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info(
        f"Creating DNS record for trend",
        task_id=self.request.id,
        trend_id=trend_id,
        subdomain=subdomain,
        target_url=target_url
    )
    
    try:
        import asyncio
        
        # Create DNS record
        result = asyncio.run(dns_manager.create_trend_subdomain(subdomain, target_url))
        
        # Save to database
        dns_repo = DNSRepository()
        dns_data = {
            'trend_id': trend_id,
            'subdomain': result['subdomain'],
            'target_url': target_url,
            'cloudflare_record_id': result['record_id'],
            'status': 'active',
            'dns_metadata': result['cloudflare_record']
        }
        
        dns_record_id = asyncio.run(dns_repo.create(dns_data))
        
        # Record metrics
        app_metrics.record_celery_task('create_dns_record', 'success')
        
        logger.info(
            f"DNS record created successfully",
            task_id=self.request.id,
            trend_id=trend_id,
            dns_record_id=dns_record_id,
            subdomain=result['subdomain']
        )
        
        return {
            'dns_record_id': dns_record_id,
            'subdomain': result['subdomain'],
            'cloudflare_record_id': result['record_id']
        }
        
    except Exception as exc:
        logger.error(
            f"DNS record creation failed: {str(exc)}",
            task_id=self.request.id,
            trend_id=trend_id,
            subdomain=subdomain,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('create_dns_record', 'failed')
        
        # Retry with exponential backoff
        raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)


@celery_app.task(bind=True)
def update_dns_record(self, dns_record_id: str, new_target_url: str, correlation_id: str = None):
    """
    Celery task to update DNS record
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info(
        f"Updating DNS record",
        task_id=self.request.id,
        dns_record_id=dns_record_id,
        new_target_url=new_target_url
    )
    
    try:
        import asyncio
        
        # Get DNS record from database
        dns_repo = DNSRepository()
        dns_record = asyncio.run(dns_repo.get_by_id(dns_record_id))
        
        if not dns_record:
            raise Exception("DNS record not found")
        
        # Update Cloudflare record
        success = asyncio.run(dns_manager.update_trend_subdomain(
            dns_record.cloudflare_record_id,
            new_target_url
        ))
        
        if success:
            # Update database record
            asyncio.run(dns_repo.update(dns_record_id, {
                'target_url': new_target_url,
                'status': 'active'
            }))
        
        app_metrics.record_celery_task('update_dns_record', 'success')
        
        logger.info(
            f"DNS record updated successfully",
            task_id=self.request.id,
            dns_record_id=dns_record_id,
            new_target_url=new_target_url
        )
        
        return {'success': success}
        
    except Exception as exc:
        logger.error(
            f"DNS record update failed: {str(exc)}",
            task_id=self.request.id,
            dns_record_id=dns_record_id,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('update_dns_record', 'failed')
        raise exc


@celery_app.task(bind=True)
def delete_dns_record(self, dns_record_id: str, correlation_id: str = None):
    """
    Celery task to delete DNS record
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info(
        f"Deleting DNS record",
        task_id=self.request.id,
        dns_record_id=dns_record_id
    )
    
    try:
        import asyncio
        from datetime import datetime
        
        # Get DNS record from database
        dns_repo = DNSRepository()
        dns_record = asyncio.run(dns_repo.get_by_id(dns_record_id))
        
        if not dns_record:
            raise Exception("DNS record not found")
        
        # Delete Cloudflare record
        success = asyncio.run(dns_manager.delete_trend_subdomain(
            dns_record.cloudflare_record_id
        ))
        
        if success:
            # Mark as deleted in database
            asyncio.run(dns_repo.update(dns_record_id, {
                'status': 'deleted',
                'deleted_at': datetime.utcnow()
            }))
        
        app_metrics.record_celery_task('delete_dns_record', 'success')
        
        logger.info(
            f"DNS record deleted successfully",
            task_id=self.request.id,
            dns_record_id=dns_record_id
        )
        
        return {'success': success}
        
    except Exception as exc:
        logger.error(
            f"DNS record deletion failed: {str(exc)}",
            task_id=self.request.id,
            dns_record_id=dns_record_id,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('delete_dns_record', 'failed')
        raise exc


@celery_app.task(bind=True)
def sync_dns_records(self, correlation_id: str = None):
    """
    Celery task to sync DNS records with Cloudflare
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info("Starting DNS records sync", task_id=self.request.id)
    
    try:
        import asyncio
        
        # Get all trend subdomains from Cloudflare
        cloudflare_records = asyncio.run(dns_manager.get_trend_subdomains())
        
        # Get all DNS records from database
        dns_repo = DNSRepository()
        db_records = asyncio.run(dns_repo.get_active_records())
        
        # Create lookup maps
        cf_records_map = {record['id']: record for record in cloudflare_records}
        db_records_map = {record.cloudflare_record_id: record for record in db_records}
        
        sync_results = {
            'cloudflare_records': len(cloudflare_records),
            'database_records': len(db_records),
            'synced': 0,
            'orphaned_cf': 0,
            'orphaned_db': 0
        }
        
        # Check for orphaned Cloudflare records (exist in CF but not in DB)
        for cf_record_id, cf_record in cf_records_map.items():
            if cf_record_id not in db_records_map:
                sync_results['orphaned_cf'] += 1
                logger.warning(
                    f"Orphaned Cloudflare record found: {cf_record['subdomain']}",
                    cloudflare_record_id=cf_record_id
                )
        
        # Check for orphaned database records (exist in DB but not in CF)
        for cf_record_id, db_record in db_records_map.items():
            if cf_record_id not in cf_records_map:
                sync_results['orphaned_db'] += 1
                logger.warning(
                    f"Orphaned database record found: {db_record.subdomain}",
                    dns_record_id=db_record.id,
                    cloudflare_record_id=cf_record_id
                )
                
                # Mark as error status
                asyncio.run(dns_repo.update(db_record.id, {'status': 'error'}))
            else:
                sync_results['synced'] += 1
        
        app_metrics.record_celery_task('sync_dns_records', 'success')
        
        logger.info(
            "DNS records sync completed",
            task_id=self.request.id,
            **sync_results
        )
        
        return sync_results
        
    except Exception as exc:
        logger.error(
            f"DNS records sync failed: {str(exc)}",
            task_id=self.request.id,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('sync_dns_records', 'failed')
        raise exc

/**
 * <PERSON>flare Worker for handling trend redirects and analytics
 * Manages subdomain routing and tracks visitor analytics
 */

// KV namespace bindings (configured in wrangler.toml)
// TREND_REDIRECTS - stores trend slug to URL mappings
// ANALYTICS_DATA - stores analytics data

/**
 * Main request handler
 */
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

/**
 * Handle incoming requests
 */
async function handleRequest(request) {
  const url = new URL(request.url)
  const hostname = url.hostname
  
  try {
    // Extract subdomain from hostname
    const subdomain = extractSubdomain(hostname)
    
    if (!subdomain) {
      return new Response('Invalid subdomain', { status: 400 })
    }
    
    // Handle different request types
    if (url.pathname === '/health') {
      return handleHealthCheck()
    }
    
    if (url.pathname === '/analytics') {
      return handleAnalyticsRequest(request, subdomain)
    }
    
    // Handle trend redirect
    return await handleTrendRedirect(request, subdomain)
    
  } catch (error) {
    console.error('Worker error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}

/**
 * Extract subdomain from hostname
 */
function extractSubdomain(hostname) {
  // Expected format: {trend-slug}.trends.yourdomain.com
  const parts = hostname.split('.')
  
  if (parts.length >= 3 && parts[1] === 'trends') {
    return parts[0]
  }
  
  return null
}

/**
 * Handle trend redirect
 */
async function handleTrendRedirect(request, trendSlug) {
  try {
    // Get redirect URL from KV store
    const redirectData = await TREND_REDIRECTS.get(trendSlug, { type: 'json' })
    
    if (!redirectData || !redirectData.url) {
      return new Response('Trend not found', { 
        status: 404,
        headers: {
          'Content-Type': 'text/html',
          'Cache-Control': 'no-cache'
        }
      })
    }
    
    // Track analytics
    await trackVisit(request, trendSlug, redirectData)
    
    // Perform redirect
    return Response.redirect(redirectData.url, 302)
    
  } catch (error) {
    console.error('Redirect error:', error)
    return new Response('Redirect failed', { status: 500 })
  }
}

/**
 * Track visitor analytics
 */
async function trackVisit(request, trendSlug, redirectData) {
  try {
    const timestamp = new Date().toISOString()
    const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown'
    const userAgent = request.headers.get('User-Agent') || 'unknown'
    const referer = request.headers.get('Referer') || 'direct'
    const country = request.cf?.country || 'unknown'
    const city = request.cf?.city || 'unknown'
    
    // Create analytics record
    const analyticsRecord = {
      timestamp,
      trendSlug,
      trendId: redirectData.trend_id,
      targetUrl: redirectData.url,
      clientIP: hashIP(clientIP), // Hash IP for privacy
      userAgent,
      referer,
      country,
      city,
      colo: request.cf?.colo || 'unknown'
    }
    
    // Store in KV with timestamp-based key for time-series data
    const analyticsKey = `${trendSlug}:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`
    await ANALYTICS_DATA.put(analyticsKey, JSON.stringify(analyticsRecord), {
      expirationTtl: 86400 * 30 // 30 days retention
    })
    
    // Update daily counters
    await updateDailyCounters(trendSlug, timestamp)
    
  } catch (error) {
    console.error('Analytics tracking error:', error)
    // Don't fail the redirect if analytics fails
  }
}

/**
 * Update daily visit counters
 */
async function updateDailyCounters(trendSlug, timestamp) {
  try {
    const date = timestamp.split('T')[0] // YYYY-MM-DD format
    const counterKey = `daily:${trendSlug}:${date}`
    
    // Get current count
    const currentCount = await ANALYTICS_DATA.get(counterKey)
    const newCount = currentCount ? parseInt(currentCount) + 1 : 1
    
    // Update counter with 48-hour expiration
    await ANALYTICS_DATA.put(counterKey, newCount.toString(), {
      expirationTtl: 86400 * 2 // 48 hours
    })
    
  } catch (error) {
    console.error('Counter update error:', error)
  }
}

/**
 * Hash IP address for privacy
 */
function hashIP(ip) {
  // Simple hash function for IP privacy
  let hash = 0
  for (let i = 0; i < ip.length; i++) {
    const char = ip.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return hash.toString(36)
}

/**
 * Handle health check requests
 */
function handleHealthCheck() {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    worker: 'trend-redirect-worker'
  }
  
  return new Response(JSON.stringify(healthData), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache'
    }
  })
}

/**
 * Handle analytics requests
 */
async function handleAnalyticsRequest(request, trendSlug) {
  try {
    const url = new URL(request.url)
    const days = parseInt(url.searchParams.get('days')) || 7
    
    // Get analytics data for the trend
    const analyticsData = await getAnalyticsData(trendSlug, days)
    
    return new Response(JSON.stringify(analyticsData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 5 minutes cache
        'Access-Control-Allow-Origin': '*'
      }
    })
    
  } catch (error) {
    console.error('Analytics request error:', error)
    return new Response('Analytics unavailable', { status: 500 })
  }
}

/**
 * Get analytics data for a trend
 */
async function getAnalyticsData(trendSlug, days) {
  const analytics = {
    trendSlug,
    period: `${days} days`,
    totalVisits: 0,
    dailyVisits: {},
    countries: {},
    referrers: {},
    generatedAt: new Date().toISOString()
  }
  
  try {
    // Get daily counters for the specified period
    const promises = []
    const today = new Date()
    
    for (let i = 0; i < days; i++) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      const counterKey = `daily:${trendSlug}:${dateStr}`
      
      promises.push(
        ANALYTICS_DATA.get(counterKey).then(count => ({
          date: dateStr,
          count: count ? parseInt(count) : 0
        }))
      )
    }
    
    const dailyCounts = await Promise.all(promises)
    
    // Process daily counts
    for (const { date, count } of dailyCounts) {
      analytics.dailyVisits[date] = count
      analytics.totalVisits += count
    }
    
    // Get detailed analytics from recent records (last 1000 entries)
    const recentAnalytics = await getRecentAnalytics(trendSlug, 1000)
    
    // Aggregate country and referrer data
    for (const record of recentAnalytics) {
      // Count countries
      if (record.country && record.country !== 'unknown') {
        analytics.countries[record.country] = (analytics.countries[record.country] || 0) + 1
      }
      
      // Count referrers
      const referrerDomain = extractDomain(record.referer)
      if (referrerDomain && referrerDomain !== 'direct') {
        analytics.referrers[referrerDomain] = (analytics.referrers[referrerDomain] || 0) + 1
      }
    }
    
  } catch (error) {
    console.error('Analytics data error:', error)
  }
  
  return analytics
}

/**
 * Get recent analytics records
 */
async function getRecentAnalytics(trendSlug, limit) {
  const records = []
  
  try {
    // List keys with the trend slug prefix
    const listResult = await ANALYTICS_DATA.list({
      prefix: `${trendSlug}:`,
      limit: limit
    })
    
    // Get the actual records
    const promises = listResult.keys.map(key => 
      ANALYTICS_DATA.get(key.name, { type: 'json' })
    )
    
    const results = await Promise.all(promises)
    
    // Filter out null results and return
    return results.filter(record => record !== null)
    
  } catch (error) {
    console.error('Recent analytics error:', error)
    return []
  }
}

/**
 * Extract domain from URL
 */
function extractDomain(url) {
  if (!url || url === 'direct') {
    return 'direct'
  }
  
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return 'unknown'
  }
}

/**
 * Handle CORS preflight requests
 */
addEventListener('fetch', event => {
  if (event.request.method === 'OPTIONS') {
    event.respondWith(handleCORS())
  }
})

function handleCORS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400'
    }
  })
}

/**
 * Scheduled event handler for cleanup
 */
addEventListener('scheduled', event => {
  event.waitUntil(handleScheduled(event))
})

async function handleScheduled(event) {
  // Clean up old analytics data
  await cleanupOldAnalytics()
}

/**
 * Clean up old analytics data
 */
async function cleanupOldAnalytics() {
  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - 30) // 30 days ago
    const cutoffTimestamp = cutoffDate.getTime()
    
    // List all analytics keys
    const listResult = await ANALYTICS_DATA.list({ limit: 1000 })
    
    const deletePromises = []
    
    for (const key of listResult.keys) {
      // Extract timestamp from key (format: trendSlug:timestamp:random)
      const parts = key.name.split(':')
      if (parts.length >= 2) {
        const timestamp = parseInt(parts[1])
        if (timestamp < cutoffTimestamp) {
          deletePromises.push(ANALYTICS_DATA.delete(key.name))
        }
      }
    }
    
    await Promise.all(deletePromises)
    
    console.log(`Cleaned up ${deletePromises.length} old analytics records`)
    
  } catch (error) {
    console.error('Cleanup error:', error)
  }
}

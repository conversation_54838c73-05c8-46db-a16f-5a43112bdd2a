# Wrangler configuration for TrendSite redirect worker
name = "trendsite-redirect-worker"
main = "redirect_worker.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Worker configuration
[env.production]
name = "trendsite-redirect-worker"
route = "*.trends.yourdomain.com/*"
zone_id = "your-cloudflare-zone-id"

[env.staging]
name = "trendsite-redirect-worker-staging"
route = "*.trends-staging.yourdomain.com/*"
zone_id = "your-cloudflare-zone-id"

[env.development]
name = "trendsite-redirect-worker-dev"

# KV Namespaces for storing redirect mappings and analytics
[[kv_namespaces]]
binding = "TREND_REDIRECTS"
id = "your-trend-redirects-kv-namespace-id"
preview_id = "your-trend-redirects-preview-kv-namespace-id"

[[kv_namespaces]]
binding = "ANALYTICS_DATA"
id = "your-analytics-data-kv-namespace-id"
preview_id = "your-analytics-data-preview-kv-namespace-id"

# Environment variables
[vars]
ENVIRONMENT = "production"
VERSION = "1.0.0"
DEBUG = "false"

[env.staging.vars]
ENVIRONMENT = "staging"
DEBUG = "true"

[env.development.vars]
ENVIRONMENT = "development"
DEBUG = "true"

# Scheduled triggers for cleanup
[[triggers]]
crons = ["0 2 * * *"]  # Run daily at 2 AM UTC

# Resource limits
[limits]
cpu_ms = 50
memory_mb = 128

# Build configuration
[build]
command = ""
cwd = ""
watch_dir = ""

# Miniflare configuration for local development
[miniflare]
kv_persist = true
cache_persist = true
d1_persist = true
r2_persist = true

# Development configuration
[dev]
ip = "127.0.0.1"
port = 8787
local_protocol = "http"
upstream_protocol = "https"

# Deployment configuration
[deploy]
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Custom domains (configure these in Cloudflare dashboard)
# *.trends.yourdomain.com -> trendsite-redirect-worker

# Required Cloudflare features:
# - Workers (paid plan for custom domains)
# - Workers KV (for storing redirects and analytics)
# - DNS management
# - SSL/TLS certificates

# Setup instructions:
# 1. Install Wrangler CLI: npm install -g wrangler
# 2. Login to Cloudflare: wrangler login
# 3. Create KV namespaces:
#    wrangler kv:namespace create "TREND_REDIRECTS"
#    wrangler kv:namespace create "ANALYTICS_DATA"
# 4. Update the namespace IDs in this file
# 5. Deploy: wrangler deploy
# 6. Configure custom domain in Cloudflare dashboard
# 7. Set up DNS records for *.trends.yourdomain.com

# KV namespace management commands:
# Create namespace: wrangler kv:namespace create "NAMESPACE_NAME"
# List namespaces: wrangler kv:namespace list
# Put key-value: wrangler kv:key put --binding=TREND_REDIRECTS "trend-slug" '{"url":"https://app.com","trend_id":"123"}'
# Get value: wrangler kv:key get --binding=TREND_REDIRECTS "trend-slug"
# Delete key: wrangler kv:key delete --binding=TREND_REDIRECTS "trend-slug"
# List keys: wrangler kv:key list --binding=TREND_REDIRECTS

# Analytics KV structure:
# Key format: "trend-slug:timestamp:random-id"
# Value: JSON object with visit data
# Daily counters: "daily:trend-slug:YYYY-MM-DD" -> count

# Redirect KV structure:
# Key: trend-slug (e.g., "ai-chatbots-2024")
# Value: {"url": "https://deployed-app.com", "trend_id": "uuid", "created_at": "2024-01-01T00:00:00Z"}

# Monitoring and debugging:
# View logs: wrangler tail
# View metrics: wrangler metrics
# Test locally: wrangler dev

# Security considerations:
# - IP addresses are hashed for privacy
# - Analytics data has automatic expiration
# - No sensitive data stored in KV
# - CORS headers configured for API access

# Performance optimizations:
# - Minimal CPU usage (< 50ms)
# - Small memory footprint (< 128MB)
# - Efficient KV operations
# - Proper caching headers
# - Async analytics tracking

# Scaling considerations:
# - KV can handle millions of operations
# - Worker can handle 100,000+ requests per second
# - Analytics data automatically expires
# - Daily cleanup prevents storage bloat

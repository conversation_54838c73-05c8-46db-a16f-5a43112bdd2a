# House-keeping Module - Implementation Plan

## Overview
The House-keeping Module manages automated cleanup and maintenance tasks to ensure system efficiency and cost optimization. It handles content expiration, database maintenance, file cleanup, DNS record management, and CDN cache purging on a scheduled basis.

## Dependencies
- **Database Module**: Content and deployment record management
- **DNS Module**: DNS record cleanup coordination
- **Coolify Deploy Module**: Deployment cleanup and resource management
- **Monitoring Module**: Cleanup operation logging and metrics
- **Security Module**: Secure credential access for cleanup operations

## Interfaces
### Outbound
- **Database**: Cleanup queries and maintenance operations
- **DNS API**: DNS record deletion and cleanup
- **Coolify API**: Deployment resource cleanup
- **CDN API**: Cache purging and invalidation
- **File System**: Static file and asset cleanup

### Inbound
- **Celery Beat**: Scheduled cleanup task execution
- **Dashboard**: Manual cleanup trigger controls
- **API**: Cleanup status and configuration endpoints

## Implementation Phases

### Phase 1: Core Cleanup Tasks
**Duration**: Week 8-9

#### 1.1 Content Expiration Handler
```python
# housekeeping/tasks/content_cleanup.py
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository
from database.models.deployment_model import DeploymentRepository
from .base_cleanup_task import BaseCleanupTask

class ContentCleanupTask(BaseCleanupTask):
    def __init__(self):
        super().__init__('content_cleanup')
        self.trend_repo = TrendRepository()
        self.content_repo = ContentRepository()
        self.deployment_repo = DeploymentRepository()
    
    async def execute(self) -> Dict[str, Any]:
        """Execute content cleanup for expired trends"""
        try:
            # Get expired trends
            expired_trends = await self.trend_repo.get_expired_trends()
            
            cleanup_results = {
                'expired_trends_found': len(expired_trends),
                'cleaned_up': 0,
                'errors': []
            }
            
            for trend in expired_trends:
                try:
                    await self.cleanup_trend(trend)
                    cleanup_results['cleaned_up'] += 1
                    
                    self.logger.info(
                        f"Cleaned up expired trend: {trend['keyword']} (ID: {trend['id']})"
                    )
                    
                except Exception as e:
                    error_msg = f"Failed to cleanup trend {trend['id']}: {str(e)}"
                    cleanup_results['errors'].append(error_msg)
                    self.logger.error(error_msg)
            
            # Log cleanup summary
            await self.log_cleanup_summary(cleanup_results)
            
            return cleanup_results
            
        except Exception as e:
            self.logger.error(f"Content cleanup task failed: {str(e)}")
            raise
    
    async def cleanup_trend(self, trend: Dict[str, Any]):
        """Cleanup all resources for a single trend"""
        trend_id = trend['id']
        
        # 1. Update trend status to expired
        await self.trend_repo.update(trend_id, {
            'status': 'expired',
            'updated_at': datetime.utcnow()
        })
        
        # 2. Get associated deployments
        deployments = await self.deployment_repo.get_by_trend_id(trend_id)
        
        # 3. Cleanup each deployment
        for deployment in deployments:
            await self.cleanup_deployment(deployment)
        
        # 4. Trigger DNS cleanup
        from housekeeping.tasks.dns_cleanup import DNSCleanupTask
        dns_cleanup = DNSCleanupTask()
        await dns_cleanup.cleanup_trend_dns(trend_id)
        
        # 5. Trigger file cleanup
        from housekeeping.tasks.file_cleanup import FileCleanupTask
        file_cleanup = FileCleanupTask()
        await file_cleanup.cleanup_trend_files(trend_id)
        
        # 6. Trigger cache cleanup
        from housekeeping.tasks.cache_cleanup import CacheCleanupTask
        cache_cleanup = CacheCleanupTask()
        await cache_cleanup.purge_trend_cache(trend_id)
    
    async def cleanup_deployment(self, deployment: Dict[str, Any]):
        """Cleanup resources for a specific deployment"""
        deployment_id = deployment['id']
        
        try:
            # Mark deployment as cleaned up
            await self.deployment_repo.update(deployment_id, {
                'status': 'cleaned_up',
                'updated_at': datetime.utcnow()
            })
            
            # If using Coolify, could trigger resource cleanup
            # This depends on your deployment strategy
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup deployment {deployment_id}: {str(e)}")
    
    async def get_cleanup_candidates(self, days_ahead: int = 1) -> List[Dict[str, Any]]:
        """Get trends that will expire in the next N days"""
        cutoff_date = datetime.utcnow() + timedelta(days=days_ahead)
        
        query = """
            SELECT * FROM trends 
            WHERE status = 'live' 
            AND expire_at IS NOT NULL 
            AND expire_at <= $1
            ORDER BY expire_at ASC
        """
        
        async with self.trend_repo.pool.acquire() as conn:
            rows = await conn.fetch(query, cutoff_date)
            return [dict(row) for row in rows]
```

#### 1.2 DNS Cleanup Task
```python
# housekeeping/tasks/dns_cleanup.py
from typing import Dict, Any, List
from dns.dns_manager import DNSManager
from database.models.dns_model import DNSRepository
from .base_cleanup_task import BaseCleanupTask

class DNSCleanupTask(BaseCleanupTask):
    def __init__(self):
        super().__init__('dns_cleanup')
        self.dns_manager = DNSManager()
        self.dns_repo = DNSRepository()
    
    async def execute(self) -> Dict[str, Any]:
        """Execute DNS cleanup for expired trends"""
        try:
            # Get DNS records for expired trends
            expired_dns_records = await self.get_expired_dns_records()
            
            cleanup_results = {
                'records_found': len(expired_dns_records),
                'cleaned_up': 0,
                'errors': []
            }
            
            for dns_record in expired_dns_records:
                try:
                    await self.cleanup_dns_record(dns_record)
                    cleanup_results['cleaned_up'] += 1
                    
                except Exception as e:
                    error_msg = f"Failed to cleanup DNS record {dns_record['id']}: {str(e)}"
                    cleanup_results['errors'].append(error_msg)
                    self.logger.error(error_msg)
            
            return cleanup_results
            
        except Exception as e:
            self.logger.error(f"DNS cleanup task failed: {str(e)}")
            raise
    
    async def cleanup_trend_dns(self, trend_id: str):
        """Cleanup DNS records for a specific trend"""
        try:
            success = await self.dns_manager.delete_trend_dns(trend_id)
            if success:
                self.logger.info(f"Successfully cleaned up DNS for trend {trend_id}")
            else:
                self.logger.warning(f"DNS cleanup returned false for trend {trend_id}")
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup DNS for trend {trend_id}: {str(e)}")
            raise
    
    async def cleanup_dns_record(self, dns_record: Dict[str, Any]):
        """Cleanup a specific DNS record"""
        record_id = dns_record['id']
        trend_id = dns_record['trend_id']
        
        try:
            # Delete from Cloudflare
            if dns_record['cloudflare_record_id']:
                await self.dns_manager.cf_api.delete_dns_record(
                    dns_record['cloudflare_record_id']
                )
            
            # Update database record
            await self.dns_repo.update(record_id, {
                'status': 'deleted',
                'deleted_at': datetime.utcnow()
            })
            
            # Remove from worker cache
            slug = dns_record['subdomain'].split('.')[0]
            await self.dns_manager.remove_worker_cache(slug)
            
            self.logger.info(f"Cleaned up DNS record {record_id} for trend {trend_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup DNS record {record_id}: {str(e)}")
            raise
    
    async def get_expired_dns_records(self) -> List[Dict[str, Any]]:
        """Get DNS records for expired trends"""
        query = """
            SELECT dr.* FROM dns_records dr
            JOIN trends t ON dr.trend_id = t.id
            WHERE t.status = 'expired'
            AND dr.status = 'active'
        """
        
        async with self.dns_repo.pool.acquire() as conn:
            rows = await conn.fetch(query)
            return [dict(row) for row in rows]
```

#### 1.3 Cache Cleanup Task
```python
# housekeeping/tasks/cache_cleanup.py
from typing import Dict, Any, List
import aiohttp
from .base_cleanup_task import BaseCleanupTask

class CacheCleanupTask(BaseCleanupTask):
    def __init__(self):
        super().__init__('cache_cleanup')
        self.cloudflare_api = self.get_cloudflare_api()
    
    async def execute(self) -> Dict[str, Any]:
        """Execute cache cleanup operations"""
        try:
            cleanup_results = {
                'cache_purged': False,
                'urls_purged': 0,
                'errors': []
            }
            
            # Get expired trend URLs to purge
            expired_urls = await self.get_expired_trend_urls()
            
            if expired_urls:
                # Purge specific URLs
                success = await self.purge_urls(expired_urls)
                cleanup_results['cache_purged'] = success
                cleanup_results['urls_purged'] = len(expired_urls) if success else 0
            
            # Perform general cache maintenance
            await self.perform_cache_maintenance()
            
            return cleanup_results
            
        except Exception as e:
            self.logger.error(f"Cache cleanup task failed: {str(e)}")
            raise
    
    async def purge_trend_cache(self, trend_id: str):
        """Purge cache for a specific trend"""
        try:
            # Get trend URLs
            urls = await self.get_trend_urls(trend_id)
            
            if urls:
                await self.purge_urls(urls)
                self.logger.info(f"Purged cache for trend {trend_id}: {len(urls)} URLs")
            
        except Exception as e:
            self.logger.error(f"Failed to purge cache for trend {trend_id}: {str(e)}")
    
    async def purge_urls(self, urls: List[str]) -> bool:
        """Purge specific URLs from CDN cache"""
        try:
            success = await self.cloudflare_api.purge_cache(urls)
            if success:
                self.logger.info(f"Successfully purged {len(urls)} URLs from cache")
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to purge URLs from cache: {str(e)}")
            return False
    
    async def get_expired_trend_urls(self) -> List[str]:
        """Get URLs for expired trends that need cache purging"""
        query = """
            SELECT DISTINCT dr.subdomain, d.deploy_url
            FROM dns_records dr
            JOIN trends t ON dr.trend_id = t.id
            LEFT JOIN deployments d ON d.trend_id = t.id
            WHERE t.status = 'expired'
            AND dr.status = 'active'
        """
        
        urls = []
        async with self.dns_repo.pool.acquire() as conn:
            rows = await conn.fetch(query)
            for row in rows:
                # Add subdomain URL
                urls.append(f"https://{row['subdomain']}")
                # Add deploy URL if available
                if row['deploy_url']:
                    urls.append(row['deploy_url'])
        
        return urls
```

### Phase 2: Database Maintenance
**Duration**: Week 9

#### 2.1 Database Optimization Task
```python
# housekeeping/tasks/db_maintenance.py
from typing import Dict, Any
import asyncpg
from .base_cleanup_task import BaseCleanupTask

class DatabaseMaintenanceTask(BaseCleanupTask):
    def __init__(self):
        super().__init__('db_maintenance')
    
    async def execute(self) -> Dict[str, Any]:
        """Execute database maintenance operations"""
        try:
            maintenance_results = {
                'vacuum_completed': False,
                'analyze_completed': False,
                'old_logs_cleaned': 0,
                'errors': []
            }
            
            # Perform VACUUM and ANALYZE
            await self.vacuum_analyze_tables()
            maintenance_results['vacuum_completed'] = True
            maintenance_results['analyze_completed'] = True
            
            # Clean old system logs
            cleaned_logs = await self.clean_old_system_logs()
            maintenance_results['old_logs_cleaned'] = cleaned_logs
            
            # Clean old analytics data
            await self.clean_old_analytics_data()
            
            # Update table statistics
            await self.update_table_statistics()
            
            return maintenance_results
            
        except Exception as e:
            self.logger.error(f"Database maintenance task failed: {str(e)}")
            raise
    
    async def vacuum_analyze_tables(self):
        """Perform VACUUM and ANALYZE on all tables"""
        tables = [
            'trends', 'content', 'deployments', 
            'dns_records', 'analytics', 'system_logs'
        ]
        
        async with self.get_db_connection() as conn:
            for table in tables:
                try:
                    # VACUUM to reclaim space
                    await conn.execute(f"VACUUM {table}")
                    
                    # ANALYZE to update statistics
                    await conn.execute(f"ANALYZE {table}")
                    
                    self.logger.info(f"Completed VACUUM ANALYZE for table: {table}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to VACUUM ANALYZE table {table}: {str(e)}")
    
    async def clean_old_system_logs(self, retention_days: int = 30) -> int:
        """Clean system logs older than retention period"""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        query = """
            DELETE FROM system_logs 
            WHERE created_at < $1 
            AND level NOT IN ('error', 'critical')
        """
        
        async with self.get_db_connection() as conn:
            result = await conn.execute(query, cutoff_date)
            deleted_count = int(result.split()[-1])
            
            self.logger.info(f"Cleaned {deleted_count} old system log entries")
            return deleted_count
    
    async def clean_old_analytics_data(self, retention_days: int = 90):
        """Clean analytics data older than retention period"""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        # Archive old analytics data before deletion
        await self.archive_analytics_data(cutoff_date)
        
        query = "DELETE FROM analytics WHERE created_at < $1"
        
        async with self.get_db_connection() as conn:
            result = await conn.execute(query, cutoff_date)
            deleted_count = int(result.split()[-1])
            
            self.logger.info(f"Cleaned {deleted_count} old analytics entries")
    
    async def update_table_statistics(self):
        """Update database statistics for query optimization"""
        async with self.get_db_connection() as conn:
            # Update PostgreSQL statistics
            await conn.execute("SELECT pg_stat_reset()")
            
            # Refresh materialized views if any
            # await conn.execute("REFRESH MATERIALIZED VIEW trend_analytics")
            
            self.logger.info("Updated database statistics")
```

## Key Components

### Directory Structure
```
housekeeping/
├── __init__.py
├── scheduler.py          # Task scheduling and orchestration
├── config.py            # Cleanup policies and thresholds
├── tasks/
│   ├── __init__.py
│   ├── base_cleanup_task.py # Abstract base for cleanup tasks
│   ├── content_cleanup.py   # Content expiration handling
│   ├── dns_cleanup.py       # DNS record management
│   ├── cache_cleanup.py     # CDN cache purging
│   ├── file_cleanup.py      # File system cleanup
│   ├── db_maintenance.py    # Database optimization
│   └── log_cleanup.py       # Log rotation and archival
├── monitors/
│   ├── cleanup_monitor.py   # Cleanup operation monitoring
│   └── resource_monitor.py  # Resource usage monitoring
└── utils/
    ├── backup_manager.py    # Backup operations
    ├── notification_sender.py # Cleanup notifications
    └── metrics_collector.py   # Cleanup metrics
```

### Configuration
```python
# housekeeping/config.py
HOUSEKEEPING_CONFIG = {
    'content_cleanup': {
        'enabled': True,
        'default_ttl_days': 14,
        'grace_period_hours': 24,
        'batch_size': 50,
        'max_concurrent_cleanups': 5
    },
    'dns_cleanup': {
        'enabled': True,
        'propagation_wait_seconds': 300,
        'retry_attempts': 3,
        'cleanup_delay_minutes': 30
    },
    'cache_cleanup': {
        'enabled': True,
        'purge_batch_size': 100,
        'cloudflare_zone_id': env('CLOUDFLARE_ZONE_ID'),
        'purge_everything_threshold': 1000
    },
    'database_maintenance': {
        'enabled': True,
        'vacuum_schedule': '0 2 * * 0',  # Weekly on Sunday at 2 AM
        'log_retention_days': 30,
        'analytics_retention_days': 90,
        'backup_before_maintenance': True
    },
    'monitoring': {
        'enabled': True,
        'alert_on_failures': True,
        'metrics_retention_days': 365,
        'notification_channels': ['email', 'slack']
    }
}
```

## Celery Task Configuration

### Scheduled Tasks
```python
# housekeeping/tasks.py
from celery import Celery
from celery.schedules import crontab
from .tasks.content_cleanup import ContentCleanupTask
from .tasks.dns_cleanup import DNSCleanupTask
from .tasks.cache_cleanup import CacheCleanupTask
from .tasks.db_maintenance import DatabaseMaintenanceTask

# Celery Beat Schedule
CELERY_BEAT_SCHEDULE = {
    'cleanup-expired-content': {
        'task': 'housekeeping.tasks.cleanup_expired_content',
        'schedule': crontab(hour=2, minute=0),  # Daily at 02:00 UTC
    },
    'dns-cleanup': {
        'task': 'housekeeping.tasks.dns_cleanup',
        'schedule': crontab(hour=2, minute=30),  # Daily at 02:30 UTC
    },
    'cache-cleanup': {
        'task': 'housekeeping.tasks.cache_cleanup',
        'schedule': crontab(hour=3, minute=0),  # Daily at 03:00 UTC
    },
    'database-maintenance': {
        'task': 'housekeeping.tasks.database_maintenance',
        'schedule': crontab(hour=1, minute=0, day_of_week=0),  # Weekly on Sunday
    },
    'cleanup-monitoring': {
        'task': 'housekeeping.tasks.cleanup_monitoring',
        'schedule': crontab(minute='*/15'),  # Every 15 minutes
    }
}

@celery.task(bind=True, max_retries=3)
def cleanup_expired_content(self):
    try:
        task = ContentCleanupTask()
        result = task.execute()
        return result
    except Exception as exc:
        self.retry(countdown=300, exc=exc)  # Retry after 5 minutes

@celery.task(bind=True, max_retries=3)
def dns_cleanup(self):
    try:
        task = DNSCleanupTask()
        result = task.execute()
        return result
    except Exception as exc:
        self.retry(countdown=300, exc=exc)

@celery.task(bind=True, max_retries=2)
def database_maintenance(self):
    try:
        task = DatabaseMaintenanceTask()
        result = task.execute()
        return result
    except Exception as exc:
        self.retry(countdown=3600, exc=exc)  # Retry after 1 hour
```

## API Endpoints

### Cleanup Control API
```python
# housekeeping/api.py
from fastapi import APIRouter, Depends, HTTPException
from .tasks import cleanup_expired_content, dns_cleanup, cache_cleanup

router = APIRouter(prefix="/api/housekeeping")

@router.post("/cleanup/content")
async def trigger_content_cleanup(
    current_user = Depends(get_current_user)
):
    """Manually trigger content cleanup"""
    if not has_permission(current_user, 'trigger_cleanup'):
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    task = cleanup_expired_content.delay()
    return {"task_id": task.id, "status": "started"}

@router.post("/cleanup/dns")
async def trigger_dns_cleanup(
    current_user = Depends(get_current_user)
):
    """Manually trigger DNS cleanup"""
    task = dns_cleanup.delay()
    return {"task_id": task.id, "status": "started"}

@router.get("/status")
async def get_cleanup_status():
    """Get overall cleanup system status"""
    # Implementation to check cleanup task statuses
    pass

@router.get("/metrics")
async def get_cleanup_metrics():
    """Get cleanup operation metrics"""
    # Implementation to return cleanup statistics
    pass
```

## Testing Strategy

### Unit Tests
```python
# tests/test_content_cleanup.py
import pytest
from housekeeping.tasks.content_cleanup import ContentCleanupTask
from database.models.trend_model import TrendRepository

@pytest.mark.asyncio
async def test_content_cleanup():
    # Create expired test trend
    trend_repo = TrendRepository()
    trend_id = await create_expired_test_trend(trend_repo)
    
    # Run cleanup
    cleanup_task = ContentCleanupTask()
    result = await cleanup_task.execute()
    
    # Verify cleanup
    assert result['cleaned_up'] > 0
    
    # Verify trend status changed
    trend = await trend_repo.get_by_id(trend_id)
    assert trend['status'] == 'expired'
```

## Deployment Notes

### Environment Variables
```bash
# Cleanup Configuration
CONTENT_TTL_DAYS=14
DNS_CLEANUP_ENABLED=true
CACHE_CLEANUP_ENABLED=true
DB_MAINTENANCE_ENABLED=true

# Retention Policies
LOG_RETENTION_DAYS=30
ANALYTICS_RETENTION_DAYS=90
BACKUP_RETENTION_DAYS=30

# Notification Settings
CLEANUP_ALERTS_ENABLED=true
SLACK_WEBHOOK_URL=your_slack_webhook
EMAIL_NOTIFICATIONS=<EMAIL>
```

## Success Criteria
- Automated cleanup of 100% of expired content within 24 hours
- Zero manual intervention required for routine cleanup
- Database performance maintained through regular optimization
- Comprehensive cleanup monitoring and alerting
- Cost optimization through efficient resource cleanup

# Infrastructure as Code - Implementation Plan

## Overview
The Infrastructure as Code Module manages all infrastructure provisioning, configuration, and deployment automation using Terraform, Ansible, and Docker. It provides reproducible, version-controlled infrastructure management for the entire trend platform across multiple environments.

## Dependencies
- **All System Modules**: Infrastructure requirements and resource specifications
- **Security Module**: Secure infrastructure configuration and secret management
- **Monitoring Module**: Infrastructure monitoring and alerting setup

## Interfaces
### Outbound
- **OCI Provider**: Cloud resource provisioning and management
- **Docker Registry**: Container image storage and distribution
- **DNS Providers**: Domain and DNS configuration
- **CDN Services**: Content delivery network setup

### Inbound
- **CI/CD Pipeline**: Automated infrastructure deployment triggers
- **Dashboard**: Infrastructure status monitoring and manual controls
- **Monitoring Systems**: Infrastructure health and performance metrics

## Implementation Phases

### Phase 1: Terraform Infrastructure Setup
**Duration**: Week 1-2

#### 1.1 OCI Provider Configuration
```hcl
# terraform/providers.tf
terraform {
  required_version = ">= 1.0"
  
  required_providers {
    oci = {
      source  = "oracle/oci"
      version = "~> 5.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
    docker = {
      source  = "kreuzwerker/docker"
      version = "~> 3.0"
    }
  }
  
  backend "s3" {
    bucket = "trend-platform-terraform-state"
    key    = "infrastructure/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "oci" {
  tenancy_ocid     = var.oci_tenancy_ocid
  user_ocid        = var.oci_user_ocid
  fingerprint      = var.oci_fingerprint
  private_key_path = var.oci_private_key_path
  region           = var.oci_region
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

provider "docker" {
  host = "unix:///var/run/docker.sock"
}
```

#### 1.2 Network Infrastructure
```hcl
# terraform/network.tf
# Virtual Cloud Network
resource "oci_core_vcn" "trend_platform_vcn" {
  compartment_id = var.compartment_id
  cidr_blocks    = ["10.0.0.0/16"]
  display_name   = "trend-platform-vcn"
  dns_label      = "trendplatform"
  
  tags = local.common_tags
}

# Internet Gateway
resource "oci_core_internet_gateway" "trend_platform_igw" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "trend-platform-igw"
  enabled        = true
  
  tags = local.common_tags
}

# Public Subnet
resource "oci_core_subnet" "public_subnet" {
  compartment_id      = var.compartment_id
  vcn_id              = oci_core_vcn.trend_platform_vcn.id
  cidr_block          = "********/24"
  display_name        = "public-subnet"
  dns_label           = "public"
  route_table_id      = oci_core_route_table.public_route_table.id
  security_list_ids   = [oci_core_security_list.public_security_list.id]
  
  tags = local.common_tags
}

# Private Subnet
resource "oci_core_subnet" "private_subnet" {
  compartment_id                 = var.compartment_id
  vcn_id                         = oci_core_vcn.trend_platform_vcn.id
  cidr_block                     = "********/24"
  display_name                   = "private-subnet"
  dns_label                      = "private"
  route_table_id                 = oci_core_route_table.private_route_table.id
  security_list_ids              = [oci_core_security_list.private_security_list.id]
  prohibit_public_ip_on_vnic     = true
  
  tags = local.common_tags
}

# Route Tables
resource "oci_core_route_table" "public_route_table" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "public-route-table"
  
  route_rules {
    destination       = "0.0.0.0/0"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_internet_gateway.trend_platform_igw.id
  }
  
  tags = local.common_tags
}

resource "oci_core_route_table" "private_route_table" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "private-route-table"
  
  route_rules {
    destination       = "0.0.0.0/0"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_nat_gateway.nat_gateway.id
  }
  
  tags = local.common_tags
}

# NAT Gateway for private subnet internet access
resource "oci_core_nat_gateway" "nat_gateway" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "nat-gateway"
  
  tags = local.common_tags
}
```

#### 1.3 Security Groups
```hcl
# terraform/security.tf
# Public Security List
resource "oci_core_security_list" "public_security_list" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "public-security-list"
  
  # Ingress Rules
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    
    tcp_options {
      min = 80
      max = 80
    }
  }
  
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    
    tcp_options {
      min = 443
      max = 443
    }
  }
  
  ingress_security_rules {
    protocol = "6" # TCP
    source   = var.admin_cidr_blocks
    
    tcp_options {
      min = 22
      max = 22
    }
  }
  
  # Egress Rules
  egress_security_rules {
    protocol    = "all"
    destination = "0.0.0.0/0"
  }
  
  tags = local.common_tags
}

# Private Security List
resource "oci_core_security_list" "private_security_list" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "private-security-list"
  
  # Ingress Rules - Allow traffic from public subnet
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "********/24"
    
    tcp_options {
      min = 5432
      max = 5432
    }
  }
  
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "********/24"
    
    tcp_options {
      min = 6379
      max = 6379
    }
  }
  
  # Egress Rules
  egress_security_rules {
    protocol    = "all"
    destination = "0.0.0.0/0"
  }
  
  tags = local.common_tags
}
```

#### 1.4 Compute Instances
```hcl
# terraform/compute.tf
# Main Application Server
resource "oci_core_instance" "app_server" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_id
  display_name        = "trend-platform-app-server"
  shape               = "VM.Standard.A1.Flex"
  
  shape_config {
    ocpus         = 4
    memory_in_gbs = 24
  }
  
  create_vnic_details {
    subnet_id                 = oci_core_subnet.public_subnet.id
    display_name              = "app-server-vnic"
    assign_public_ip          = true
    assign_private_dns_record = true
    hostname_label            = "app-server"
  }
  
  source_details {
    source_type = "image"
    source_id   = data.oci_core_images.ubuntu_images.images[0].id
    boot_volume_size_in_gbs = 100
  }
  
  metadata = {
    ssh_authorized_keys = file(var.ssh_public_key_path)
    user_data = base64encode(templatefile("${path.module}/scripts/app-server-init.sh", {
      docker_compose_version = var.docker_compose_version
      environment           = var.environment
    }))
  }
  
  tags = merge(local.common_tags, {
    Role = "application-server"
  })
}

# Database Server
resource "oci_core_instance" "db_server" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_id
  display_name        = "trend-platform-db-server"
  shape               = "VM.Standard.A1.Flex"
  
  shape_config {
    ocpus         = 2
    memory_in_gbs = 12
  }
  
  create_vnic_details {
    subnet_id                 = oci_core_subnet.private_subnet.id
    display_name              = "db-server-vnic"
    assign_public_ip          = false
    assign_private_dns_record = true
    hostname_label            = "db-server"
  }
  
  source_details {
    source_type = "image"
    source_id   = data.oci_core_images.ubuntu_images.images[0].id
    boot_volume_size_in_gbs = 200
  }
  
  metadata = {
    ssh_authorized_keys = file(var.ssh_public_key_path)
    user_data = base64encode(templatefile("${path.module}/scripts/db-server-init.sh", {
      postgres_version = var.postgres_version
      redis_version   = var.redis_version
    }))
  }
  
  tags = merge(local.common_tags, {
    Role = "database-server"
  })
}

# Load Balancer
resource "oci_load_balancer_load_balancer" "app_load_balancer" {
  compartment_id = var.compartment_id
  display_name   = "trend-platform-lb"
  shape          = "flexible"
  
  shape_details {
    minimum_bandwidth_in_mbps = 10
    maximum_bandwidth_in_mbps = 100
  }
  
  subnet_ids = [oci_core_subnet.public_subnet.id]
  
  is_private = false
  
  tags = local.common_tags
}
```

### Phase 2: Configuration Management
**Duration**: Week 2-3

#### 2.1 Ansible Playbooks
```yaml
# ansible/playbooks/app-server.yml
---
- name: Configure Application Server
  hosts: app_servers
  become: yes
  vars:
    docker_compose_version: "2.21.0"
    node_version: "18"
    
  tasks:
    - name: Update system packages
      apt:
        update_cache: yes
        upgrade: dist
        
    - name: Install required packages
      apt:
        name:
          - docker.io
          - docker-compose
          - nginx
          - certbot
          - python3-certbot-nginx
          - htop
          - curl
          - git
        state: present
        
    - name: Start and enable Docker
      systemd:
        name: docker
        state: started
        enabled: yes
        
    - name: Add ubuntu user to docker group
      user:
        name: ubuntu
        groups: docker
        append: yes
        
    - name: Install Node.js
      shell: |
        curl -fsSL https://deb.nodesource.com/setup_{{ node_version }}.x | sudo -E bash -
        apt-get install -y nodejs
        
    - name: Configure Nginx
      template:
        src: nginx.conf.j2
        dest: /etc/nginx/sites-available/default
      notify: restart nginx
      
    - name: Configure firewall
      ufw:
        rule: allow
        port: "{{ item }}"
      loop:
        - "22"
        - "80"
        - "443"
        
    - name: Enable firewall
      ufw:
        state: enabled
        
  handlers:
    - name: restart nginx
      systemd:
        name: nginx
        state: restarted
```

#### 2.2 Docker Compose Configuration
```yaml
# docker/docker-compose.yml
version: '3.8'

services:
  # Main Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: trend-platform-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    ports:
      - "3000:3000"
    depends_on:
      - redis
    volumes:
      - app_data:/app/data
      - ./logs:/app/logs
    networks:
      - app_network
      
  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trend-platform-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network
      
  # Celery Worker
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: trend-platform-worker
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${REDIS_URL}
    depends_on:
      - redis
    volumes:
      - app_data:/app/data
      - ./logs:/app/logs
    networks:
      - app_network
      
  # Celery Beat Scheduler
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: trend-platform-scheduler
    restart: unless-stopped
    command: celery -A app.celery beat --loglevel=info
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${REDIS_URL}
    depends_on:
      - redis
    volumes:
      - app_data:/app/data
      - ./logs:/app/logs
    networks:
      - app_network
      
  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: trend-platform-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - app_network
      
  grafana:
    image: grafana/grafana:latest
    container_name: trend-platform-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/dashboards:/etc/grafana/provisioning/dashboards
    networks:
      - app_network
      
  # Log Aggregation
  loki:
    image: grafana/loki:latest
    container_name: trend-platform-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    networks:
      - app_network

volumes:
  app_data:
  redis_data:
  prometheus_data:
  grafana_data:
  loki_data:

networks:
  app_network:
    driver: bridge
```

## Key Components

### Directory Structure
```
infrastructure/
├── terraform/
│   ├── main.tf              # Main configuration
│   ├── variables.tf         # Input variables
│   ├── outputs.tf           # Output values
│   ├── providers.tf         # Provider configurations
│   ├── network.tf           # Network infrastructure
│   ├── compute.tf           # Compute resources
│   ├── security.tf          # Security groups and policies
│   ├── storage.tf           # Storage resources
│   ├── dns.tf               # DNS configuration
│   └── modules/
│       ├── vpc/             # VPC module
│       ├── compute/         # Compute module
│       └── monitoring/      # Monitoring module
├── ansible/
│   ├── playbooks/
│   │   ├── app-server.yml   # Application server setup
│   │   ├── db-server.yml    # Database server setup
│   │   └── monitoring.yml   # Monitoring setup
│   ├── roles/
│   │   ├── docker/          # Docker installation role
│   │   ├── nginx/           # Nginx configuration role
│   │   └── security/        # Security hardening role
│   ├── inventory/
│   │   ├── production.yml   # Production inventory
│   │   └── staging.yml      # Staging inventory
│   └── group_vars/
│       ├── all.yml          # Global variables
│       └── production.yml   # Production variables
├── docker/
│   ├── Dockerfile           # Application container
│   ├── docker-compose.yml   # Multi-container setup
│   └── scripts/
│       ├── entrypoint.sh    # Container entrypoint
│       └── healthcheck.sh   # Health check script
├── scripts/
│   ├── deploy.sh            # Deployment script
│   ├── backup.sh            # Backup script
│   ├── restore.sh           # Restore script
│   └── monitoring.sh        # Monitoring setup
└── environments/
    ├── production/
    │   ├── terraform.tfvars  # Production variables
    │   └── ansible.cfg       # Ansible configuration
    └── staging/
        ├── terraform.tfvars  # Staging variables
        └── ansible.cfg       # Ansible configuration
```

### Configuration
```hcl
# terraform/variables.tf
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "oci_region" {
  description = "OCI region"
  type        = string
  default     = "us-ashburn-1"
}

variable "compartment_id" {
  description = "OCI compartment ID"
  type        = string
}

variable "ssh_public_key_path" {
  description = "Path to SSH public key"
  type        = string
  default     = "~/.ssh/id_rsa.pub"
}

variable "admin_cidr_blocks" {
  description = "CIDR blocks for admin access"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "app_instance_count" {
  description = "Number of application instances"
  type        = number
  default     = 2
}

variable "db_instance_type" {
  description = "Database instance type"
  type        = string
  default     = "VM.Standard.A1.Flex"
}

# Local values
locals {
  common_tags = {
    Environment = var.environment
    Project     = "trend-platform"
    ManagedBy   = "terraform"
    CreatedAt   = timestamp()
  }
}
```

## Deployment Scripts

### Automated Deployment
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=${1:-production}
ACTION=${2:-apply}

echo "Deploying to $ENVIRONMENT environment..."

# Change to terraform directory
cd terraform

# Initialize Terraform
terraform init -backend-config="environments/$ENVIRONMENT/backend.conf"

# Plan infrastructure changes
terraform plan -var-file="environments/$ENVIRONMENT/terraform.tfvars" -out=tfplan

if [ "$ACTION" = "plan" ]; then
    echo "Plan completed. Review the changes above."
    exit 0
fi

# Apply infrastructure changes
terraform apply tfplan

# Get infrastructure outputs
APP_SERVER_IP=$(terraform output -raw app_server_public_ip)
DB_SERVER_IP=$(terraform output -raw db_server_private_ip)

echo "Infrastructure deployed successfully!"
echo "App Server IP: $APP_SERVER_IP"
echo "DB Server IP: $DB_SERVER_IP"

# Configure servers with Ansible
cd ../ansible

# Update inventory with new IPs
cat > inventory/$ENVIRONMENT.yml <<EOF
all:
  children:
    app_servers:
      hosts:
        app-server:
          ansible_host: $APP_SERVER_IP
          ansible_user: ubuntu
    db_servers:
      hosts:
        db-server:
          ansible_host: $DB_SERVER_IP
          ansible_user: ubuntu
          ansible_ssh_common_args: '-o ProxyJump=ubuntu@$APP_SERVER_IP'
EOF

# Run Ansible playbooks
ansible-playbook -i inventory/$ENVIRONMENT.yml playbooks/app-server.yml
ansible-playbook -i inventory/$ENVIRONMENT.yml playbooks/db-server.yml

echo "Configuration completed successfully!"

# Deploy application containers
ssh ubuntu@$APP_SERVER_IP << 'EOF'
cd /opt/trend-platform
docker-compose pull
docker-compose up -d
docker-compose ps
EOF

echo "Application deployed successfully!"
```

## Testing Strategy

### Infrastructure Tests
```python
# tests/test_infrastructure.py
import pytest
import subprocess
import requests
import time

def test_terraform_plan():
    """Test Terraform plan execution"""
    result = subprocess.run([
        'terraform', 'plan', 
        '-var-file=environments/staging/terraform.tfvars',
        '-detailed-exitcode'
    ], cwd='terraform', capture_output=True, text=True)
    
    # Exit code 0 = no changes, 2 = changes planned
    assert result.returncode in [0, 2]

def test_application_health():
    """Test application health after deployment"""
    app_url = "https://app.yourdomain.com/api/health"
    
    # Wait for application to start
    time.sleep(30)
    
    response = requests.get(app_url, timeout=10)
    assert response.status_code == 200
    
    health_data = response.json()
    assert health_data['status'] == 'healthy'

def test_database_connectivity():
    """Test database connectivity"""
    # This would test database connection from app server
    pass

def test_monitoring_stack():
    """Test monitoring stack deployment"""
    prometheus_url = "https://monitoring.yourdomain.com/prometheus"
    grafana_url = "https://monitoring.yourdomain.com/grafana"
    
    # Test Prometheus
    response = requests.get(f"{prometheus_url}/api/v1/status/config")
    assert response.status_code == 200
    
    # Test Grafana
    response = requests.get(f"{grafana_url}/api/health")
    assert response.status_code == 200
```

## Deployment Notes

### Environment Variables
```bash
# OCI Configuration
export OCI_TENANCY_OCID="ocid1.tenancy.oc1..xxx"
export OCI_USER_OCID="ocid1.user.oc1..xxx"
export OCI_FINGERPRINT="xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx"
export OCI_PRIVATE_KEY_PATH="~/.oci/oci_api_key.pem"
export OCI_REGION="us-ashburn-1"

# Terraform Configuration
export TF_VAR_compartment_id="ocid1.compartment.oc1..xxx"
export TF_VAR_ssh_public_key_path="~/.ssh/id_rsa.pub"

# Application Configuration
export DATABASE_URL="*************************************/trenddb"
export REDIS_URL="redis://:password@redis:6379/0"
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="your_supabase_anon_key"
```

### CI/CD Pipeline Integration
```yaml
# .github/workflows/infrastructure.yml
name: Infrastructure Deployment

on:
  push:
    branches: [main]
    paths: ['infrastructure/**']

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.5.0
          
      - name: Terraform Plan
        run: |
          cd infrastructure/terraform
          terraform init
          terraform plan -var-file="environments/production/terraform.tfvars"
          
      - name: Terraform Apply
        if: github.ref == 'refs/heads/main'
        run: |
          cd infrastructure/terraform
          terraform apply -auto-approve -var-file="environments/production/terraform.tfvars"
```

## Success Criteria
- 100% infrastructure reproducibility across environments
- Zero-downtime deployments with automated rollback
- Complete infrastructure monitoring and alerting
- Automated disaster recovery procedures
- Cost optimization through resource right-sizing

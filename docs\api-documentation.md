# Trend Platform API Documentation

## Overview

The Trend Platform API provides a comprehensive set of endpoints for AI-powered content generation, asset management, and automated deployment. The API is built with FastAPI and follows RESTful principles with comprehensive authentication and monitoring.

## Base URL

```
Production: https://api.trendplatform.com
Development: http://localhost:8000
```

## Authentication

All API endpoints (except health checks and documentation) require authentication using JWT tokens.

### Authentication Header
```
Authorization: Bearer <jwt_token>
```

### Getting a Token
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

## API Endpoints

### 1. Authentication (`/api/auth`)

#### Login
- **POST** `/api/auth/login` - Authenticate user and get JWT token
- **POST** `/api/auth/refresh` - Refresh JWT token
- **POST** `/api/auth/logout` - Logout and invalidate token

#### User Management
- **GET** `/api/auth/me` - Get current user information
- **PUT** `/api/auth/me` - Update current user profile

### 2. Trends Management (`/api/trends`)

#### Trend Operations
- **GET** `/api/trends` - List trends with filtering and pagination
- **POST** `/api/trends` - Create new trend
- **GET** `/api/trends/{trend_id}` - Get specific trend
- **PUT** `/api/trends/{trend_id}` - Update trend
- **DELETE** `/api/trends/{trend_id}` - Delete trend

#### Trend Analysis
- **POST** `/api/trends/analyze` - Analyze trend data
- **GET** `/api/trends/{trend_id}/analytics` - Get trend analytics
- **POST** `/api/trends/batch-analyze` - Analyze multiple trends

### 3. Content Generation (`/api/content`)

#### Content Operations
- **GET** `/api/content` - List generated content
- **POST** `/api/content/generate` - Generate content for trend
- **GET** `/api/content/{content_id}` - Get specific content
- **PUT** `/api/content/{content_id}` - Update content
- **DELETE** `/api/content/{content_id}` - Delete content

#### Content Management
- **POST** `/api/content/{content_id}/regenerate` - Regenerate content
- **GET** `/api/content/{content_id}/preview` - Preview content
- **POST** `/api/content/batch-generate` - Generate content for multiple trends

### 4. Templates (`/api/templates`)

#### Template Management
- **GET** `/api/templates` - List available templates
- **GET** `/api/templates/{template_name}` - Get template details
- **POST** `/api/templates/validate` - Validate template syntax
- **POST** `/api/templates/render` - Render template with data

#### Template Operations
- **GET** `/api/templates/health` - Template engine health check
- **GET** `/api/templates/variables/{template_name}` - Get template variables

### 5. AI Services (`/api/ai`)

#### AI Provider Management
- **GET** `/api/ai/providers` - List available AI providers
- **GET** `/api/ai/providers/{provider}/health` - Check provider health
- **POST** `/api/ai/providers/{provider}/test` - Test provider connection

#### AI Operations
- **POST** `/api/ai/generate-text` - Generate text content
- **POST** `/api/ai/generate-image` - Generate image content
- **GET** `/api/ai/usage-stats` - Get AI usage statistics

### 6. Asset Management (`/api/assets`)

#### Asset Processing
- **POST** `/api/assets/process-image` - Process image from URL
- **POST** `/api/assets/upload-image` - Upload and process image file
- **GET** `/api/assets/storage-stats` - Get storage statistics
- **POST** `/api/assets/cleanup` - Cleanup old assets

#### Asset Operations
- **GET** `/api/assets/processing-stats` - Get processing statistics
- **GET** `/api/assets/health` - Asset management health check

### 7. Deployment (`/api/deployment`)

#### Deployment Operations
- **POST** `/api/deployment/deploy-trend` - Deploy content for specific trend
- **POST** `/api/deployment/deploy-batch` - Deploy multiple trends
- **GET** `/api/deployment/status` - Get git repository status
- **GET** `/api/deployment/deployments/recent` - Get recent deployments

#### Webhook Endpoints
- **POST** `/api/deployment/webhook/github` - GitHub webhook handler
- **POST** `/api/deployment/webhook/gitlab` - GitLab webhook handler
- **POST** `/api/deployment/webhook/generic` - Generic webhook handler

#### Health Checks
- **GET** `/api/deployment/health` - Deployment service health check

### 8. Pipeline Orchestration (`/api/pipeline`)

#### Pipeline Execution
- **POST** `/api/pipeline/execute` - Execute complete pipeline for trend
- **POST** `/api/pipeline/execute-batch` - Execute batch pipeline
- **GET** `/api/pipeline/status/{trend_id}` - Get pipeline status
- **GET** `/api/pipeline/active` - Get active pipelines

#### Pipeline Management
- **GET** `/api/pipeline/health` - Pipeline health check
- **GET** `/api/pipeline/modes` - Get available pipeline modes

### 9. Dashboard (`/api/dashboard`)

#### Dashboard Data
- **GET** `/api/dashboard/stats` - Get comprehensive dashboard statistics
- **GET** `/api/dashboard/trends/recent` - Get recent trends with status
- **GET** `/api/dashboard/content/recent` - Get recently generated content
- **GET** `/api/dashboard/activity/recent` - Get recent system activity

#### Performance Metrics
- **GET** `/api/dashboard/performance/metrics` - Get performance metrics

## Request/Response Examples

### Generate Content for Trend

**Request:**
```http
POST /api/content/generate
Authorization: Bearer <token>
Content-Type: application/json

{
  "trend_id": "trend-123",
  "template_type": "article",
  "include_image": true,
  "include_code": true,
  "force_regenerate": false
}
```

**Response:**
```json
{
  "success": true,
  "content": {
    "id": "content-456",
    "trend_id": "trend-123",
    "title": "Understanding AI Trends in 2024",
    "description": "A comprehensive guide to AI trends",
    "body": "# Understanding AI Trends in 2024\n\n...",
    "slug": "understanding-ai-trends-2024",
    "hero_image_url": "/assets/hero_optimized.webp",
    "word_count": 1250,
    "readability_score": 85.5
  },
  "assets": [
    {
      "filename": "hero_optimized.webp",
      "mime_type": "image/webp",
      "width": 1200,
      "height": 800,
      "file_size": 45678
    }
  ],
  "generation_time": 12.5,
  "ai_model_used": "gpt-4"
}
```

### Execute Complete Pipeline

**Request:**
```http
POST /api/pipeline/execute
Authorization: Bearer <token>
Content-Type: application/json

{
  "trend_id": "trend-123",
  "mode": "full_pipeline",
  "template_type": "article",
  "include_assets": true,
  "auto_deploy": true
}
```

**Response:**
```json
{
  "success": true,
  "trend_id": "trend-123",
  "stage": "completed",
  "total_time": 45.2,
  "stage_times": {
    "initialization": 1.2,
    "content_generation": 15.8,
    "asset_processing": 8.5,
    "template_rendering": 2.1,
    "deployment": 16.3,
    "cleanup": 1.3
  },
  "content_generated": true,
  "assets_processed": true,
  "deployed": true,
  "warnings": []
}
```

### Get Dashboard Statistics

**Request:**
```http
GET /api/dashboard/stats
Authorization: Bearer <token>
```

**Response:**
```json
{
  "trends": {
    "total": 1250,
    "recent_week": 45,
    "by_status": {
      "approved": 800,
      "pending": 200,
      "completed": 250
    },
    "by_category": {
      "Technology": 400,
      "Business": 300,
      "Health": 250,
      "Entertainment": 300
    }
  },
  "content": {
    "total": 950,
    "with_images": 720,
    "with_code": 380,
    "average_word_count": 1150,
    "average_readability": 82.5
  },
  "assets": {
    "total_files": 2840,
    "total_size_mb": 1250.5,
    "webp_enabled": true,
    "optimization_enabled": true
  },
  "pipeline": {
    "active_count": 3,
    "health_status": "healthy",
    "components_healthy": 6,
    "total_components": 6
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Error Handling

### Error Response Format
```json
{
  "error": "error_code",
  "message": "Human readable error message",
  "details": {
    "field": "Additional error details"
  },
  "correlation_id": "uuid-for-tracking"
}
```

### Common Error Codes
- `400` - Bad Request (invalid input)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate limited based on user tier:

- **Free Tier**: 100 requests/hour
- **Pro Tier**: 1,000 requests/hour
- **Enterprise Tier**: 10,000 requests/hour

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Webhooks

The platform supports webhooks for real-time notifications:

### Webhook Events
- `content.generated` - Content generation completed
- `deployment.completed` - Deployment finished
- `pipeline.completed` - Pipeline execution finished
- `trend.created` - New trend added

### Webhook Payload Example
```json
{
  "event": "content.generated",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "trend_id": "trend-123",
    "content_id": "content-456",
    "title": "Understanding AI Trends",
    "status": "completed"
  }
}
```

## Health Checks

### System Health
- **GET** `/api/health` - Comprehensive system health
- **GET** `/api/health/ready` - Readiness check
- **GET** `/api/health/live` - Liveness check

### Component Health
- **GET** `/api/ai/health` - AI services health
- **GET** `/api/assets/health` - Asset management health
- **GET** `/api/deployment/health` - Deployment service health
- **GET** `/api/pipeline/health` - Pipeline orchestrator health

## Monitoring

### Metrics Endpoint
- **GET** `/metrics` - Prometheus metrics

### Available Metrics
- HTTP request duration and count
- Content generation success/failure rates
- Asset processing statistics
- Deployment success rates
- AI API usage and costs

## SDK and Libraries

Official SDKs are available for:
- **Python**: `pip install trendplatform-sdk`
- **JavaScript/Node.js**: `npm install @trendplatform/sdk`
- **Go**: `go get github.com/trendplatform/go-sdk`

## Support

- **Documentation**: https://docs.trendplatform.com
- **API Status**: https://status.trendplatform.com
- **Support**: <EMAIL>

"""
AI Service Factory for managing different AI providers
Provides centralized creation and management of AI clients
"""

from typing import Dict, Any, Optional, Type, List
from enum import Enum
from .base_ai_client import BaseAIClient, AIServiceError
from .openai_client import OpenAICompatibleClient
from .anthropic_client import AnthropicClient
from shared.config import settings, GENERATOR_CONFIG
from monitoring.logger import get_logger

logger = get_logger('generator.ai.factory')


class AIProvider(str, Enum):
    """Supported AI providers"""
    OPENAI = "openai"
    AZURE_OPENAI = "azure_openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"
    CUSTOM = "custom"


class AIServiceFactory:
    """
    Factory class for creating and managing AI service clients
    Supports multiple providers and configurations
    """
    
    def __init__(self):
        self.logger = logger
        self._clients: Dict[str, BaseAIClient] = {}
        self._provider_classes: Dict[AIProvider, Type[BaseAIClient]] = {
            AIProvider.OPENAI: OpenAICompatibleClient,
            AIProvider.AZURE_OPENAI: OpenAICompatibleClient,
            AIProvider.ANTHROPIC: AnthropicClient,
            # Future providers can be added here
            # AIProvider.LOCAL: LocalAIClient,
        }
        
        # Default configurations for each provider
        self._default_configs = {
            AIProvider.OPENAI: {
                "base_url": "https://api.openai.com/v1",
                "text_model": "gpt-3.5-turbo",
                "image_model": "dall-e-3",
                "requests_per_minute": 60,
                "tokens_per_minute": 90000
            },
            AIProvider.AZURE_OPENAI: {
                "base_url": None,  # Must be provided
                "text_model": "gpt-35-turbo",
                "image_model": "dall-e-3",
                "requests_per_minute": 60,
                "tokens_per_minute": 90000
            },
            AIProvider.ANTHROPIC: {
                "base_url": "https://api.anthropic.com",
                "text_model": "claude-3-sonnet-20240229",
                "image_model": None,  # Not supported
                "requests_per_minute": 50,
                "tokens_per_minute": 40000
            }
        }
    
    def create_client(
        self, 
        provider: AIProvider, 
        api_key: str,
        base_url: Optional[str] = None,
        **kwargs
    ) -> BaseAIClient:
        """
        Create an AI client for the specified provider
        
        Args:
            provider: AI provider type
            api_key: API key for the provider
            base_url: Custom base URL (optional)
            **kwargs: Additional configuration parameters
            
        Returns:
            Configured AI client instance
            
        Raises:
            AIServiceError: If provider is not supported or configuration is invalid
        """
        if provider not in self._provider_classes:
            raise AIServiceError(f"Unsupported AI provider: {provider}")
        
        # Get default configuration for provider
        default_config = self._default_configs.get(provider, {}).copy()
        
        # Override with provided parameters
        if base_url:
            default_config["base_url"] = base_url
        
        default_config.update(kwargs)
        
        # Validate required parameters
        if not api_key:
            raise AIServiceError(f"API key is required for provider: {provider}")
        
        if not default_config.get("base_url"):
            raise AIServiceError(f"Base URL is required for provider: {provider}")
        
        try:
            client_class = self._provider_classes[provider]
            client = client_class(
                api_key=api_key,
                base_url=default_config["base_url"],
                **{k: v for k, v in default_config.items() if k != "base_url"}
            )
            
            self.logger.info(
                f"Created AI client for provider: {provider}",
                provider=provider,
                base_url=default_config["base_url"]
            )
            
            return client
            
        except Exception as e:
            raise AIServiceError(f"Failed to create client for provider {provider}: {str(e)}")
    
    def get_or_create_client(
        self, 
        provider: AIProvider,
        api_key: str,
        base_url: Optional[str] = None,
        cache_key: Optional[str] = None,
        **kwargs
    ) -> BaseAIClient:
        """
        Get existing client or create new one with caching
        
        Args:
            provider: AI provider type
            api_key: API key for the provider
            base_url: Custom base URL (optional)
            cache_key: Custom cache key (optional)
            **kwargs: Additional configuration parameters
            
        Returns:
            AI client instance (cached or new)
        """
        # Generate cache key
        if not cache_key:
            cache_key = f"{provider}_{hash(api_key)}_{hash(base_url or '')}"
        
        # Return cached client if exists
        if cache_key in self._clients:
            return self._clients[cache_key]
        
        # Create new client
        client = self.create_client(provider, api_key, base_url, **kwargs)
        
        # Cache the client
        self._clients[cache_key] = client
        
        return client
    
    def create_from_config(self, config: Dict[str, Any]) -> BaseAIClient:
        """
        Create AI client from configuration dictionary
        
        Args:
            config: Configuration dictionary with provider settings
            
        Returns:
            Configured AI client instance
        """
        provider_str = config.get("provider", "openai")
        
        try:
            provider = AIProvider(provider_str)
        except ValueError:
            raise AIServiceError(f"Invalid provider in config: {provider_str}")
        
        api_key = config.get("api_key")
        if not api_key:
            raise AIServiceError("API key not found in configuration")
        
        base_url = config.get("base_url")
        
        # Extract additional parameters
        additional_params = {
            k: v for k, v in config.items() 
            if k not in ["provider", "api_key", "base_url"]
        }
        
        return self.create_client(provider, api_key, base_url, **additional_params)
    
    def create_default_client(self) -> BaseAIClient:
        """
        Create default AI client using application settings
        
        Returns:
            Default AI client instance
        """
        # Use settings from shared configuration
        provider = AIProvider.OPENAI  # Default provider
        api_key = settings.openai_api_key
        base_url = settings.openai_base_url
        
        if not api_key:
            raise AIServiceError("OpenAI API key not configured in settings")
        
        # Get additional configuration from GENERATOR_CONFIG
        ai_config = GENERATOR_CONFIG.get("ai_services", {})
        text_config = ai_config.get("text_generation", {})
        image_config = ai_config.get("image_generation", {})
        
        additional_params = {
            "text_model": text_config.get("model", "gpt-3.5-turbo"),
            "image_model": image_config.get("model", "dall-e-3"),
            "requests_per_minute": 60,
            "tokens_per_minute": 90000
        }
        
        return self.create_client(provider, api_key, base_url, **additional_params)
    
    def get_supported_providers(self) -> List[AIProvider]:
        """
        Get list of supported AI providers
        
        Returns:
            List of supported provider types
        """
        return list(self._provider_classes.keys())
    
    def register_provider(self, provider: AIProvider, client_class: Type[BaseAIClient]):
        """
        Register a new AI provider
        
        Args:
            provider: Provider type
            client_class: Client class implementing BaseAIClient
        """
        if not issubclass(client_class, BaseAIClient):
            raise AIServiceError("Client class must inherit from BaseAIClient")
        
        self._provider_classes[provider] = client_class
        
        self.logger.info(f"Registered new AI provider: {provider}")
    
    def clear_cache(self):
        """Clear all cached clients"""
        self._clients.clear()
        self.logger.info("Cleared AI client cache")
    
    async def health_check_all(self) -> Dict[str, Any]:
        """
        Perform health check on all cached clients
        
        Returns:
            Health status for all clients
        """
        results = {}
        
        for cache_key, client in self._clients.items():
            try:
                health_status = await client.health_check()
                results[cache_key] = health_status
            except Exception as e:
                results[cache_key] = {
                    "status": "error",
                    "error": str(e),
                    "client_type": type(client).__name__
                }
        
        return results
    
    def get_client_stats(self) -> Dict[str, Any]:
        """
        Get statistics for all cached clients
        
        Returns:
            Usage statistics for all clients
        """
        stats = {
            "total_clients": len(self._clients),
            "clients": {}
        }
        
        for cache_key, client in self._clients.items():
            usage_stats = client.get_usage_stats()
            stats["clients"][cache_key] = {
                "client_type": type(client).__name__,
                "usage_stats": usage_stats,
                "base_url": client.base_url
            }
        
        return stats


# Global factory instance
ai_service_factory = AIServiceFactory()


def get_ai_client(
    provider: Optional[AIProvider] = None,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None,
    **kwargs
) -> BaseAIClient:
    """
    Convenience function to get an AI client
    
    Args:
        provider: AI provider (defaults to OpenAI)
        api_key: API key (defaults to settings)
        base_url: Base URL (defaults to settings)
        **kwargs: Additional parameters
        
    Returns:
        AI client instance
    """
    if provider is None:
        # Return default client
        return ai_service_factory.create_default_client()
    
    if api_key is None:
        api_key = settings.openai_api_key
    
    if base_url is None:
        base_url = settings.openai_base_url
    
    return ai_service_factory.get_or_create_client(
        provider=provider,
        api_key=api_key,
        base_url=base_url,
        **kwargs
    )


def create_ai_client_from_config(config: Dict[str, Any]) -> BaseAIClient:
    """
    Convenience function to create AI client from configuration
    
    Args:
        config: Configuration dictionary
        
    Returns:
        AI client instance
    """
    return ai_service_factory.create_from_config(config)

"""
Anthropic Claude AI client implementation
Example of how to add additional AI providers to the system
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
import aiohttp
from .base_ai_client import (
    BaseAIClient, AIClientMixin, AIServiceError, AIServiceType, 
    AIRequest, AIResponse
)
from shared.utils import AsyncHTTP<PERSON>lient
from monitoring.logger import get_logger

logger = get_logger('generator.ai.anthropic')


class AnthropicClient(BaseAIClient, AIClientMixin):
    """
    Anthropic Claude AI client
    Example implementation for additional AI providers
    """
    
    def __init__(self, api_key: str, base_url: str = "https://api.anthropic.com", **kwargs):
        """
        Initialize Anthropic client
        
        Args:
            api_key: Anthropic API key
            base_url: Base URL for Anthropic API
            **kwargs: Additional configuration
        """
        super().__init__(api_key, base_url, **kwargs)
        self.logger = logger
        
        # Default models
        self.default_models = {
            AIServiceType.TEXT_GENERATION: kwargs.get('text_model', 'claude-3-sonnet-20240229'),
            AIServiceType.CODE_GENERATION: kwargs.get('code_model', 'claude-3-sonnet-20240229'),
            # Anthropic doesn't have image generation or moderation APIs
            AIServiceType.IMAGE_GENERATION: None,
            AIServiceType.CONTENT_MODERATION: None
        }
        
        # Rate limiting (Anthropic specific)
        self.rate_limits = {
            "requests_per_minute": kwargs.get('requests_per_minute', 50),
            "tokens_per_minute": kwargs.get('tokens_per_minute', 40000),
            "requests_per_day": kwargs.get('requests_per_day', 1000)
        }
        
        # Cost estimation (approximate USD per 1K tokens)
        self.cost_per_1k_tokens = {
            'claude-3-opus-20240229': {'input': 0.015, 'output': 0.075},
            'claude-3-sonnet-20240229': {'input': 0.003, 'output': 0.015},
            'claude-3-haiku-20240307': {'input': 0.00025, 'output': 0.00125}
        }
        
        # Anthropic API version
        self.api_version = kwargs.get('api_version', '2023-06-01')
    
    async def generate_text(self, request: AIRequest) -> AIResponse:
        """
        Generate text using Anthropic Claude API
        
        Args:
            request: AI request with prompt and parameters
            
        Returns:
            AI response with generated text
        """
        start_time = time.time()
        
        try:
            self._validate_request(request)
            
            model = request.model or self.default_models[AIServiceType.TEXT_GENERATION]
            
            if not model:
                raise AIServiceError("Text generation not supported by Anthropic client")
            
            # Prepare request payload (Anthropic format)
            payload = {
                "model": model,
                "max_tokens": request.max_tokens or 2000,
                "messages": [
                    {
                        "role": "user",
                        "content": request.prompt
                    }
                ]
            }
            
            # Add temperature if specified
            if request.temperature is not None:
                payload["temperature"] = request.temperature
            
            # Add additional parameters
            if request.additional_params:
                # Filter out parameters that Anthropic doesn't support
                supported_params = ['top_p', 'top_k', 'stop_sequences']
                for param in supported_params:
                    if param in request.additional_params:
                        payload[param] = request.additional_params[param]
            
            headers = {
                "x-api-key": self.api_key,
                "anthropic-version": self.api_version,
                "Content-Type": "application/json"
            }
            
            # Make API request
            async with AsyncHTTPClient(timeout=120) as client:
                response = await client.post(
                    f"{self.base_url}/v1/messages",
                    json=payload,
                    headers=headers
                )
                
                if response['status'] != 200:
                    error_msg = f"Anthropic API returned status {response['status']}"
                    if 'data' in response and isinstance(response['data'], dict):
                        error_detail = response['data'].get('error', {}).get('message', '')
                        if error_detail:
                            error_msg += f": {error_detail}"
                    raise AIServiceError(error_msg)
                
                data = response['data']
                
                if 'content' not in data or not data['content']:
                    raise AIServiceError("No content generated by Anthropic")
                
                # Extract text content (Anthropic returns array of content blocks)
                content_blocks = data['content']
                content = ""
                for block in content_blocks:
                    if block.get('type') == 'text':
                        content += block.get('text', '')
                
                if not content:
                    raise AIServiceError("No text content found in Anthropic response")
                
                # Extract usage information
                usage_info = data.get('usage', {})
                tokens_used = usage_info.get('input_tokens', 0) + usage_info.get('output_tokens', 0)
                
                # Calculate cost
                cost = self._calculate_text_cost(model, usage_info)
                
                response_time = time.time() - start_time
                
                # Create response
                ai_response = AIResponse(
                    content=content.strip(),
                    service_type=AIServiceType.TEXT_GENERATION,
                    model_used=model,
                    tokens_used=tokens_used,
                    response_time=response_time,
                    cost=cost,
                    metadata={
                        'usage': usage_info,
                        'stop_reason': data.get('stop_reason'),
                        'stop_sequence': data.get('stop_sequence'),
                        'message_id': data.get('id')
                    }
                )
                
                # Update usage statistics
                self.update_usage_stats(ai_response, success=True)
                
                self.logger.info(
                    f"Anthropic text generation successful",
                    model=model,
                    tokens_used=tokens_used,
                    response_time=response_time,
                    cost=cost
                )
                
                return ai_response
                
        except AIServiceError:
            raise
        except Exception as e:
            self.update_usage_stats(AIResponse(
                content="", 
                service_type=AIServiceType.TEXT_GENERATION,
                model_used=request.model or "unknown"
            ), success=False)
            self._handle_api_error(e, "Anthropic text generation")
    
    async def generate_image(self, request: AIRequest) -> AIResponse:
        """
        Anthropic doesn't support image generation
        """
        raise AIServiceError("Image generation not supported by Anthropic")
    
    async def moderate_content(self, content: str) -> Dict[str, Any]:
        """
        Anthropic doesn't have a dedicated moderation API
        We can use Claude to analyze content safety
        """
        try:
            moderation_prompt = f"""
            Please analyze the following content for safety and appropriateness. 
            Respond with a JSON object containing:
            - "flagged": boolean indicating if content should be flagged
            - "categories": object with boolean flags for different violation types
            - "explanation": brief explanation of any issues found
            
            Content to analyze:
            {content}
            
            Respond only with the JSON object, no other text.
            """
            
            request = AIRequest(
                prompt=moderation_prompt,
                service_type=AIServiceType.TEXT_GENERATION,
                max_tokens=500,
                temperature=0.1
            )
            
            response = await self.generate_text(request)
            
            # Try to parse the JSON response
            import json
            try:
                moderation_result = json.loads(response.content)
                return moderation_result
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return {
                    "flagged": False,
                    "categories": {},
                    "explanation": "Unable to parse moderation response",
                    "raw_response": response.content
                }
                
        except Exception as e:
            self._handle_api_error(e, "Anthropic content moderation")
    
    def get_supported_models(self) -> Dict[AIServiceType, List[str]]:
        """Get supported models for each service type"""
        return {
            AIServiceType.TEXT_GENERATION: [
                'claude-3-opus-20240229',
                'claude-3-sonnet-20240229', 
                'claude-3-haiku-20240307',
                'claude-2.1',
                'claude-2.0',
                'claude-instant-1.2'
            ],
            AIServiceType.CODE_GENERATION: [
                'claude-3-opus-20240229',
                'claude-3-sonnet-20240229',
                'claude-3-haiku-20240307'
            ],
            AIServiceType.IMAGE_GENERATION: [],  # Not supported
            AIServiceType.CONTENT_MODERATION: [
                'claude-3-sonnet-20240229'  # Using Claude for content analysis
            ]
        }
    
    async def validate_api_key(self) -> bool:
        """Validate API key by making a simple request"""
        try:
            # Make a minimal request to test the API key
            test_request = AIRequest(
                prompt="Hello",
                service_type=AIServiceType.TEXT_GENERATION,
                max_tokens=5
            )
            
            await self.generate_text(test_request)
            return True
            
        except Exception:
            return False
    
    async def estimate_cost(self, request: AIRequest) -> float:
        """Estimate cost for a request"""
        if request.service_type != AIServiceType.TEXT_GENERATION:
            return 0.0
        
        model = request.model or self.default_models.get(request.service_type, '')
        
        # Estimate tokens (rough approximation)
        estimated_input_tokens = len(request.prompt.split()) * 1.33
        estimated_output_tokens = request.max_tokens or 1000
        
        return self._calculate_text_cost(model, {
            'input_tokens': int(estimated_input_tokens),
            'output_tokens': int(estimated_output_tokens)
        })
    
    def get_rate_limits(self) -> Dict[str, Any]:
        """Get rate limit information"""
        return self.rate_limits.copy()
    
    def _calculate_text_cost(self, model: str, usage_info: Dict[str, Any]) -> float:
        """Calculate cost for text generation"""
        if model not in self.cost_per_1k_tokens:
            return 0.0
        
        costs = self.cost_per_1k_tokens[model]
        
        input_tokens = usage_info.get('input_tokens', 0)
        output_tokens = usage_info.get('output_tokens', 0)
        
        input_cost = (input_tokens / 1000) * costs['input']
        output_cost = (output_tokens / 1000) * costs['output']
        
        return input_cost + output_cost

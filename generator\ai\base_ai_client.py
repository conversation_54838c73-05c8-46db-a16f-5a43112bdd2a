"""
Abstract base class for AI service clients
Defines the interface for all AI service implementations
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from shared.exceptions import TrendPlatformException


class AIServiceError(TrendPlatformException):
    """Base exception for AI service errors"""
    pass


class AIServiceType(str, Enum):
    """AI service type enumeration"""
    TEXT_GENERATION = "text_generation"
    IMAGE_GENERATION = "image_generation"
    CODE_GENERATION = "code_generation"
    CONTENT_MODERATION = "content_moderation"


@dataclass
class AIUsageStats:
    """AI service usage statistics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_tokens_used: int = 0
    total_cost: float = 0.0
    average_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    @property
    def error_rate(self) -> float:
        """Calculate error rate percentage"""
        return 100.0 - self.success_rate


@dataclass
class AIRequest:
    """Base AI request structure"""
    prompt: str
    service_type: AIServiceType
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    model: Optional[str] = None
    additional_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_params is None:
            self.additional_params = {}


@dataclass
class AIResponse:
    """Base AI response structure"""
    content: str
    service_type: AIServiceType
    model_used: str
    tokens_used: int = 0
    response_time: float = 0.0
    cost: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseAIClient(ABC):
    """
    Abstract base class for AI service clients
    Defines the interface that all AI service implementations must follow
    """
    
    def __init__(self, api_key: str, base_url: str, **kwargs):
        """
        Initialize AI client
        
        Args:
            api_key: API key for the service
            base_url: Base URL for the API
            **kwargs: Additional configuration parameters
        """
        self.api_key = api_key
        self.base_url = base_url
        self.config = kwargs
        self.usage_stats = AIUsageStats()
        self._session = None
    
    @abstractmethod
    async def generate_text(self, request: AIRequest) -> AIResponse:
        """
        Generate text content using AI
        
        Args:
            request: AI request with prompt and parameters
            
        Returns:
            AI response with generated content
            
        Raises:
            AIServiceError: If generation fails
        """
        pass
    
    @abstractmethod
    async def generate_image(self, request: AIRequest) -> AIResponse:
        """
        Generate image using AI
        
        Args:
            request: AI request with prompt and parameters
            
        Returns:
            AI response with image URL or data
            
        Raises:
            AIServiceError: If generation fails
        """
        pass
    
    @abstractmethod
    async def moderate_content(self, content: str) -> Dict[str, Any]:
        """
        Moderate content for safety and compliance
        
        Args:
            content: Content to moderate
            
        Returns:
            Moderation results with flags and scores
            
        Raises:
            AIServiceError: If moderation fails
        """
        pass
    
    @abstractmethod
    def get_supported_models(self) -> Dict[AIServiceType, List[str]]:
        """
        Get list of supported models for each service type
        
        Returns:
            Dictionary mapping service types to available models
        """
        pass
    
    @abstractmethod
    async def validate_api_key(self) -> bool:
        """
        Validate API key and connection
        
        Returns:
            True if API key is valid and service is accessible
        """
        pass
    
    def get_usage_stats(self) -> AIUsageStats:
        """
        Get usage statistics for this client
        
        Returns:
            Current usage statistics
        """
        return self.usage_stats
    
    def update_usage_stats(self, response: AIResponse, success: bool = True):
        """
        Update usage statistics after API call
        
        Args:
            response: AI response with usage data
            success: Whether the request was successful
        """
        self.usage_stats.total_requests += 1
        self.usage_stats.last_request_time = datetime.utcnow()
        
        if success:
            self.usage_stats.successful_requests += 1
            self.usage_stats.total_tokens_used += response.tokens_used
            self.usage_stats.total_cost += response.cost
            
            # Update average response time
            total_time = (self.usage_stats.average_response_time * 
                         (self.usage_stats.successful_requests - 1) + response.response_time)
            self.usage_stats.average_response_time = total_time / self.usage_stats.successful_requests
        else:
            self.usage_stats.failed_requests += 1
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the AI service
        
        Returns:
            Health status information
        """
        try:
            is_valid = await self.validate_api_key()
            return {
                "status": "healthy" if is_valid else "unhealthy",
                "api_key_valid": is_valid,
                "base_url": self.base_url,
                "usage_stats": self.usage_stats,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def estimate_cost(self, request: AIRequest) -> float:
        """
        Estimate cost for a request (to be implemented by subclasses)
        
        Args:
            request: AI request to estimate cost for
            
        Returns:
            Estimated cost in USD
        """
        return 0.0
    
    def get_rate_limits(self) -> Dict[str, Any]:
        """
        Get rate limit information (to be implemented by subclasses)
        
        Returns:
            Rate limit information
        """
        return {
            "requests_per_minute": None,
            "tokens_per_minute": None,
            "requests_per_day": None
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self._session:
            await self._session.close()
    
    def __repr__(self) -> str:
        """String representation of the client"""
        return f"{self.__class__.__name__}(base_url='{self.base_url}')"


class AIClientMixin:
    """
    Mixin class providing common functionality for AI clients
    """
    
    def _validate_request(self, request: AIRequest) -> None:
        """
        Validate AI request parameters
        
        Args:
            request: Request to validate
            
        Raises:
            AIServiceError: If request is invalid
        """
        if not request.prompt or not request.prompt.strip():
            raise AIServiceError("Prompt cannot be empty")
        
        if request.max_tokens is not None and request.max_tokens <= 0:
            raise AIServiceError("max_tokens must be positive")
        
        if request.temperature is not None and not (0.0 <= request.temperature <= 2.0):
            raise AIServiceError("temperature must be between 0.0 and 2.0")
    
    def _handle_api_error(self, error: Exception, context: str = "") -> None:
        """
        Handle and transform API errors
        
        Args:
            error: Original error
            context: Additional context for the error
            
        Raises:
            AIServiceError: Transformed error with context
        """
        error_msg = f"AI service error"
        if context:
            error_msg += f" ({context})"
        error_msg += f": {str(error)}"
        
        raise AIServiceError(error_msg) from error
    
    def _extract_usage_info(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract usage information from API response
        
        Args:
            response_data: Raw API response data
            
        Returns:
            Extracted usage information
        """
        usage = {}
        
        # Common usage fields across different APIs
        if "usage" in response_data:
            usage_data = response_data["usage"]
            usage["tokens_used"] = usage_data.get("total_tokens", 0)
            usage["prompt_tokens"] = usage_data.get("prompt_tokens", 0)
            usage["completion_tokens"] = usage_data.get("completion_tokens", 0)
        
        return usage

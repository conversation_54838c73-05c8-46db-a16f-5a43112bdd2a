"""
OpenAI-compatible AI client implementation
Supports OpenAI API and compatible services (Azure OpenAI, local models, etc.)
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
import aiohttp
from .base_ai_client import (
    BaseAIClient, AIClientMixin, AIServiceError, AIServiceType, 
    AIRequest, AIResponse
)
from shared.utils import AsyncHTTPClient
from monitoring.logger import get_logger

logger = get_logger('generator.ai.openai')


class OpenAICompatibleClient(BaseAIClient, AIClientMixin):
    """
    OpenAI-compatible AI client
    Supports OpenAI API and compatible services
    """
    
    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1", **kwargs):
        """
        Initialize OpenAI-compatible client
        
        Args:
            api_key: OpenAI API key
            base_url: Base URL for the API (default: OpenAI)
            **kwargs: Additional configuration
        """
        super().__init__(api_key, base_url, **kwargs)
        self.logger = logger
        
        # Default models
        self.default_models = {
            AIServiceType.TEXT_GENERATION: kwargs.get('text_model', 'gpt-3.5-turbo'),
            AIServiceType.IMAGE_GENERATION: kwargs.get('image_model', 'dall-e-3'),
            AIServiceType.CODE_GENERATION: kwargs.get('code_model', 'gpt-3.5-turbo'),
            AIServiceType.CONTENT_MODERATION: kwargs.get('moderation_model', 'text-moderation-latest')
        }
        
        # Rate limiting
        self.rate_limits = {
            "requests_per_minute": kwargs.get('requests_per_minute', 60),
            "tokens_per_minute": kwargs.get('tokens_per_minute', 90000),
            "requests_per_day": kwargs.get('requests_per_day', 1000)
        }
        
        # Cost estimation (approximate USD per 1K tokens)
        self.cost_per_1k_tokens = {
            'gpt-3.5-turbo': {'input': 0.0015, 'output': 0.002},
            'gpt-4': {'input': 0.03, 'output': 0.06},
            'gpt-4-turbo': {'input': 0.01, 'output': 0.03},
            'dall-e-3': {'per_image': 0.04}  # Standard quality 1024x1024
        }
    
    async def generate_text(self, request: AIRequest) -> AIResponse:
        """
        Generate text using OpenAI-compatible API
        
        Args:
            request: AI request with prompt and parameters
            
        Returns:
            AI response with generated text
        """
        start_time = time.time()
        
        try:
            self._validate_request(request)
            
            model = request.model or self.default_models[AIServiceType.TEXT_GENERATION]
            
            # Prepare request payload
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "user",
                        "content": request.prompt
                    }
                ],
                "max_tokens": request.max_tokens or 2000,
                "temperature": request.temperature or 0.7
            }
            
            # Add additional parameters
            if request.additional_params:
                payload.update(request.additional_params)
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Make API request
            async with AsyncHTTPClient(timeout=120) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers
                )
                
                if response['status'] != 200:
                    error_msg = f"API returned status {response['status']}"
                    if 'data' in response and isinstance(response['data'], dict):
                        error_detail = response['data'].get('error', {}).get('message', '')
                        if error_detail:
                            error_msg += f": {error_detail}"
                    raise AIServiceError(error_msg)
                
                data = response['data']
                
                if 'choices' not in data or not data['choices']:
                    raise AIServiceError("No content generated by AI")
                
                content = data['choices'][0]['message']['content']
                
                # Extract usage information
                usage_info = self._extract_usage_info(data)
                tokens_used = usage_info.get('tokens_used', 0)
                
                # Calculate cost
                cost = self._calculate_text_cost(model, tokens_used)
                
                response_time = time.time() - start_time
                
                # Create response
                ai_response = AIResponse(
                    content=content.strip(),
                    service_type=AIServiceType.TEXT_GENERATION,
                    model_used=model,
                    tokens_used=tokens_used,
                    response_time=response_time,
                    cost=cost,
                    metadata={
                        'usage': usage_info,
                        'finish_reason': data['choices'][0].get('finish_reason'),
                        'request_id': data.get('id')
                    }
                )
                
                # Update usage statistics
                self.update_usage_stats(ai_response, success=True)
                
                self.logger.info(
                    f"Text generation successful",
                    model=model,
                    tokens_used=tokens_used,
                    response_time=response_time,
                    cost=cost
                )
                
                return ai_response
                
        except AIServiceError:
            raise
        except Exception as e:
            self.update_usage_stats(AIResponse(
                content="", 
                service_type=AIServiceType.TEXT_GENERATION,
                model_used=request.model or "unknown"
            ), success=False)
            self._handle_api_error(e, "text generation")
    
    async def generate_image(self, request: AIRequest) -> AIResponse:
        """
        Generate image using OpenAI-compatible API
        
        Args:
            request: AI request with prompt and parameters
            
        Returns:
            AI response with image URL
        """
        start_time = time.time()
        
        try:
            self._validate_request(request)
            
            model = request.model or self.default_models[AIServiceType.IMAGE_GENERATION]
            
            # Prepare request payload
            payload = {
                "model": model,
                "prompt": request.prompt,
                "size": request.additional_params.get('size', '1024x1024'),
                "quality": request.additional_params.get('quality', 'standard'),
                "n": request.additional_params.get('n', 1)
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Make API request
            async with AsyncHTTPClient(timeout=120) as client:
                response = await client.post(
                    f"{self.base_url}/images/generations",
                    json=payload,
                    headers=headers
                )
                
                if response['status'] != 200:
                    error_msg = f"Image API returned status {response['status']}"
                    if 'data' in response and isinstance(response['data'], dict):
                        error_detail = response['data'].get('error', {}).get('message', '')
                        if error_detail:
                            error_msg += f": {error_detail}"
                    raise AIServiceError(error_msg)
                
                data = response['data']
                
                if 'data' not in data or not data['data']:
                    raise AIServiceError("No image generated by AI")
                
                image_url = data['data'][0]['url']
                
                # Calculate cost (images are typically charged per image)
                cost = self._calculate_image_cost(model, payload.get('size', '1024x1024'))
                
                response_time = time.time() - start_time
                
                # Create response
                ai_response = AIResponse(
                    content=image_url,
                    service_type=AIServiceType.IMAGE_GENERATION,
                    model_used=model,
                    tokens_used=0,  # Images don't use tokens
                    response_time=response_time,
                    cost=cost,
                    metadata={
                        'size': payload.get('size'),
                        'quality': payload.get('quality'),
                        'revised_prompt': data['data'][0].get('revised_prompt')
                    }
                )
                
                # Update usage statistics
                self.update_usage_stats(ai_response, success=True)
                
                self.logger.info(
                    f"Image generation successful",
                    model=model,
                    size=payload.get('size'),
                    response_time=response_time,
                    cost=cost
                )
                
                return ai_response
                
        except AIServiceError:
            raise
        except Exception as e:
            self.update_usage_stats(AIResponse(
                content="", 
                service_type=AIServiceType.IMAGE_GENERATION,
                model_used=request.model or "unknown"
            ), success=False)
            self._handle_api_error(e, "image generation")
    
    async def moderate_content(self, content: str) -> Dict[str, Any]:
        """
        Moderate content using OpenAI moderation API
        
        Args:
            content: Content to moderate
            
        Returns:
            Moderation results
        """
        try:
            payload = {
                "input": content,
                "model": self.default_models[AIServiceType.CONTENT_MODERATION]
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            async with AsyncHTTPClient(timeout=30) as client:
                response = await client.post(
                    f"{self.base_url}/moderations",
                    json=payload,
                    headers=headers
                )
                
                if response['status'] != 200:
                    raise AIServiceError(f"Moderation API returned status {response['status']}")
                
                data = response['data']
                
                if 'results' not in data or not data['results']:
                    raise AIServiceError("No moderation results returned")
                
                result = data['results'][0]
                
                return {
                    'flagged': result.get('flagged', False),
                    'categories': result.get('categories', {}),
                    'category_scores': result.get('category_scores', {}),
                    'model_used': payload['model']
                }
                
        except Exception as e:
            self._handle_api_error(e, "content moderation")
    
    def get_supported_models(self) -> Dict[AIServiceType, List[str]]:
        """Get supported models for each service type"""
        return {
            AIServiceType.TEXT_GENERATION: [
                'gpt-3.5-turbo', 'gpt-3.5-turbo-16k', 'gpt-4', 'gpt-4-turbo', 'gpt-4-32k'
            ],
            AIServiceType.IMAGE_GENERATION: [
                'dall-e-2', 'dall-e-3'
            ],
            AIServiceType.CODE_GENERATION: [
                'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'
            ],
            AIServiceType.CONTENT_MODERATION: [
                'text-moderation-latest', 'text-moderation-stable'
            ]
        }
    
    async def validate_api_key(self) -> bool:
        """Validate API key by making a simple request"""
        try:
            # Make a minimal request to test the API key
            test_request = AIRequest(
                prompt="Test",
                service_type=AIServiceType.TEXT_GENERATION,
                max_tokens=1
            )
            
            await self.generate_text(test_request)
            return True
            
        except Exception:
            return False
    
    async def estimate_cost(self, request: AIRequest) -> float:
        """Estimate cost for a request"""
        model = request.model or self.default_models.get(request.service_type, '')
        
        if request.service_type == AIServiceType.TEXT_GENERATION:
            # Estimate tokens (rough approximation: 1 token ≈ 0.75 words)
            estimated_tokens = len(request.prompt.split()) * 1.33
            if request.max_tokens:
                estimated_tokens += request.max_tokens
            
            return self._calculate_text_cost(model, int(estimated_tokens))
            
        elif request.service_type == AIServiceType.IMAGE_GENERATION:
            size = request.additional_params.get('size', '1024x1024') if request.additional_params else '1024x1024'
            return self._calculate_image_cost(model, size)
        
        return 0.0
    
    def get_rate_limits(self) -> Dict[str, Any]:
        """Get rate limit information"""
        return self.rate_limits.copy()
    
    def _calculate_text_cost(self, model: str, tokens_used: int) -> float:
        """Calculate cost for text generation"""
        if model not in self.cost_per_1k_tokens:
            return 0.0
        
        costs = self.cost_per_1k_tokens[model]
        # Simplified: assume equal input/output tokens
        input_tokens = tokens_used // 2
        output_tokens = tokens_used - input_tokens
        
        input_cost = (input_tokens / 1000) * costs['input']
        output_cost = (output_tokens / 1000) * costs['output']
        
        return input_cost + output_cost
    
    def _calculate_image_cost(self, model: str, size: str) -> float:
        """Calculate cost for image generation"""
        if model not in self.cost_per_1k_tokens:
            return 0.0
        
        base_cost = self.cost_per_1k_tokens[model]['per_image']
        
        # Adjust cost based on size (simplified)
        if '1024x1024' in size:
            return base_cost
        elif '512x512' in size:
            return base_cost * 0.5
        elif '256x256' in size:
            return base_cost * 0.25
        
        return base_cost

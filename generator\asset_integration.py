"""
Asset integration service for content generation pipeline
Integrates asset management with content generation and deployment
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from pathlib import Path

from .assets import AssetManager, AssetProcessingResult, ImageProcessingOptions, asset_manager
from .models import GeneratedContent, ContentAsset
from .git_ops import GitContentManager
from shared.config import GENERATOR_CONFIG
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics

logger = get_logger('generator.asset_integration')


class AssetIntegrationError(TrendPlatformException):
    """Asset integration related errors"""
    pass


class ContentAssetProcessor:
    """
    Processes assets for content generation pipeline
    Handles image optimization, format conversion, and deployment integration
    """
    
    def __init__(self, asset_manager: Optional[AssetManager] = None):
        """
        Initialize content asset processor
        
        Args:
            asset_manager: Asset manager instance (uses global if None)
        """
        self.asset_manager = asset_manager or asset_manager
        self.logger = logger
        self.metrics = app_metrics
        
        # Asset processing configuration
        self.config = GENERATOR_CONFIG.get('assets', {})
        self.processing_options = ImageProcessingOptions.from_config()
        
        # URL patterns for different image sources
        self.ai_image_patterns = [
            'oaidalleapiprodscus.blob.core.windows.net',  # DALL-E
            'cdn.openai.com',  # OpenAI CDN
            'images.unsplash.com',  # Unsplash
            'source.unsplash.com'  # Unsplash source
        ]
    
    async def process_content_assets(self, content: GeneratedContent) -> Tuple[GeneratedContent, List[ContentAsset]]:
        """
        Process all assets for generated content
        
        Args:
            content: Generated content with potential asset URLs
            
        Returns:
            Tuple of (updated_content, processed_assets)
        """
        start_time = time.time()
        processed_assets = []
        
        try:
            self.logger.info(
                f"Processing assets for content: {content.title}",
                trend_id=content.trend_id,
                has_hero_image=bool(content.hero_image_url)
            )
            
            # Process hero image if present
            if content.hero_image_url:
                hero_assets = await self._process_hero_image(content)
                processed_assets.extend(hero_assets)
                
                # Update content with optimized image URLs
                content = self._update_content_image_urls(content, hero_assets)
            
            # Process any embedded images in content body
            embedded_assets = await self._process_embedded_images(content)
            processed_assets.extend(embedded_assets)
            
            # Update content body with optimized image references
            if embedded_assets:
                content = self._update_content_body_images(content, embedded_assets)
            
            processing_time = time.time() - start_time
            
            # Record metrics
            self.metrics.record_asset_processing(
                len(processed_assets), 
                processing_time,
                'success'
            )
            
            self.logger.info(
                f"Asset processing completed",
                trend_id=content.trend_id,
                assets_processed=len(processed_assets),
                processing_time=f"{processing_time:.2f}s"
            )
            
            return content, processed_assets
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            self.metrics.record_asset_processing(
                0, 
                processing_time,
                'error'
            )
            
            error_msg = f"Asset processing failed for content {content.trend_id}: {str(e)}"
            self.logger.error(error_msg)
            raise AssetIntegrationError(error_msg)
    
    async def _process_hero_image(self, content: GeneratedContent) -> List[ContentAsset]:
        """Process hero image for content"""
        try:
            if not content.hero_image_url:
                return []
            
            self.logger.info(f"Processing hero image: {content.hero_image_url}")
            
            # Generate filename based on content slug
            filename = f"hero_{content.slug}"
            
            # Download and process image
            result = await self.asset_manager.download_and_process(
                url=str(content.hero_image_url),
                filename=filename,
                options=self.processing_options
            )
            
            if not result.success:
                self.logger.warning(f"Hero image processing failed: {result.error_message}")
                return []
            
            assets = result.assets_created
            
            # Set alt text for assets
            for asset in assets:
                asset.alt_text = content.hero_image_alt or f"Hero image for {content.title}"
            
            self.logger.info(
                f"Hero image processed successfully",
                original_size=result.original_asset.file_size if result.original_asset else 0,
                assets_created=len(assets),
                size_reduction=f"{result.size_reduction:.1f}%"
            )
            
            return assets
            
        except Exception as e:
            self.logger.error(f"Hero image processing failed: {str(e)}")
            return []
    
    async def _process_embedded_images(self, content: GeneratedContent) -> List[ContentAsset]:
        """Process images embedded in content body"""
        try:
            # Extract image URLs from content body
            image_urls = self._extract_image_urls_from_content(content.body)
            
            if not image_urls:
                return []
            
            self.logger.info(f"Processing {len(image_urls)} embedded images")
            
            processed_assets = []
            
            for i, url in enumerate(image_urls):
                try:
                    filename = f"content_{content.slug}_{i}"
                    
                    result = await self.asset_manager.download_and_process(
                        url=url,
                        filename=filename,
                        options=self.processing_options
                    )
                    
                    if result.success:
                        assets = result.assets_created
                        
                        # Set alt text
                        for asset in assets:
                            asset.alt_text = f"Image {i+1} for {content.title}"
                        
                        processed_assets.extend(assets)
                    else:
                        self.logger.warning(f"Failed to process embedded image {url}: {result.error_message}")
                        
                except Exception as e:
                    self.logger.warning(f"Error processing embedded image {url}: {str(e)}")
                    continue
            
            self.logger.info(f"Processed {len(processed_assets)} embedded image assets")
            return processed_assets
            
        except Exception as e:
            self.logger.error(f"Embedded image processing failed: {str(e)}")
            return []
    
    def _extract_image_urls_from_content(self, content_body: str) -> List[str]:
        """Extract image URLs from content body"""
        import re
        
        # Patterns for different image URL formats
        patterns = [
            r'!\[.*?\]\((https?://[^\s\)]+)\)',  # Markdown images
            r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>',  # HTML img tags
            r'https?://[^\s]+\.(?:jpg|jpeg|png|gif|webp)',  # Direct image URLs
        ]
        
        urls = []
        for pattern in patterns:
            matches = re.findall(pattern, content_body, re.IGNORECASE)
            urls.extend(matches)
        
        # Filter for AI-generated images and valid URLs
        filtered_urls = []
        for url in urls:
            if self._is_ai_generated_image(url) or self._is_valid_image_url(url):
                filtered_urls.append(url)
        
        return list(set(filtered_urls))  # Remove duplicates
    
    def _is_ai_generated_image(self, url: str) -> bool:
        """Check if URL is from an AI image generation service"""
        return any(pattern in url for pattern in self.ai_image_patterns)
    
    def _is_valid_image_url(self, url: str) -> bool:
        """Check if URL appears to be a valid image URL"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            
            # Check if it's a valid URL
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Check if it has an image extension
            path = parsed.path.lower()
            image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            
            return any(path.endswith(ext) for ext in image_extensions)
            
        except Exception:
            return False
    
    def _update_content_image_urls(self, content: GeneratedContent, assets: List[ContentAsset]) -> GeneratedContent:
        """Update content with optimized image URLs"""
        if not assets:
            return content
        
        # Find the best asset (prefer WebP, then optimized)
        best_asset = None
        webp_asset = None
        
        for asset in assets:
            if asset.mime_type == 'image/webp':
                webp_asset = asset
            elif not best_asset:
                best_asset = asset
        
        # Use WebP if available, otherwise use best asset
        selected_asset = webp_asset or best_asset
        
        if selected_asset:
            # Update hero image URL to point to optimized version
            # In a real deployment, this would be the deployed URL
            content.hero_image_url = self._get_deployed_asset_url(selected_asset)
            
            # Update alt text if not set
            if not content.hero_image_alt and selected_asset.alt_text:
                content.hero_image_alt = selected_asset.alt_text
        
        return content
    
    def _update_content_body_images(self, content: GeneratedContent, assets: List[ContentAsset]) -> GeneratedContent:
        """Update content body with optimized image references"""
        if not assets:
            return content
        
        updated_body = content.body
        
        # Create mapping of original URLs to optimized assets
        asset_mapping = {}
        for asset in assets:
            if asset.original_url:
                # Prefer WebP version
                if asset.mime_type == 'image/webp':
                    asset_mapping[asset.original_url] = asset
                elif asset.original_url not in asset_mapping:
                    asset_mapping[asset.original_url] = asset
        
        # Replace URLs in content body
        for original_url, asset in asset_mapping.items():
            optimized_url = self._get_deployed_asset_url(asset)
            updated_body = updated_body.replace(original_url, optimized_url)
        
        content.body = updated_body
        return content
    
    def _get_deployed_asset_url(self, asset: ContentAsset) -> str:
        """
        Get deployed URL for asset
        
        In a real deployment, this would return the CDN or static site URL
        For now, return a relative path that will be resolved during deployment
        """
        assets_dir = GENERATOR_CONFIG.get('assets', {}).get('assets_directory', 'assets')
        return f"/{assets_dir}/{asset.filename}"
    
    async def prepare_assets_for_deployment(self, assets: List[ContentAsset]) -> List[ContentAsset]:
        """
        Prepare assets for git deployment
        
        Args:
            assets: List of processed assets
            
        Returns:
            List of assets ready for deployment
        """
        try:
            deployment_ready_assets = []
            
            for asset in assets:
                # Ensure asset file exists
                if not asset.local_path or not Path(asset.local_path).exists():
                    self.logger.warning(f"Asset file not found: {asset.filename}")
                    continue
                
                # Use optimized path if available
                deployment_path = asset.optimized_path or asset.local_path
                
                # Create deployment-ready asset
                deployment_asset = ContentAsset(
                    filename=asset.filename,
                    original_url=asset.original_url,
                    local_path=deployment_path,
                    file_size=asset.optimized_size or asset.file_size,
                    mime_type=asset.mime_type,
                    width=asset.width,
                    height=asset.height,
                    alt_text=asset.alt_text,
                    created_at=asset.created_at
                )
                
                deployment_ready_assets.append(deployment_asset)
            
            self.logger.info(f"Prepared {len(deployment_ready_assets)} assets for deployment")
            return deployment_ready_assets
            
        except Exception as e:
            error_msg = f"Failed to prepare assets for deployment: {str(e)}"
            self.logger.error(error_msg)
            raise AssetIntegrationError(error_msg)
    
    async def cleanup_processed_assets(self, assets: List[ContentAsset], keep_optimized: bool = True):
        """
        Cleanup processed asset files
        
        Args:
            assets: Assets to cleanup
            keep_optimized: Whether to keep optimized versions
        """
        try:
            cleaned_count = 0
            
            for asset in assets:
                # Clean up original file if we have optimized version
                if keep_optimized and asset.optimized_path and asset.local_path != asset.optimized_path:
                    if Path(asset.local_path).exists():
                        Path(asset.local_path).unlink()
                        cleaned_count += 1
                
                # Clean up all files if not keeping optimized
                elif not keep_optimized:
                    for path in [asset.local_path, asset.optimized_path]:
                        if path and Path(path).exists():
                            Path(path).unlink()
                            cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.info(f"Cleaned up {cleaned_count} asset files")
                
        except Exception as e:
            self.logger.warning(f"Asset cleanup failed: {str(e)}")
    
    async def get_asset_stats(self) -> Dict[str, Any]:
        """Get asset processing statistics"""
        try:
            storage_stats = self.asset_manager.get_storage_stats()
            
            return {
                'storage': storage_stats,
                'processing_options': {
                    'max_width': self.processing_options.max_width,
                    'max_height': self.processing_options.max_height,
                    'quality': self.processing_options.quality,
                    'webp_enabled': self.config.get('webp_conversion', True),
                    'optimization_enabled': self.config.get('image_optimization', True)
                },
                'supported_formats': self.config.get('supported_formats', []),
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global content asset processor instance
content_asset_processor = ContentAssetProcessor()

"""
Asset management system for content generation
Handles image processing, optimization, and file management
"""

import os
import asyncio
import aiohttp
import aiofiles
import hashlib
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Union
from datetime import datetime
from dataclasses import dataclass
from PIL import Image, ImageOps
from PIL.ExifTags import <PERSON>IE<PERSON><PERSON><PERSON>
import io

from shared.config import settings, GENERATOR_CONFIG
from shared.exceptions import TrendPlatformException
from shared.utils import AsyncHTTPClient
from monitoring.logger import get_logger
from .models import ContentAsset

logger = get_logger('generator.assets')


class AssetProcessingError(TrendPlatformException):
    """Asset processing related errors"""
    pass


@dataclass
class ImageProcessingOptions:
    """Options for image processing"""
    max_width: int = 1200
    max_height: int = 800
    quality: int = 85
    format: str = "JPEG"  # JPEG, PNG, WebP
    optimize: bool = True
    progressive: bool = True
    strip_metadata: bool = True
    
    @classmethod
    def from_config(cls) -> 'ImageProcessingOptions':
        """Create options from configuration"""
        asset_config = GENERATOR_CONFIG.get('assets', {})
        return cls(
            max_width=asset_config.get('max_image_width', 1200),
            quality=asset_config.get('image_quality', 85),
            optimize=asset_config.get('image_optimization', True)
        )


@dataclass
class AssetProcessingResult:
    """Result of asset processing operation"""
    success: bool
    original_asset: Optional[ContentAsset] = None
    processed_asset: Optional[ContentAsset] = None
    webp_asset: Optional[ContentAsset] = None
    processing_time: float = 0.0
    size_reduction: float = 0.0
    error_message: Optional[str] = None
    
    @property
    def assets_created(self) -> List[ContentAsset]:
        """Get list of all created assets"""
        assets = []
        if self.processed_asset:
            assets.append(self.processed_asset)
        if self.webp_asset:
            assets.append(self.webp_asset)
        return assets


class AssetManager:
    """
    Manages asset processing, optimization, and storage
    Handles image downloading, processing, and format conversion
    """
    
    def __init__(self, storage_dir: Optional[str] = None):
        """
        Initialize asset manager
        
        Args:
            storage_dir: Directory for storing processed assets
        """
        self.storage_dir = storage_dir or self._get_default_storage_dir()
        self.logger = logger
        self.temp_dirs: List[str] = []
        
        # Ensure storage directory exists
        Path(self.storage_dir).mkdir(parents=True, exist_ok=True)
        
        # Supported formats
        self.supported_formats = GENERATOR_CONFIG.get('assets', {}).get(
            'supported_formats', ['jpg', 'jpeg', 'png', 'webp']
        )
        
        # Processing options
        self.default_options = ImageProcessingOptions.from_config()
        
        self.logger.info(f"Asset manager initialized with storage dir: {self.storage_dir}")
    
    def _get_default_storage_dir(self) -> str:
        """Get default storage directory"""
        return os.path.join(tempfile.gettempdir(), "trendsite_assets")
    
    async def download_image(self, url: str, filename: Optional[str] = None) -> ContentAsset:
        """
        Download image from URL
        
        Args:
            url: Image URL to download
            filename: Custom filename (generates one if None)
            
        Returns:
            ContentAsset with downloaded image
            
        Raises:
            AssetProcessingError: If download fails
        """
        try:
            self.logger.info(f"Downloading image from URL: {url}")
            
            # Generate filename if not provided
            if not filename:
                url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
                filename = f"image_{url_hash}"
            
            # Download image
            async with AsyncHTTPClient(timeout=30) as client:
                response = await client.get(url)
                
                if response['status'] != 200:
                    raise AssetProcessingError(f"Failed to download image: HTTP {response['status']}")
                
                image_data = response['data']
                
                if not image_data:
                    raise AssetProcessingError("Empty image data received")
            
            # Determine file extension from content type or URL
            content_type = response.get('headers', {}).get('content-type', '')
            extension = self._get_extension_from_content_type(content_type)
            
            if not extension:
                # Try to get extension from URL
                extension = self._get_extension_from_url(url)
            
            if not extension:
                extension = '.jpg'  # Default fallback
            
            # Ensure filename has extension
            if not filename.endswith(extension):
                filename += extension
            
            # Create local file path
            local_path = os.path.join(self.storage_dir, filename)
            
            # Save image data
            async with aiofiles.open(local_path, 'wb') as f:
                await f.write(image_data)
            
            # Determine MIME type
            mime_type = self._get_mime_type_from_extension(extension)
            
            # Get image dimensions
            width, height = self._get_image_dimensions(local_path)
            
            asset = ContentAsset(
                filename=filename,
                original_url=url,
                local_path=local_path,
                file_size=len(image_data),
                mime_type=mime_type,
                width=width,
                height=height,
                created_at=datetime.utcnow()
            )
            
            self.logger.info(
                f"Image downloaded successfully",
                filename=filename,
                size=len(image_data),
                dimensions=f"{width}x{height}"
            )
            
            return asset
            
        except Exception as e:
            error_msg = f"Failed to download image from {url}: {str(e)}"
            self.logger.error(error_msg)
            raise AssetProcessingError(error_msg)
    
    async def process_image(
        self, 
        asset: ContentAsset, 
        options: Optional[ImageProcessingOptions] = None
    ) -> AssetProcessingResult:
        """
        Process and optimize image
        
        Args:
            asset: Asset to process
            options: Processing options (uses defaults if None)
            
        Returns:
            Asset processing result with optimized images
        """
        import time
        start_time = time.time()
        
        result = AssetProcessingResult(success=False, original_asset=asset)
        
        try:
            if not options:
                options = self.default_options
            
            self.logger.info(f"Processing image: {asset.filename}")
            
            # Validate input file
            if not os.path.exists(asset.local_path):
                raise AssetProcessingError(f"Source file not found: {asset.local_path}")
            
            # Open and process image
            with Image.open(asset.local_path) as img:
                # Fix orientation based on EXIF data
                img = self._fix_image_orientation(img)
                
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Create white background for transparency
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize if necessary
                original_size = img.size
                img = self._resize_image(img, options.max_width, options.max_height)
                
                # Process standard format (JPEG/PNG)
                processed_asset = await self._save_processed_image(
                    img, asset, options, suffix="_optimized"
                )
                result.processed_asset = processed_asset
                
                # Create WebP version if enabled
                if GENERATOR_CONFIG.get('assets', {}).get('webp_conversion', True):
                    webp_asset = await self._save_webp_image(img, asset, options)
                    result.webp_asset = webp_asset
                
                # Calculate size reduction
                if processed_asset:
                    original_size_bytes = asset.file_size
                    new_size_bytes = processed_asset.file_size
                    result.size_reduction = ((original_size_bytes - new_size_bytes) / original_size_bytes) * 100
                
                result.success = True
                result.processing_time = time.time() - start_time
                
                self.logger.info(
                    f"Image processing completed",
                    filename=asset.filename,
                    original_size=f"{original_size[0]}x{original_size[1]}",
                    new_size=f"{img.size[0]}x{img.size[1]}",
                    size_reduction=f"{result.size_reduction:.1f}%",
                    processing_time=f"{result.processing_time:.2f}s"
                )
                
                return result
                
        except Exception as e:
            result.processing_time = time.time() - start_time
            result.error_message = str(e)
            error_msg = f"Image processing failed for {asset.filename}: {str(e)}"
            self.logger.error(error_msg)
            return result
    
    async def download_and_process(
        self, 
        url: str, 
        filename: Optional[str] = None,
        options: Optional[ImageProcessingOptions] = None
    ) -> AssetProcessingResult:
        """
        Download and process image in one operation
        
        Args:
            url: Image URL to download
            filename: Custom filename
            options: Processing options
            
        Returns:
            Asset processing result
        """
        try:
            # Download image
            asset = await self.download_image(url, filename)
            
            # Process image
            result = await self.process_image(asset, options)
            result.original_asset = asset
            
            return result
            
        except Exception as e:
            return AssetProcessingResult(
                success=False,
                error_message=str(e)
            )
    
    def _fix_image_orientation(self, img: Image.Image) -> Image.Image:
        """Fix image orientation based on EXIF data"""
        try:
            if hasattr(img, '_getexif'):
                exif = img._getexif()
                if exif is not None:
                    orientation = exif.get(ORIENTATION)
                    if orientation:
                        img = ImageOps.exif_transpose(img)
        except Exception:
            # If EXIF processing fails, continue without rotation
            pass
        
        return img
    
    def _resize_image(self, img: Image.Image, max_width: int, max_height: int) -> Image.Image:
        """Resize image while maintaining aspect ratio"""
        width, height = img.size
        
        # Calculate new dimensions
        if width <= max_width and height <= max_height:
            return img  # No resize needed
        
        # Calculate scaling factor
        width_ratio = max_width / width
        height_ratio = max_height / height
        scale_factor = min(width_ratio, height_ratio)
        
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        # Resize with high-quality resampling
        return img.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    async def _save_processed_image(
        self, 
        img: Image.Image, 
        original_asset: ContentAsset, 
        options: ImageProcessingOptions,
        suffix: str = "_processed"
    ) -> ContentAsset:
        """Save processed image in standard format"""
        # Generate filename
        base_name = Path(original_asset.filename).stem
        extension = '.jpg' if options.format == 'JPEG' else '.png'
        filename = f"{base_name}{suffix}{extension}"
        local_path = os.path.join(self.storage_dir, filename)
        
        # Save image
        save_kwargs = {
            'format': options.format,
            'quality': options.quality,
            'optimize': options.optimize
        }
        
        if options.format == 'JPEG':
            save_kwargs['progressive'] = options.progressive
        
        img.save(local_path, **save_kwargs)
        
        # Get file size
        file_size = os.path.getsize(local_path)
        
        # Create asset
        return ContentAsset(
            filename=filename,
            original_url=original_asset.original_url,
            local_path=local_path,
            optimized_path=local_path,
            file_size=file_size,
            optimized_size=file_size,
            mime_type=f"image/{options.format.lower()}",
            width=img.size[0],
            height=img.size[1],
            created_at=datetime.utcnow()
        )
    
    async def _save_webp_image(
        self, 
        img: Image.Image, 
        original_asset: ContentAsset, 
        options: ImageProcessingOptions
    ) -> ContentAsset:
        """Save image in WebP format"""
        # Generate filename
        base_name = Path(original_asset.filename).stem
        filename = f"{base_name}_optimized.webp"
        local_path = os.path.join(self.storage_dir, filename)
        
        # Save as WebP with high quality
        webp_quality = min(options.quality + 10, 95)  # WebP can use higher quality
        img.save(
            local_path,
            format='WebP',
            quality=webp_quality,
            optimize=True,
            method=6  # Best compression
        )
        
        # Get file size
        file_size = os.path.getsize(local_path)
        
        # Create asset
        return ContentAsset(
            filename=filename,
            original_url=original_asset.original_url,
            local_path=local_path,
            optimized_path=local_path,
            file_size=file_size,
            optimized_size=file_size,
            mime_type="image/webp",
            width=img.size[0],
            height=img.size[1],
            created_at=datetime.utcnow()
        )
    
    def _get_extension_from_content_type(self, content_type: str) -> str:
        """Get file extension from content type"""
        content_type_map = {
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/png': '.png',
            'image/webp': '.webp',
            'image/gif': '.gif'
        }
        return content_type_map.get(content_type.lower(), '')
    
    def _get_extension_from_url(self, url: str) -> str:
        """Get file extension from URL"""
        try:
            path = Path(url.split('?')[0])  # Remove query parameters
            extension = path.suffix.lower()
            if extension in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                return extension
        except Exception:
            pass
        return ''
    
    def _get_mime_type_from_extension(self, extension: str) -> str:
        """Get MIME type from file extension"""
        mime_map = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.webp': 'image/webp',
            '.gif': 'image/gif'
        }
        return mime_map.get(extension.lower(), 'application/octet-stream')
    
    def _get_image_dimensions(self, file_path: str) -> Tuple[int, int]:
        """Get image dimensions"""
        try:
            with Image.open(file_path) as img:
                return img.size
        except Exception:
            return (0, 0)
    
    async def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        Cleanup temporary asset files older than specified age
        
        Args:
            max_age_hours: Maximum age of files to keep in hours
        """
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            cleaned_count = 0
            total_size_freed = 0
            
            for file_path in Path(self.storage_dir).glob('*'):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    
                    if file_age > max_age_seconds:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        cleaned_count += 1
                        total_size_freed += file_size
            
            if cleaned_count > 0:
                self.logger.info(
                    f"Cleaned up {cleaned_count} temporary files",
                    size_freed=f"{total_size_freed / 1024 / 1024:.1f}MB"
                )
            
        except Exception as e:
            self.logger.warning(f"Asset cleanup failed: {str(e)}")
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage directory statistics"""
        try:
            storage_path = Path(self.storage_dir)
            
            if not storage_path.exists():
                return {'exists': False}
            
            files = list(storage_path.glob('*'))
            file_count = len([f for f in files if f.is_file()])
            
            total_size = sum(f.stat().st_size for f in files if f.is_file())
            
            return {
                'exists': True,
                'path': str(storage_path),
                'file_count': file_count,
                'total_size_bytes': total_size,
                'total_size_mb': total_size / 1024 / 1024
            }
            
        except Exception as e:
            return {'exists': False, 'error': str(e)}
    
    def __del__(self):
        """Cleanup on destruction"""
        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    import shutil
                    shutil.rmtree(temp_dir)
            except Exception:
                pass


# Global asset manager instance
asset_manager = AssetManager()

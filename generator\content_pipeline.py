"""
Integrated content generation pipeline
Combines AI services, template system, and content processing
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from .ai.ai_service_factory import get_ai_client, AIProvider
from .ai.base_ai_client import AIRequest, AIServiceType, AIServiceError
from .templates.engine import MDXTemplateEngine, template_engine
from .utils.slug_generator import SlugGenerator, generate_unique_slug
from .utils.text_processor import TextProcessor, extract_tags, calculate_reading_time
from .asset_integration import ContentAssetProcessor, content_asset_processor
from .models import (
    ContentRequest, GeneratedContent, ContentGenerationResult,
    TemplateContext, ContentType, ContentStatus, ContentAsset
)
from database.models.trend_model import TrendEntity
from shared.config import GENERATOR_CONFIG
from shared.exceptions import ContentGenerationError
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics

logger = get_logger('generator.pipeline')


class ContentGenerationPipeline:
    """
    Integrated content generation pipeline
    Orchestrates AI services, template processing, and content creation
    """
    
    def __init__(self, ai_provider: Optional[AIProvider] = None):
        """
        Initialize content generation pipeline
        
        Args:
            ai_provider: AI provider to use (defaults to configured provider)
        """
        self.ai_client = get_ai_client(provider=ai_provider)
        self.template_engine = template_engine
        self.slug_generator = SlugGenerator()
        self.text_processor = TextProcessor()
        self.asset_processor = content_asset_processor
        self.logger = logger
        self.metrics = app_metrics
        
        # Content generation prompts
        self.prompts = {
            'article': """
            Write a comprehensive, engaging article about "{keyword}" that is currently trending in {region}.
            
            Requirements:
            - Write 800-1500 words
            - Include an engaging introduction explaining why this topic is trending
            - Provide detailed information and context about {keyword}
            - Include practical examples or use cases where relevant
            - Write in a conversational, accessible tone for general readers
            - Include relevant statistics or data points if applicable
            - End with a conclusion that summarizes key points
            - Make it SEO-friendly with natural keyword usage
            - Focus on {category} category content
            
            Target audience: General readers interested in {category} trends
            
            Article content:
            """,
            
            'description': """
            Write a compelling 150-character meta description for an article about '{keyword}' 
            trending in {region}. Make it engaging and SEO-friendly.
            """,
            
            'code_snippet': """
            Generate a practical, working code example related to "{keyword}" in the {category} category.
            
            Requirements:
            - Provide functional, tested code
            - Include clear comments explaining key parts
            - Use modern best practices and conventions
            - Make it educational and useful for developers
            - Choose the most appropriate programming language for this topic
            - Keep it concise but complete (20-50 lines)
            
            Code example:
            """,
            
            'image_prompt': """
            A modern, professional illustration representing '{keyword}' in the context of {category}, 
            trending topic, clean design, high quality, suitable for article hero image
            """
        }
    
    async def generate_content(self, request: ContentRequest) -> ContentGenerationResult:
        """
        Generate complete content for a trend
        
        Args:
            request: Content generation request
            
        Returns:
            Content generation result with all generated assets
        """
        start_time = time.time()
        result = ContentGenerationResult(
            success=False,
            trend_id=request.trend_id,
            status=ContentStatus.GENERATING
        )
        
        try:
            self.logger.info(
                f"Starting content generation",
                trend_id=request.trend_id,
                keyword=request.keyword,
                content_type=request.content_type
            )
            
            # Step 1: Generate article content
            article_content = await self._generate_article_content(request)
            
            # Step 2: Generate description
            description = await self._generate_description(request)
            
            # Step 3: Generate code snippet (if requested and relevant)
            code_snippet = None
            code_language = None
            if request.include_code and request.category.lower() == 'technology':
                code_snippet, code_language = await self._generate_code_snippet(request)
            
            # Step 4: Generate hero image (if requested)
            hero_image_url = None
            hero_image_alt = None
            if request.include_image:
                hero_image_url, hero_image_alt = await self._generate_hero_image(request)
            
            # Step 5: Process content and extract metadata
            processed_content = await self._process_content(
                article_content, request.keyword, request.category
            )
            
            # Step 6: Generate unique slug
            title = processed_content.get('title', f"Understanding {request.keyword}")
            slug = await generate_unique_slug(title)

            # Step 7: Create initial generated content object
            generated_content = GeneratedContent(
                trend_id=request.trend_id,
                title=title,
                description=description,
                body=processed_content['body'],
                slug=slug,
                content_type=request.content_type,
                meta_tags=processed_content['meta_tags'],
                tags=processed_content['tags'],
                hero_image_url=hero_image_url,
                hero_image_alt=hero_image_alt,
                code_snippet=code_snippet,
                code_language=code_language,
                word_count=processed_content['word_count'],
                readability_score=processed_content['readability_score'],
                reading_time=processed_content['reading_time'],
                ai_model_used=processed_content['ai_model_used'],
                template_used=request.template_type.value,
                generation_metadata={
                    'request': request.dict(),
                    'generation_time': time.time() - start_time,
                    'ai_provider': str(type(self.ai_client).__name__),
                    'prompts_used': {
                        'article': self.prompts['article'][:100] + '...',
                        'description': self.prompts['description'][:100] + '...'
                    }
                }
            )

            # Step 8: Process assets (images)
            if request.include_image and generated_content.hero_image_url:
                try:
                    processed_content_with_assets, processed_assets = await self.asset_processor.process_content_assets(
                        generated_content
                    )

                    # Update generated content with optimized asset URLs
                    generated_content = processed_content_with_assets

                    # Add assets to result
                    result.assets = processed_assets

                    self.logger.info(
                        f"Asset processing completed",
                        trend_id=request.trend_id,
                        assets_processed=len(processed_assets)
                    )

                except Exception as e:
                    self.logger.warning(f"Asset processing failed: {str(e)}")
                    # Continue without assets rather than failing completely

            # Step 9: Generate MDX content using template
            mdx_content = await self._generate_mdx_content(request, generated_content)
            
            # Step 9: Update result
            generation_time = time.time() - start_time
            
            result.success = True
            result.content = generated_content
            result.status = ContentStatus.GENERATED
            result.generation_time = generation_time
            result.ai_api_calls = self._count_ai_calls()
            result.completed_at = datetime.utcnow()
            
            # Record metrics
            self.metrics.record_content_generated(request.category, str(type(self.ai_client).__name__))
            self.metrics.record_content_generation_duration('complete_pipeline', generation_time)
            
            self.logger.info(
                f"Content generation completed successfully",
                trend_id=request.trend_id,
                generation_time=generation_time,
                word_count=generated_content.word_count,
                has_image=hero_image_url is not None,
                has_code=code_snippet is not None
            )
            
            return result
            
        except Exception as e:
            generation_time = time.time() - start_time
            error_msg = f"Content generation failed: {str(e)}"
            
            result.success = False
            result.error_message = error_msg
            result.error_details = {
                'exception_type': type(e).__name__,
                'generation_time': generation_time
            }
            result.status = ContentStatus.FAILED
            result.completed_at = datetime.utcnow()
            
            self.logger.error(
                error_msg,
                trend_id=request.trend_id,
                generation_time=generation_time,
                error=str(e)
            )
            
            raise ContentGenerationError(error_msg) from e
    
    async def _generate_article_content(self, request: ContentRequest) -> str:
        """Generate main article content"""
        prompt = self.prompts['article'].format(
            keyword=request.keyword,
            region=request.region,
            category=request.category
        )
        
        if request.custom_prompt:
            prompt = f"{prompt}\n\nAdditional instructions: {request.custom_prompt}"
        
        ai_request = AIRequest(
            prompt=prompt,
            service_type=AIServiceType.TEXT_GENERATION,
            max_tokens=request.max_length or GENERATOR_CONFIG['content']['max_article_length'],
            temperature=0.7
        )
        
        response = await self.ai_client.generate_text(ai_request)
        return response.content
    
    async def _generate_description(self, request: ContentRequest) -> str:
        """Generate meta description"""
        prompt = self.prompts['description'].format(
            keyword=request.keyword,
            region=request.region
        )
        
        ai_request = AIRequest(
            prompt=prompt,
            service_type=AIServiceType.TEXT_GENERATION,
            max_tokens=100,
            temperature=0.5
        )
        
        response = await self.ai_client.generate_text(ai_request)
        description = response.content.strip().replace('"', '')
        
        # Ensure description is within meta description limits
        if len(description) > 160:
            description = description[:157] + "..."
        
        return description
    
    async def _generate_code_snippet(self, request: ContentRequest) -> tuple[Optional[str], Optional[str]]:
        """Generate code snippet for technology topics"""
        try:
            prompt = self.prompts['code_snippet'].format(
                keyword=request.keyword,
                category=request.category
            )
            
            ai_request = AIRequest(
                prompt=prompt,
                service_type=AIServiceType.CODE_GENERATION,
                max_tokens=800,
                temperature=0.3
            )
            
            response = await self.ai_client.generate_text(ai_request)
            
            # Extract code and language from response
            code_snippet, code_language = self.text_processor.extract_code_snippet(response.content)
            
            return code_snippet, code_language
            
        except Exception as e:
            self.logger.warning(f"Code snippet generation failed: {str(e)}")
            return None, None
    
    async def _generate_hero_image(self, request: ContentRequest) -> tuple[Optional[str], Optional[str]]:
        """Generate hero image"""
        try:
            # Check if AI client supports image generation
            supported_models = self.ai_client.get_supported_models()
            if not supported_models.get(AIServiceType.IMAGE_GENERATION):
                self.logger.info("Image generation not supported by current AI provider")
                return None, None
            
            prompt = self.prompts['image_prompt'].format(
                keyword=request.keyword,
                category=request.category
            )
            
            ai_request = AIRequest(
                prompt=prompt,
                service_type=AIServiceType.IMAGE_GENERATION,
                additional_params={
                    'size': '1024x1024',
                    'quality': 'standard'
                }
            )
            
            response = await self.ai_client.generate_image(ai_request)
            
            alt_text = f"Illustration representing {request.keyword}"
            
            return response.content, alt_text
            
        except Exception as e:
            self.logger.warning(f"Image generation failed: {str(e)}")
            return None, None
    
    async def _process_content(self, content: str, keyword: str, category: str) -> Dict[str, Any]:
        """Process and analyze content"""
        # Extract title (first line or generate one)
        lines = content.split('\n')
        title = lines[0].strip('#').strip() if lines else f"Understanding {keyword}: The Latest Trend"
        
        # Clean title
        if title.lower().startswith(('title:', 'headline:')):
            title = title.split(':', 1)[1].strip()
        
        # Calculate metrics
        word_count = len(content.split())
        readability_score = self.text_processor.calculate_readability_score(content)
        reading_time = calculate_reading_time(content)
        
        # Extract tags
        tags = extract_tags(content, keyword, category, max_tags=8)
        
        # Generate meta tags
        meta_tags = {
            'title': title,
            'description': self.text_processor.extract_excerpt(content, 160),
            'keywords': f"{keyword}, {category}, trending",
            'og:title': title,
            'og:type': 'article',
            'twitter:card': 'summary_large_image'
        }
        
        return {
            'title': title,
            'body': content,
            'word_count': word_count,
            'readability_score': readability_score,
            'reading_time': reading_time,
            'tags': tags,
            'meta_tags': meta_tags,
            'ai_model_used': getattr(self.ai_client, 'default_models', {}).get(
                AIServiceType.TEXT_GENERATION, 'unknown'
            )
        }
    
    async def _generate_mdx_content(self, request: ContentRequest, content: GeneratedContent) -> str:
        """Generate MDX content using template system"""
        # Create template context
        context = TemplateContext(
            keyword=request.keyword,
            category=request.category,
            region=request.region,
            title=content.title,
            description=content.description,
            body=content.body,
            slug=content.slug,
            tags=content.tags,
            hero_image_url=str(content.hero_image_url) if content.hero_image_url else None,
            hero_image_alt=content.hero_image_alt,
            code_snippet=content.code_snippet,
            code_language=content.code_language,
            word_count=content.word_count,
            readability_score=content.readability_score,
            reading_time=content.reading_time,
            ai_model_used=content.ai_model_used
        )
        
        # Generate MDX content
        mdx_content = self.template_engine.create_mdx_content(
            request.template_type.value,
            context.dict(),
            content.dict()
        )
        
        return mdx_content
    
    def _count_ai_calls(self) -> int:
        """Count AI API calls made during generation"""
        usage_stats = self.ai_client.get_usage_stats()
        return usage_stats.total_requests
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the pipeline"""
        try:
            ai_health = await self.ai_client.health_check()
            template_health = self.template_engine.validate_template('article.mdx')
            
            return {
                'status': 'healthy' if ai_health.get('status') == 'healthy' and template_health else 'unhealthy',
                'ai_client': ai_health,
                'template_engine': {
                    'status': 'healthy' if template_health else 'unhealthy',
                    'available_templates': self.template_engine.list_templates()
                },
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global pipeline instance
content_pipeline = ContentGenerationPipeline()

"""
Content deployment service
Orchestrates content generation and git deployment
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass

from .content_pipeline import ContentGenerationPipeline, content_pipeline
from .git_ops import Git<PERSON>ontent<PERSON>ana<PERSON>, GitDeploymentResult, git_content_manager
from .templates.engine import MDXTemplateEngine, template_engine
from .asset_integration import ContentAssetProcessor, content_asset_processor
from .models import (
    ContentRequest, GeneratedContent, ContentGenerationResult,
    ContentStatus, TemplateType
)
from database.models.trend_model import TrendEntity, TrendRepository
from database.models.content_model import ContentRepository
from shared.config import GENERATOR_CONFIG
from shared.exceptions import ContentGenerationError, TrendPlatformException
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics

logger = get_logger('generator.deployment')


class DeploymentError(TrendPlatformException):
    """Deployment-related errors"""
    pass


@dataclass
class DeploymentResult:
    """Complete deployment result"""
    success: bool
    trend_id: str
    content_generation_result: Optional[ContentGenerationResult] = None
    git_deployment_result: Optional[GitDeploymentResult] = None
    mdx_content: Optional[str] = None
    deployment_url: Optional[str] = None
    total_time: float = 0.0
    error_message: Optional[str] = None
    
    @property
    def files_deployed(self) -> List[str]:
        """Get list of deployed files"""
        if self.git_deployment_result:
            return (self.git_deployment_result.files_created + 
                   self.git_deployment_result.files_updated)
        return []
    
    @property
    def commit_hash(self) -> Optional[str]:
        """Get deployment commit hash"""
        if self.git_deployment_result:
            return self.git_deployment_result.commit_hash
        return None


class ContentDeploymentService:
    """
    Service for deploying generated content to git repositories
    Integrates content generation pipeline with git operations
    """
    
    def __init__(self):
        self.content_pipeline = content_pipeline
        self.git_manager = git_content_manager
        self.template_engine = template_engine
        self.asset_processor = content_asset_processor
        self.trend_repo = TrendRepository()
        self.content_repo = ContentRepository()
        self.logger = logger
        self.metrics = app_metrics
    
    async def deploy_trend_content(
        self, 
        trend: TrendEntity,
        template_type: TemplateType = TemplateType.ARTICLE,
        force_regenerate: bool = False,
        custom_commit_message: Optional[str] = None
    ) -> DeploymentResult:
        """
        Deploy content for a specific trend
        
        Args:
            trend: Trend entity to generate content for
            template_type: Template type to use
            force_regenerate: Force regeneration even if content exists
            custom_commit_message: Custom git commit message
            
        Returns:
            Deployment result with details
        """
        start_time = time.time()
        result = DeploymentResult(success=False, trend_id=trend.id)
        
        try:
            self.logger.info(
                f"Starting content deployment for trend: {trend.keyword}",
                trend_id=trend.id,
                keyword=trend.keyword,
                category=trend.category,
                region=trend.region
            )
            
            # Check if content already exists
            existing_content = None
            if not force_regenerate:
                existing_content = await self.content_repo.get_by_trend_id(trend.id)
            
            # Generate or retrieve content
            if existing_content and not force_regenerate:
                self.logger.info(f"Using existing content for trend: {trend.keyword}")
                generated_content = GeneratedContent(
                    trend_id=trend.id,
                    title=existing_content.title,
                    description=existing_content.description,
                    body=existing_content.body,
                    slug=existing_content.slug or trend.keyword.lower().replace(' ', '-'),
                    tags=existing_content.meta_tags.get('keywords', '').split(', ') if existing_content.meta_tags else [],
                    hero_image_url=existing_content.hero_image_url,
                    hero_image_alt=existing_content.hero_image_alt,
                    code_snippet=existing_content.code_snippet,
                    code_language=existing_content.code_language,
                    word_count=existing_content.word_count or 0,
                    readability_score=existing_content.readability_score or 0.0,
                    ai_model_used=existing_content.ai_model_used or 'unknown'
                )
                
                # Create a mock generation result
                generation_result = ContentGenerationResult(
                    success=True,
                    trend_id=trend.id,
                    content=generated_content,
                    status=ContentStatus.GENERATED
                )
            else:
                # Generate new content
                content_request = ContentRequest(
                    trend_id=trend.id,
                    keyword=trend.keyword,
                    category=trend.category,
                    region=trend.region,
                    template_type=template_type,
                    include_code=True,
                    include_image=True
                )
                
                generation_result = await self.content_pipeline.generate_content(content_request)
                
                if not generation_result.success:
                    raise DeploymentError(f"Content generation failed: {generation_result.error_message}")
                
                generated_content = generation_result.content
            
            result.content_generation_result = generation_result
            
            # Generate MDX content using template system
            mdx_content = await self._generate_mdx_content(trend, generated_content, template_type)
            result.mdx_content = mdx_content

            # Update generated content with MDX
            generated_content.body = mdx_content

            # Prepare assets for deployment
            deployment_assets = []
            if generation_result and generation_result.assets:
                deployment_assets = await self.asset_processor.prepare_assets_for_deployment(
                    generation_result.assets
                )

            # Deploy to git repository
            git_result = await self.git_manager.deploy_content(
                content=generated_content,
                assets=deployment_assets,
                custom_commit_message=custom_commit_message
            )
            
            result.git_deployment_result = git_result
            
            if not git_result.success:
                raise DeploymentError(f"Git deployment failed: {git_result.error_message}")
            
            # Save/update content in database if it was newly generated
            if not existing_content or force_regenerate:
                await self._save_content_to_database(trend, generated_content, git_result)
            
            # Update trend status
            await self._update_trend_status(trend, 'deployed')
            
            # Calculate total time
            result.total_time = time.time() - start_time
            result.success = True
            
            # Generate deployment URL (if configured)
            result.deployment_url = self._generate_deployment_url(generated_content.slug)
            
            # Record metrics
            self.metrics.record_content_deployed(trend.category, 'git')
            self.metrics.record_deployment_duration('complete', result.total_time)
            
            self.logger.info(
                f"Content deployment completed successfully",
                trend_id=trend.id,
                commit_hash=git_result.commit_hash,
                files_deployed=len(result.files_deployed),
                total_time=result.total_time
            )
            
            return result
            
        except Exception as e:
            result.total_time = time.time() - start_time
            result.error_message = str(e)
            
            self.logger.error(
                f"Content deployment failed for trend: {trend.keyword}",
                trend_id=trend.id,
                error=str(e),
                total_time=result.total_time
            )
            
            # Update trend status to failed
            try:
                await self._update_trend_status(trend, 'deployment_failed')
            except Exception:
                pass  # Don't fail on status update error
            
            return result
    
    async def deploy_multiple_trends(
        self, 
        trends: List[TrendEntity],
        template_type: TemplateType = TemplateType.ARTICLE,
        max_concurrent: int = 3,
        force_regenerate: bool = False
    ) -> Dict[str, Any]:
        """
        Deploy content for multiple trends concurrently
        
        Args:
            trends: List of trends to deploy
            template_type: Template type to use
            max_concurrent: Maximum concurrent deployments
            force_regenerate: Force regeneration for all trends
            
        Returns:
            Batch deployment results
        """
        start_time = time.time()
        
        results = {
            'total_trends': len(trends),
            'successful_deployments': 0,
            'failed_deployments': 0,
            'deployment_results': [],
            'errors': [],
            'total_time': 0.0
        }
        
        try:
            self.logger.info(f"Starting batch deployment for {len(trends)} trends")
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def deploy_single_trend(trend: TrendEntity) -> DeploymentResult:
                async with semaphore:
                    return await self.deploy_trend_content(
                        trend=trend,
                        template_type=template_type,
                        force_regenerate=force_regenerate
                    )
            
            # Execute deployments concurrently
            deployment_tasks = [deploy_single_trend(trend) for trend in trends]
            deployment_results = await asyncio.gather(*deployment_tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(deployment_results):
                trend = trends[i]
                
                if isinstance(result, Exception):
                    # Handle exceptions
                    error_msg = f"Deployment failed for trend '{trend.keyword}': {str(result)}"
                    results['errors'].append(error_msg)
                    results['failed_deployments'] += 1
                    
                    results['deployment_results'].append({
                        'trend_id': trend.id,
                        'keyword': trend.keyword,
                        'success': False,
                        'error': str(result)
                    })
                    
                elif isinstance(result, DeploymentResult):
                    # Handle successful results
                    if result.success:
                        results['successful_deployments'] += 1
                    else:
                        results['failed_deployments'] += 1
                        if result.error_message:
                            results['errors'].append(result.error_message)
                    
                    results['deployment_results'].append({
                        'trend_id': result.trend_id,
                        'keyword': trend.keyword,
                        'success': result.success,
                        'commit_hash': result.commit_hash,
                        'files_deployed': len(result.files_deployed),
                        'deployment_time': result.total_time,
                        'error': result.error_message
                    })
            
            results['total_time'] = time.time() - start_time
            
            self.logger.info(
                f"Batch deployment completed",
                total_trends=results['total_trends'],
                successful=results['successful_deployments'],
                failed=results['failed_deployments'],
                total_time=results['total_time']
            )
            
            return results
            
        except Exception as e:
            results['total_time'] = time.time() - start_time
            error_msg = f"Batch deployment failed: {str(e)}"
            results['errors'].append(error_msg)
            
            self.logger.error(error_msg, total_time=results['total_time'])
            return results
    
    async def _generate_mdx_content(
        self, 
        trend: TrendEntity, 
        content: GeneratedContent, 
        template_type: TemplateType
    ) -> str:
        """Generate MDX content using template system"""
        try:
            # Prepare trend data
            trend_data = {
                'keyword': trend.keyword,
                'category': trend.category,
                'region': trend.region,
                'search_volume': trend.search_volume,
                'growth_rate': trend.growth_rate,
                'score': trend.score
            }
            
            # Prepare content data
            content_data = content.dict()
            
            # Generate MDX content
            mdx_content = self.template_engine.create_mdx_content(
                template_type.value,
                trend_data,
                content_data
            )
            
            return mdx_content
            
        except Exception as e:
            raise DeploymentError(f"MDX content generation failed: {str(e)}")
    
    async def _save_content_to_database(
        self, 
        trend: TrendEntity, 
        content: GeneratedContent, 
        git_result: GitDeploymentResult
    ):
        """Save generated content to database"""
        try:
            content_data = {
                'trend_id': trend.id,
                'title': content.title,
                'description': content.description,
                'body': content.body,
                'slug': content.slug,
                'meta_tags': content.meta_tags,
                'hero_image_url': content.hero_image_url,
                'hero_image_alt': content.hero_image_alt,
                'code_snippet': content.code_snippet,
                'code_language': content.code_language,
                'word_count': content.word_count,
                'readability_score': content.readability_score,
                'ai_model_used': content.ai_model_used,
                'generation_metadata': {
                    **content.generation_metadata,
                    'git_commit_hash': git_result.commit_hash,
                    'deployed_files': git_result.files_created + git_result.files_updated,
                    'deployment_time': datetime.utcnow().isoformat()
                }
            }
            
            # Check if content already exists
            existing_content = await self.content_repo.get_by_trend_id(trend.id)
            
            if existing_content:
                # Update existing content
                await self.content_repo.update(existing_content.id, content_data)
            else:
                # Create new content
                await self.content_repo.create(content_data)
                
        except Exception as e:
            self.logger.warning(f"Failed to save content to database: {str(e)}")
            # Don't fail deployment on database save error
    
    async def _update_trend_status(self, trend: TrendEntity, status: str):
        """Update trend status"""
        try:
            await self.trend_repo.update(trend.id, {'status': status})
        except Exception as e:
            self.logger.warning(f"Failed to update trend status: {str(e)}")
    
    def _generate_deployment_url(self, slug: str) -> Optional[str]:
        """Generate deployment URL for content"""
        # This would be configured based on your static site setup
        base_url = GENERATOR_CONFIG.get('deployment', {}).get('base_url')
        if base_url:
            return f"{base_url.rstrip('/')}/{slug}"
        return None
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on deployment service"""
        try:
            # Check content pipeline
            pipeline_health = await self.content_pipeline.health_check()
            
            # Check git repository
            git_status = await self.git_manager.check_repository_status()
            
            # Check template engine
            template_health = self.template_engine.validate_template('article.mdx')
            
            return {
                'status': 'healthy' if all([
                    pipeline_health.get('status') == 'healthy',
                    git_status.get('accessible', False),
                    template_health
                ]) else 'unhealthy',
                'content_pipeline': pipeline_health,
                'git_repository': git_status,
                'template_engine': {
                    'status': 'healthy' if template_health else 'unhealthy',
                    'available_templates': self.template_engine.list_templates()
                },
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global deployment service instance
deployment_service = ContentDeploymentService()

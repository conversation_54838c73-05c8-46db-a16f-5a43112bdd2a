"""
Git operations for content deployment
Manages repository cloning, content file creation, and automated commits
"""

import os
import shutil
import tempfile
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import git
from git import Repo, GitCommandError
import aiofiles
from dataclasses import dataclass

from shared.config import settings, GENERATOR_CONFIG
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger
from .models import GeneratedContent, ContentAsset

logger = get_logger('generator.git_ops')


class GitOperationError(TrendPlatformException):
    """Git operation related errors"""
    pass


@dataclass
class GitDeploymentResult:
    """Result of git deployment operation"""
    success: bool
    commit_hash: Optional[str] = None
    files_created: List[str] = None
    files_updated: List[str] = None
    error_message: Optional[str] = None
    deployment_url: Optional[str] = None
    
    def __post_init__(self):
        if self.files_created is None:
            self.files_created = []
        if self.files_updated is None:
            self.files_updated = []


@dataclass
class GitRepositoryConfig:
    """Git repository configuration"""
    url: str
    branch: str = "main"
    content_directory: str = "content"
    assets_directory: str = "assets"
    username: Optional[str] = None
    token: Optional[str] = None
    
    @property
    def authenticated_url(self) -> str:
        """Get authenticated repository URL"""
        if self.username and self.token:
            # Handle both HTTPS and SSH URLs
            if self.url.startswith('https://'):
                # Insert credentials into HTTPS URL
                url_parts = self.url.replace('https://', '').split('/')
                domain = url_parts[0]
                path = '/'.join(url_parts[1:])
                return f"https://{self.username}:{self.token}@{domain}/{path}"
            elif self.url.startswith('git@'):
                # For SSH URLs, assume key-based auth is configured
                return self.url
        return self.url


class GitContentManager:
    """
    Manages git operations for content deployment
    Handles repository cloning, file management, and automated commits
    """
    
    def __init__(self, config: Optional[GitRepositoryConfig] = None):
        """
        Initialize git content manager
        
        Args:
            config: Git repository configuration
        """
        self.config = config or self._get_default_config()
        self.logger = logger
        self._temp_dirs: List[str] = []
        
        # Git configuration
        self.git_config = {
            'user.name': 'TrendSite Content Generator',
            'user.email': '<EMAIL>'
        }
    
    def _get_default_config(self) -> GitRepositoryConfig:
        """Get default git configuration from settings"""
        return GitRepositoryConfig(
            url=settings.content_repo_url or "",
            branch=GENERATOR_CONFIG.get('git', {}).get('branch', 'main'),
            content_directory=GENERATOR_CONFIG.get('git', {}).get('content_directory', 'content'),
            assets_directory=GENERATOR_CONFIG.get('git', {}).get('assets_directory', 'assets'),
            username=settings.git_username,
            token=settings.git_token
        )
    
    async def clone_repository(self, target_dir: Optional[str] = None) -> str:
        """
        Clone repository to local directory
        
        Args:
            target_dir: Target directory for cloning (creates temp dir if None)
            
        Returns:
            Path to cloned repository
            
        Raises:
            GitOperationError: If cloning fails
        """
        if not self.config.url:
            raise GitOperationError("Repository URL not configured")
        
        if target_dir is None:
            target_dir = tempfile.mkdtemp(prefix="trendsite_repo_")
            self._temp_dirs.append(target_dir)
        
        try:
            self.logger.info(f"Cloning repository to {target_dir}")
            
            # Clone repository
            repo = Repo.clone_from(
                self.config.authenticated_url,
                target_dir,
                branch=self.config.branch,
                depth=1  # Shallow clone for efficiency
            )
            
            # Configure git user
            with repo.config_writer() as git_config:
                git_config.set_value("user", "name", self.git_config['user.name'])
                git_config.set_value("user", "email", self.git_config['user.email'])
            
            self.logger.info(f"Repository cloned successfully to {target_dir}")
            return target_dir
            
        except GitCommandError as e:
            error_msg = f"Failed to clone repository: {str(e)}"
            self.logger.error(error_msg)
            raise GitOperationError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error during repository cloning: {str(e)}"
            self.logger.error(error_msg)
            raise GitOperationError(error_msg)
    
    async def create_content_file(
        self, 
        repo_path: str, 
        content: GeneratedContent, 
        filename: Optional[str] = None
    ) -> str:
        """
        Create content file in repository
        
        Args:
            repo_path: Path to cloned repository
            content: Generated content to write
            filename: Custom filename (uses slug if None)
            
        Returns:
            Relative path to created file
            
        Raises:
            GitOperationError: If file creation fails
        """
        try:
            # Determine filename
            if filename is None:
                filename = f"{content.slug}.mdx"
            
            # Ensure filename has correct extension
            if not filename.endswith('.mdx'):
                filename += '.mdx'
            
            # Create content directory path
            content_dir = Path(repo_path) / self.config.content_directory
            content_dir.mkdir(parents=True, exist_ok=True)
            
            # Create file path
            file_path = content_dir / filename
            relative_path = f"{self.config.content_directory}/{filename}"
            
            self.logger.info(f"Creating content file: {relative_path}")
            
            # Write content to file
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(content.body)
            
            self.logger.info(f"Content file created successfully: {relative_path}")
            return relative_path
            
        except Exception as e:
            error_msg = f"Failed to create content file: {str(e)}"
            self.logger.error(error_msg)
            raise GitOperationError(error_msg)
    
    async def create_asset_files(
        self, 
        repo_path: str, 
        assets: List[ContentAsset]
    ) -> List[str]:
        """
        Create asset files in repository
        
        Args:
            repo_path: Path to cloned repository
            assets: List of assets to create
            
        Returns:
            List of relative paths to created asset files
            
        Raises:
            GitOperationError: If asset creation fails
        """
        created_files = []
        
        try:
            # Create assets directory
            assets_dir = Path(repo_path) / self.config.assets_directory
            assets_dir.mkdir(parents=True, exist_ok=True)
            
            for asset in assets:
                # Determine asset filename
                filename = asset.filename
                if not filename:
                    # Generate filename from URL or use default
                    filename = f"asset_{len(created_files)}.{asset.mime_type.split('/')[-1]}"
                
                # Create file path
                file_path = assets_dir / filename
                relative_path = f"{self.config.assets_directory}/{filename}"
                
                self.logger.info(f"Creating asset file: {relative_path}")
                
                # Copy or download asset file
                if asset.local_path and os.path.exists(asset.local_path):
                    # Copy from local path
                    shutil.copy2(asset.local_path, file_path)
                elif asset.optimized_path and os.path.exists(asset.optimized_path):
                    # Copy optimized version
                    shutil.copy2(asset.optimized_path, file_path)
                else:
                    # Skip if no local file available
                    self.logger.warning(f"No local file available for asset: {filename}")
                    continue
                
                created_files.append(relative_path)
                self.logger.info(f"Asset file created: {relative_path}")
            
            return created_files
            
        except Exception as e:
            error_msg = f"Failed to create asset files: {str(e)}"
            self.logger.error(error_msg)
            raise GitOperationError(error_msg)
    
    async def commit_and_push(
        self, 
        repo_path: str, 
        files: List[str], 
        commit_message: str,
        push: bool = True
    ) -> str:
        """
        Commit files and push to repository
        
        Args:
            repo_path: Path to cloned repository
            files: List of file paths to commit
            commit_message: Commit message
            push: Whether to push to remote repository
            
        Returns:
            Commit hash
            
        Raises:
            GitOperationError: If commit/push fails
        """
        try:
            repo = Repo(repo_path)
            
            # Add files to staging
            for file_path in files:
                repo.index.add([file_path])
                self.logger.debug(f"Added file to staging: {file_path}")
            
            # Check if there are changes to commit
            if not repo.index.diff("HEAD"):
                self.logger.info("No changes to commit")
                return repo.head.commit.hexsha
            
            # Commit changes
            commit = repo.index.commit(commit_message)
            commit_hash = commit.hexsha
            
            self.logger.info(f"Changes committed: {commit_hash}")
            
            # Push to remote repository
            if push:
                origin = repo.remote('origin')
                origin.push(self.config.branch)
                self.logger.info(f"Changes pushed to remote repository")
            
            return commit_hash
            
        except GitCommandError as e:
            error_msg = f"Git operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise GitOperationError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error during commit/push: {str(e)}"
            self.logger.error(error_msg)
            raise GitOperationError(error_msg)
    
    async def deploy_content(
        self, 
        content: GeneratedContent, 
        assets: Optional[List[ContentAsset]] = None,
        custom_commit_message: Optional[str] = None
    ) -> GitDeploymentResult:
        """
        Deploy content and assets to git repository
        
        Args:
            content: Generated content to deploy
            assets: Optional list of assets to deploy
            custom_commit_message: Custom commit message
            
        Returns:
            Deployment result with details
        """
        result = GitDeploymentResult(success=False)
        repo_path = None
        
        try:
            # Clone repository
            repo_path = await self.clone_repository()
            
            # Create content file
            content_file = await self.create_content_file(repo_path, content)
            result.files_created.append(content_file)
            
            # Create asset files if provided
            if assets:
                asset_files = await self.create_asset_files(repo_path, assets)
                result.files_created.extend(asset_files)
            
            # Generate commit message
            if custom_commit_message:
                commit_message = custom_commit_message
            else:
                commit_message = GENERATOR_CONFIG.get('git', {}).get(
                    'commit_message_template', 
                    'Add content for trend: {keyword}'
                ).format(keyword=content.title)
            
            # Commit and push changes
            all_files = result.files_created + result.files_updated
            commit_hash = await self.commit_and_push(repo_path, all_files, commit_message)
            
            # Update result
            result.success = True
            result.commit_hash = commit_hash
            
            self.logger.info(
                f"Content deployment successful",
                commit_hash=commit_hash,
                files_created=len(result.files_created),
                content_title=content.title
            )
            
            return result
            
        except Exception as e:
            error_msg = f"Content deployment failed: {str(e)}"
            result.error_message = error_msg
            self.logger.error(error_msg)
            return result
            
        finally:
            # Cleanup temporary directory
            if repo_path and repo_path in self._temp_dirs:
                try:
                    shutil.rmtree(repo_path)
                    self._temp_dirs.remove(repo_path)
                except Exception as e:
                    self.logger.warning(f"Failed to cleanup temp directory: {str(e)}")
    
    async def check_repository_status(self) -> Dict[str, Any]:
        """
        Check repository status and connectivity
        
        Returns:
            Repository status information
        """
        try:
            # Test repository access
            repo_path = await self.clone_repository()
            
            try:
                repo = Repo(repo_path)
                
                status = {
                    'accessible': True,
                    'url': self.config.url,
                    'branch': self.config.branch,
                    'latest_commit': repo.head.commit.hexsha,
                    'latest_commit_date': repo.head.commit.committed_datetime.isoformat(),
                    'content_directory': self.config.content_directory,
                    'assets_directory': self.config.assets_directory
                }
                
                return status
                
            finally:
                # Cleanup
                if repo_path in self._temp_dirs:
                    shutil.rmtree(repo_path)
                    self._temp_dirs.remove(repo_path)
                
        except Exception as e:
            return {
                'accessible': False,
                'error': str(e),
                'url': self.config.url,
                'branch': self.config.branch
            }
    
    def cleanup(self):
        """Cleanup temporary directories"""
        for temp_dir in self._temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except Exception as e:
                self.logger.warning(f"Failed to cleanup temp directory {temp_dir}: {str(e)}")
        self._temp_dirs.clear()
    
    def __del__(self):
        """Cleanup on destruction"""
        self.cleanup()


# Global git content manager instance
git_content_manager = GitContentManager()

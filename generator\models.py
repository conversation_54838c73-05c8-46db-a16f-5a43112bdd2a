"""
Pydantic models for content generation
Defines data structures for content requests, generated content, and assets
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, HttpUrl, Field, validator
from enum import Enum


class ContentType(str, Enum):
    """Content type enumeration"""
    ARTICLE = "article"
    LANDING = "landing"
    BLOG_POST = "blog_post"
    NEWS = "news"


class ContentStatus(str, Enum):
    """Content status enumeration"""
    PENDING = "pending"
    GENERATING = "generating"
    GENERATED = "generated"
    PUBLISHED = "published"
    FAILED = "failed"


class TemplateType(str, Enum):
    """Template type enumeration"""
    ARTICLE = "article.mdx"
    LANDING = "landing.mdx"
    CUSTOM = "custom.mdx"


class ContentRequest(BaseModel):
    """Request model for content generation"""
    trend_id: str = Field(..., description="ID of the trend to generate content for")
    keyword: str = Field(..., description="Primary keyword/trend")
    category: str = Field(..., description="Content category")
    region: str = Field(..., description="Target region")
    content_type: ContentType = Field(default=ContentType.ARTICLE, description="Type of content to generate")
    template_type: TemplateType = Field(default=TemplateType.ARTICLE, description="Template to use")
    priority: int = Field(default=1, ge=1, le=10, description="Generation priority (1-10)")
    custom_prompt: Optional[str] = Field(None, description="Custom prompt for AI generation")
    include_code: bool = Field(default=True, description="Include code snippets if relevant")
    include_image: bool = Field(default=True, description="Include hero image")
    max_length: Optional[int] = Field(None, ge=500, le=5000, description="Maximum content length")
    
    @validator('keyword')
    def validate_keyword(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('Keyword must be at least 2 characters long')
        return v.strip()
    
    @validator('category')
    def validate_category(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('Category must be specified')
        return v.strip()


class GeneratedContent(BaseModel):
    """Model for generated content data"""
    trend_id: str
    title: str = Field(..., description="Content title")
    description: str = Field(..., description="Content description/excerpt")
    body: str = Field(..., description="Main content body")
    slug: str = Field(..., description="URL-friendly slug")
    content_type: ContentType = Field(default=ContentType.ARTICLE)
    
    # SEO and metadata
    meta_tags: Dict[str, str] = Field(default_factory=dict, description="Meta tags for SEO")
    tags: List[str] = Field(default_factory=list, description="Content tags")
    
    # Media assets
    hero_image_url: Optional[HttpUrl] = Field(None, description="Hero image URL")
    hero_image_alt: Optional[str] = Field(None, description="Hero image alt text")
    
    # Code snippets
    code_snippet: Optional[str] = Field(None, description="Code example")
    code_language: Optional[str] = Field(None, description="Programming language")
    
    # Content metrics
    word_count: int = Field(default=0, ge=0, description="Word count")
    readability_score: Optional[float] = Field(None, ge=0, le=100, description="Readability score")
    reading_time: int = Field(default=1, ge=1, description="Estimated reading time in minutes")
    
    # Generation metadata
    ai_model_used: str = Field(default="", description="AI model used for generation")
    generation_metadata: Dict[str, Any] = Field(default_factory=dict, description="Generation metadata")
    template_used: str = Field(default="article.mdx", description="Template used")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None
    
    @validator('word_count', always=True)
    def calculate_word_count(cls, v, values):
        if v == 0 and 'body' in values:
            return len(values['body'].split())
        return v
    
    @validator('reading_time', always=True)
    def calculate_reading_time(cls, v, values):
        if v == 1 and 'word_count' in values:
            import math
            return max(1, math.ceil(values['word_count'] / 200))
        return v


class ContentAsset(BaseModel):
    """Model for content assets (images, files)"""
    filename: str = Field(..., description="Asset filename")
    original_url: Optional[HttpUrl] = Field(None, description="Original asset URL")
    local_path: str = Field(..., description="Local file path")
    optimized_path: Optional[str] = Field(None, description="Optimized file path")
    file_size: int = Field(default=0, ge=0, description="File size in bytes")
    optimized_size: Optional[int] = Field(None, ge=0, description="Optimized file size")
    mime_type: str = Field(..., description="MIME type")
    width: Optional[int] = Field(None, ge=1, description="Image width")
    height: Optional[int] = Field(None, ge=1, description="Image height")
    alt_text: Optional[str] = Field(None, description="Alt text for images")
    created_at: datetime = Field(default_factory=datetime.utcnow)


class TemplateContext(BaseModel):
    """Model for template rendering context"""
    # Trend data
    keyword: str
    category: str
    region: str
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    score: Optional[float] = None
    
    # Content data
    title: str
    description: str
    body: str
    slug: str
    tags: List[str] = Field(default_factory=list)
    
    # Media
    hero_image_url: Optional[str] = None
    hero_image_alt: Optional[str] = None
    code_snippet: Optional[str] = None
    code_language: Optional[str] = None
    
    # Metadata
    word_count: int = 0
    readability_score: Optional[float] = None
    reading_time: int = 1
    ai_model_used: str = ""
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Additional context
    custom_data: Dict[str, Any] = Field(default_factory=dict)


class ContentGenerationResult(BaseModel):
    """Result model for content generation operations"""
    success: bool = Field(..., description="Whether generation was successful")
    content_id: Optional[str] = Field(None, description="Generated content ID")
    trend_id: str = Field(..., description="Source trend ID")
    
    # Generated content
    content: Optional[GeneratedContent] = Field(None, description="Generated content data")
    assets: List[ContentAsset] = Field(default_factory=list, description="Generated assets")
    
    # File paths
    mdx_file_path: Optional[str] = Field(None, description="Path to generated MDX file")
    asset_paths: List[str] = Field(default_factory=list, description="Paths to asset files")
    
    # Generation metrics
    generation_time: float = Field(default=0.0, ge=0, description="Generation time in seconds")
    ai_api_calls: int = Field(default=0, ge=0, description="Number of AI API calls made")
    
    # Error information
    error_message: Optional[str] = Field(None, description="Error message if generation failed")
    error_details: Dict[str, Any] = Field(default_factory=dict, description="Detailed error information")
    
    # Status tracking
    status: ContentStatus = Field(default=ContentStatus.PENDING, description="Generation status")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None


class BatchGenerationRequest(BaseModel):
    """Request model for batch content generation"""
    trend_ids: List[str] = Field(..., description="List of trend IDs to process")
    content_type: ContentType = Field(default=ContentType.ARTICLE, description="Content type for all items")
    template_type: TemplateType = Field(default=TemplateType.ARTICLE, description="Template for all items")
    priority: int = Field(default=1, ge=1, le=10, description="Priority for all items")
    max_concurrent: int = Field(default=3, ge=1, le=10, description="Maximum concurrent generations")
    include_code: bool = Field(default=True, description="Include code snippets")
    include_image: bool = Field(default=True, description="Include hero images")


class BatchGenerationResult(BaseModel):
    """Result model for batch content generation"""
    total_requested: int = Field(..., ge=0, description="Total number of items requested")
    successful: int = Field(default=0, ge=0, description="Number of successful generations")
    failed: int = Field(default=0, ge=0, description="Number of failed generations")
    
    results: List[ContentGenerationResult] = Field(default_factory=list, description="Individual results")
    errors: List[str] = Field(default_factory=list, description="Error messages")
    
    total_time: float = Field(default=0.0, ge=0, description="Total processing time")
    average_time: float = Field(default=0.0, ge=0, description="Average time per item")
    
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None


class TemplateValidationResult(BaseModel):
    """Result model for template validation"""
    template_name: str = Field(..., description="Name of validated template")
    is_valid: bool = Field(..., description="Whether template is valid")
    errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    required_variables: List[str] = Field(default_factory=list, description="Required template variables")
    optional_variables: List[str] = Field(default_factory=list, description="Optional template variables")


class SlugGenerationRequest(BaseModel):
    """Request model for slug generation"""
    text: str = Field(..., description="Text to generate slug from")
    max_length: int = Field(default=50, ge=3, le=100, description="Maximum slug length")
    max_attempts: int = Field(default=10, ge=1, le=50, description="Maximum attempts to find unique slug")


class SlugGenerationResult(BaseModel):
    """Result model for slug generation"""
    original_text: str = Field(..., description="Original input text")
    generated_slug: str = Field(..., description="Generated unique slug")
    attempts_made: int = Field(default=1, ge=1, description="Number of attempts made")
    alternatives: List[str] = Field(default_factory=list, description="Alternative slug suggestions")
    is_unique: bool = Field(default=True, description="Whether slug is unique")


# Export commonly used models
__all__ = [
    'ContentType',
    'ContentStatus', 
    'TemplateType',
    'ContentRequest',
    'GeneratedContent',
    'ContentAsset',
    'TemplateContext',
    'ContentGenerationResult',
    'BatchGenerationRequest',
    'BatchGenerationResult',
    'TemplateValidationResult',
    'SlugGenerationRequest',
    'SlugGenerationResult'
]

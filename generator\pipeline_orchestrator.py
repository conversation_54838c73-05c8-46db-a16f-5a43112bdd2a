"""
Pipeline orchestrator for end-to-end content generation workflow
Coordinates all components: AI services, templates, assets, and deployment
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from .content_pipeline import ContentGenerationPipeline, content_pipeline
from .deployment_service import ContentDeploymentService, deployment_service
from .ai.ai_service_factory import get_ai_client, AIProvider
from .templates.engine import template_engine
from .asset_integration import content_asset_processor
from .git_ops import git_content_manager
from .models import (
    ContentRequest, GeneratedContent, ContentGenerationResult,
    TemplateType, ContentStatus, BatchGenerationRequest
)
from database.models.trend_model import TrendEntity, TrendRepository
from database.models.content_model import ContentRepository
from shared.config import GENERATOR_CONFIG
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics

logger = get_logger('generator.orchestrator')


class PipelineStage(str, Enum):
    """Pipeline execution stages"""
    INITIALIZATION = "initialization"
    CONTENT_GENERATION = "content_generation"
    ASSET_PROCESSING = "asset_processing"
    TEMPLATE_RENDERING = "template_rendering"
    DEPLOYMENT = "deployment"
    CLEANUP = "cleanup"
    COMPLETED = "completed"
    FAILED = "failed"


class PipelineMode(str, Enum):
    """Pipeline execution modes"""
    GENERATE_ONLY = "generate_only"  # Generate content without deployment
    DEPLOY_ONLY = "deploy_only"      # Deploy existing content
    FULL_PIPELINE = "full_pipeline"  # Complete end-to-end workflow
    BATCH_PROCESSING = "batch_processing"  # Process multiple trends


@dataclass
class PipelineConfig:
    """Configuration for pipeline execution"""
    mode: PipelineMode = PipelineMode.FULL_PIPELINE
    ai_provider: Optional[AIProvider] = None
    template_type: TemplateType = TemplateType.ARTICLE
    include_assets: bool = True
    include_code: bool = True
    force_regenerate: bool = False
    auto_deploy: bool = True
    cleanup_assets: bool = True
    max_concurrent: int = 3
    timeout_seconds: int = 300


@dataclass
class PipelineResult:
    """Result of pipeline execution"""
    success: bool
    trend_id: str
    stage: PipelineStage
    content_generation_result: Optional[ContentGenerationResult] = None
    deployment_result: Optional[Any] = None
    total_time: float = 0.0
    stage_times: Dict[str, float] = None
    error_message: Optional[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.stage_times is None:
            self.stage_times = {}
        if self.warnings is None:
            self.warnings = []


class ContentPipelineOrchestrator:
    """
    Orchestrates the complete content generation and deployment pipeline
    Manages workflow coordination, error handling, and performance monitoring
    """
    
    def __init__(self):
        self.content_pipeline = content_pipeline
        self.deployment_service = deployment_service
        self.template_engine = template_engine
        self.asset_processor = content_asset_processor
        self.git_manager = git_content_manager
        self.trend_repo = TrendRepository()
        self.content_repo = ContentRepository()
        self.logger = logger
        self.metrics = app_metrics
        
        # Pipeline configuration
        self.default_config = PipelineConfig()
        
        # Active pipeline tracking
        self.active_pipelines: Dict[str, PipelineResult] = {}
    
    async def execute_pipeline(
        self, 
        trend: TrendEntity, 
        config: Optional[PipelineConfig] = None
    ) -> PipelineResult:
        """
        Execute complete pipeline for a single trend
        
        Args:
            trend: Trend entity to process
            config: Pipeline configuration (uses default if None)
            
        Returns:
            Pipeline execution result
        """
        if config is None:
            config = self.default_config
        
        start_time = time.time()
        result = PipelineResult(
            success=False,
            trend_id=trend.id,
            stage=PipelineStage.INITIALIZATION
        )
        
        # Track active pipeline
        self.active_pipelines[trend.id] = result
        
        try:
            self.logger.info(
                f"Starting pipeline execution",
                trend_id=trend.id,
                keyword=trend.keyword,
                mode=config.mode,
                template_type=config.template_type
            )
            
            # Stage 1: Initialization
            await self._execute_initialization_stage(trend, config, result)
            
            # Stage 2: Content Generation (if needed)
            if config.mode in [PipelineMode.GENERATE_ONLY, PipelineMode.FULL_PIPELINE]:
                await self._execute_content_generation_stage(trend, config, result)
            
            # Stage 3: Asset Processing (if enabled and content generated)
            if (config.include_assets and 
                result.content_generation_result and 
                result.content_generation_result.success):
                await self._execute_asset_processing_stage(trend, config, result)
            
            # Stage 4: Template Rendering
            if config.mode in [PipelineMode.FULL_PIPELINE, PipelineMode.DEPLOY_ONLY]:
                await self._execute_template_rendering_stage(trend, config, result)
            
            # Stage 5: Deployment (if enabled)
            if config.auto_deploy and config.mode in [PipelineMode.FULL_PIPELINE, PipelineMode.DEPLOY_ONLY]:
                await self._execute_deployment_stage(trend, config, result)
            
            # Stage 6: Cleanup
            await self._execute_cleanup_stage(trend, config, result)
            
            # Mark as completed
            result.stage = PipelineStage.COMPLETED
            result.success = True
            result.total_time = time.time() - start_time
            
            # Record metrics
            self.metrics.record_pipeline_execution(
                config.mode.value,
                result.total_time,
                'success'
            )
            
            self.logger.info(
                f"Pipeline execution completed successfully",
                trend_id=trend.id,
                total_time=f"{result.total_time:.2f}s",
                stages_completed=len(result.stage_times)
            )
            
            return result
            
        except Exception as e:
            result.total_time = time.time() - start_time
            result.error_message = str(e)
            result.stage = PipelineStage.FAILED
            result.success = False
            
            # Record metrics
            self.metrics.record_pipeline_execution(
                config.mode.value,
                result.total_time,
                'error'
            )
            
            self.logger.error(
                f"Pipeline execution failed",
                trend_id=trend.id,
                stage=result.stage,
                error=str(e),
                total_time=f"{result.total_time:.2f}s"
            )
            
            return result
            
        finally:
            # Remove from active pipelines
            self.active_pipelines.pop(trend.id, None)
    
    async def execute_batch_pipeline(
        self, 
        trends: List[TrendEntity], 
        config: Optional[PipelineConfig] = None
    ) -> Dict[str, Any]:
        """
        Execute pipeline for multiple trends concurrently
        
        Args:
            trends: List of trends to process
            config: Pipeline configuration
            
        Returns:
            Batch execution results
        """
        if config is None:
            config = self.default_config
        
        config.mode = PipelineMode.BATCH_PROCESSING
        start_time = time.time()
        
        results = {
            'total_trends': len(trends),
            'successful': 0,
            'failed': 0,
            'results': [],
            'errors': [],
            'total_time': 0.0
        }
        
        try:
            self.logger.info(f"Starting batch pipeline execution for {len(trends)} trends")
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(config.max_concurrent)
            
            async def process_single_trend(trend: TrendEntity) -> PipelineResult:
                async with semaphore:
                    return await self.execute_pipeline(trend, config)
            
            # Execute pipelines concurrently
            pipeline_tasks = [process_single_trend(trend) for trend in trends]
            pipeline_results = await asyncio.gather(*pipeline_tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(pipeline_results):
                trend = trends[i]
                
                if isinstance(result, Exception):
                    # Handle exceptions
                    error_msg = f"Pipeline failed for trend '{trend.keyword}': {str(result)}"
                    results['errors'].append(error_msg)
                    results['failed'] += 1
                    
                    results['results'].append({
                        'trend_id': trend.id,
                        'keyword': trend.keyword,
                        'success': False,
                        'error': str(result)
                    })
                    
                elif isinstance(result, PipelineResult):
                    # Handle successful results
                    if result.success:
                        results['successful'] += 1
                    else:
                        results['failed'] += 1
                        if result.error_message:
                            results['errors'].append(result.error_message)
                    
                    results['results'].append({
                        'trend_id': result.trend_id,
                        'keyword': trend.keyword,
                        'success': result.success,
                        'stage': result.stage,
                        'total_time': result.total_time,
                        'error': result.error_message
                    })
            
            results['total_time'] = time.time() - start_time
            
            self.logger.info(
                f"Batch pipeline execution completed",
                total_trends=results['total_trends'],
                successful=results['successful'],
                failed=results['failed'],
                total_time=f"{results['total_time']:.2f}s"
            )
            
            return results
            
        except Exception as e:
            results['total_time'] = time.time() - start_time
            error_msg = f"Batch pipeline execution failed: {str(e)}"
            results['errors'].append(error_msg)
            
            self.logger.error(error_msg, total_time=results['total_time'])
            return results
    
    async def _execute_initialization_stage(
        self, 
        trend: TrendEntity, 
        config: PipelineConfig, 
        result: PipelineResult
    ):
        """Execute initialization stage"""
        stage_start = time.time()
        result.stage = PipelineStage.INITIALIZATION
        
        try:
            # Validate trend data
            if not trend.keyword or not trend.category:
                raise ValueError("Trend must have keyword and category")
            
            # Check if content already exists (for deploy-only mode)
            if config.mode == PipelineMode.DEPLOY_ONLY:
                existing_content = await self.content_repo.get_by_trend_id(trend.id)
                if not existing_content:
                    raise ValueError("No existing content found for deploy-only mode")
            
            # Validate git repository access
            if config.auto_deploy:
                git_status = await self.git_manager.check_repository_status()
                if not git_status.get('accessible', False):
                    result.warnings.append("Git repository not accessible - deployment will be skipped")
                    config.auto_deploy = False
            
            result.stage_times['initialization'] = time.time() - stage_start
            
        except Exception as e:
            raise TrendPlatformException(f"Initialization failed: {str(e)}")
    
    async def _execute_content_generation_stage(
        self, 
        trend: TrendEntity, 
        config: PipelineConfig, 
        result: PipelineResult
    ):
        """Execute content generation stage"""
        stage_start = time.time()
        result.stage = PipelineStage.CONTENT_GENERATION
        
        try:
            # Create content request
            content_request = ContentRequest(
                trend_id=trend.id,
                keyword=trend.keyword,
                category=trend.category,
                region=trend.region,
                template_type=config.template_type,
                include_code=config.include_code,
                include_image=config.include_assets,
                force_regenerate=config.force_regenerate
            )
            
            # Generate content
            generation_result = await self.content_pipeline.generate_content(content_request)
            result.content_generation_result = generation_result
            
            if not generation_result.success:
                raise TrendPlatformException(f"Content generation failed: {generation_result.error_message}")
            
            result.stage_times['content_generation'] = time.time() - stage_start
            
        except Exception as e:
            raise TrendPlatformException(f"Content generation failed: {str(e)}")
    
    async def _execute_asset_processing_stage(
        self, 
        trend: TrendEntity, 
        config: PipelineConfig, 
        result: PipelineResult
    ):
        """Execute asset processing stage"""
        stage_start = time.time()
        result.stage = PipelineStage.ASSET_PROCESSING
        
        try:
            if not result.content_generation_result or not result.content_generation_result.content:
                result.warnings.append("No content available for asset processing")
                return
            
            content = result.content_generation_result.content
            
            # Process assets if hero image exists
            if content.hero_image_url:
                processed_content, assets = await self.asset_processor.process_content_assets(content)
                
                # Update content with processed assets
                result.content_generation_result.content = processed_content
                if assets:
                    result.content_generation_result.assets.extend(assets)
            
            result.stage_times['asset_processing'] = time.time() - stage_start
            
        except Exception as e:
            # Asset processing failure shouldn't stop the pipeline
            result.warnings.append(f"Asset processing failed: {str(e)}")
            result.stage_times['asset_processing'] = time.time() - stage_start
    
    async def _execute_template_rendering_stage(
        self, 
        trend: TrendEntity, 
        config: PipelineConfig, 
        result: PipelineResult
    ):
        """Execute template rendering stage"""
        stage_start = time.time()
        result.stage = PipelineStage.TEMPLATE_RENDERING
        
        try:
            # Get content (either generated or existing)
            content = None
            if result.content_generation_result and result.content_generation_result.content:
                content = result.content_generation_result.content
            else:
                # Load existing content for deploy-only mode
                existing_content = await self.content_repo.get_by_trend_id(trend.id)
                if existing_content:
                    content = GeneratedContent(
                        trend_id=trend.id,
                        title=existing_content.title,
                        description=existing_content.description,
                        body=existing_content.body,
                        slug=existing_content.slug or trend.keyword.lower().replace(' ', '-'),
                        tags=existing_content.meta_tags.get('keywords', '').split(', ') if existing_content.meta_tags else [],
                        hero_image_url=existing_content.hero_image_url,
                        hero_image_alt=existing_content.hero_image_alt,
                        code_snippet=existing_content.code_snippet,
                        code_language=existing_content.code_language,
                        word_count=existing_content.word_count or 0,
                        readability_score=existing_content.readability_score or 0.0,
                        ai_model_used=existing_content.ai_model_used or 'unknown'
                    )
            
            if not content:
                raise TrendPlatformException("No content available for template rendering")
            
            # Render template
            trend_data = {
                'keyword': trend.keyword,
                'category': trend.category,
                'region': trend.region,
                'search_volume': trend.search_volume,
                'growth_rate': trend.growth_rate,
                'score': trend.score
            }
            
            content_data = content.dict()
            
            mdx_content = self.template_engine.create_mdx_content(
                config.template_type.value,
                trend_data,
                content_data
            )
            
            # Update content body with rendered MDX
            content.body = mdx_content
            
            result.stage_times['template_rendering'] = time.time() - stage_start
            
        except Exception as e:
            raise TrendPlatformException(f"Template rendering failed: {str(e)}")
    
    async def _execute_deployment_stage(
        self, 
        trend: TrendEntity, 
        config: PipelineConfig, 
        result: PipelineResult
    ):
        """Execute deployment stage"""
        stage_start = time.time()
        result.stage = PipelineStage.DEPLOYMENT
        
        try:
            deployment_result = await self.deployment_service.deploy_trend_content(
                trend=trend,
                template_type=config.template_type,
                force_regenerate=config.force_regenerate
            )
            
            result.deployment_result = deployment_result
            
            if not deployment_result.success:
                raise TrendPlatformException(f"Deployment failed: {deployment_result.error_message}")
            
            result.stage_times['deployment'] = time.time() - stage_start
            
        except Exception as e:
            raise TrendPlatformException(f"Deployment failed: {str(e)}")
    
    async def _execute_cleanup_stage(
        self, 
        trend: TrendEntity, 
        config: PipelineConfig, 
        result: PipelineResult
    ):
        """Execute cleanup stage"""
        stage_start = time.time()
        result.stage = PipelineStage.CLEANUP
        
        try:
            # Cleanup processed assets if requested
            if (config.cleanup_assets and 
                result.content_generation_result and 
                result.content_generation_result.assets):
                
                await self.asset_processor.cleanup_processed_assets(
                    result.content_generation_result.assets,
                    keep_optimized=False  # Clean up everything after deployment
                )
            
            # Update trend status
            await self.trend_repo.update(trend.id, {'status': 'completed'})
            
            result.stage_times['cleanup'] = time.time() - stage_start
            
        except Exception as e:
            # Cleanup failures shouldn't stop the pipeline
            result.warnings.append(f"Cleanup failed: {str(e)}")
            result.stage_times['cleanup'] = time.time() - stage_start
    
    async def get_pipeline_status(self, trend_id: str) -> Optional[PipelineResult]:
        """Get status of active pipeline"""
        return self.active_pipelines.get(trend_id)
    
    async def get_active_pipelines(self) -> Dict[str, PipelineResult]:
        """Get all active pipelines"""
        return self.active_pipelines.copy()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on pipeline orchestrator"""
        try:
            # Check all components
            content_health = await self.content_pipeline.health_check()
            deployment_health = await self.deployment_service.health_check()
            asset_health = await self.asset_processor.get_asset_stats()
            
            return {
                'status': 'healthy' if all([
                    content_health.get('status') == 'healthy',
                    deployment_health.get('status') == 'healthy',
                    asset_health.get('storage', {}).get('exists', False)
                ]) else 'unhealthy',
                'components': {
                    'content_pipeline': content_health.get('status'),
                    'deployment_service': deployment_health.get('status'),
                    'asset_processor': 'healthy' if asset_health.get('storage', {}).get('exists', False) else 'unhealthy',
                    'template_engine': 'healthy' if self.template_engine.validate_template('article.mdx') else 'unhealthy'
                },
                'active_pipelines': len(self.active_pipelines),
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global pipeline orchestrator instance
pipeline_orchestrator = ContentPipelineOrchestrator()

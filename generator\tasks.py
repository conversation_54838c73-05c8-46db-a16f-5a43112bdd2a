"""
Celery tasks for content generation
Handles AI-powered content creation and management
"""
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

from shared.celery_app import celery_app
from monitoring.logger import get_logger, set_correlation_id
from monitoring.metrics import app_metrics
from database.models.trend_model import TrendRepository, TrendStatus
from database.models.content_model import ContentRepository
from generator.content_generator import ContentGenerationOrchestrator
from shared.exceptions import GenerationError, DatabaseError

logger = get_logger('generator.tasks')
content_orchestrator = ContentGenerationOrchestrator()
trend_repo = TrendRepository()
content_repo = ContentRepository()


@celery_app.task(bind=True, max_retries=3)
def generate_content_for_trend(self, trend_id: str, correlation_id: str = None):
    """
    Generate content for a specific approved trend
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info("Starting content generation for trend", task_id=self.request.id, trend_id=trend_id)
    
    try:
        # Run the content generation
        result = asyncio.run(_generate_content_for_trend_async(trend_id))
        
        # Record metrics
        app_metrics.record_celery_task('generate_content_for_trend', 'success')
        app_metrics.record_content_generation(
            trend_id=trend_id,
            status='success',
            duration=result.get('generation_time', 0)
        )
        
        logger.info(
            "Content generation completed successfully",
            task_id=self.request.id,
            trend_id=trend_id,
            content_id=result.get('content_id'),
            generation_time=result.get('generation_time')
        )
        
        return result
        
    except Exception as exc:
        # Record failure metrics
        app_metrics.record_celery_task('generate_content_for_trend', 'failure')
        app_metrics.record_content_generation(
            trend_id=trend_id,
            status='failure',
            error=str(exc)
        )
        
        logger.error(
            f"Content generation failed: {str(exc)}",
            task_id=self.request.id,
            trend_id=trend_id,
            error=str(exc)
        )
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 60 * (2 ** self.request.retries)  # 60s, 120s, 240s
            logger.info(f"Retrying content generation in {countdown} seconds", trend_id=trend_id)
            raise self.retry(countdown=countdown, exc=exc)
        else:
            # Mark trend as failed after max retries
            asyncio.run(_mark_trend_generation_failed(trend_id, str(exc)))
            raise


@celery_app.task(bind=True, max_retries=2)
def regenerate_content_task(self, content_id: str, correlation_id: str = None):
    """
    Regenerate existing content
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info("Starting content regeneration", task_id=self.request.id, content_id=content_id)
    
    try:
        # Run the content regeneration
        result = asyncio.run(_regenerate_content_async(content_id))
        
        # Record metrics
        app_metrics.record_celery_task('regenerate_content', 'success')
        
        logger.info(
            "Content regeneration completed successfully",
            task_id=self.request.id,
            content_id=content_id,
            generation_time=result.get('generation_time')
        )
        
        return result
        
    except Exception as exc:
        # Record failure metrics
        app_metrics.record_celery_task('regenerate_content', 'failure')
        
        logger.error(
            f"Content regeneration failed: {str(exc)}",
            task_id=self.request.id,
            content_id=content_id,
            error=str(exc)
        )
        
        # Retry with shorter backoff for regeneration
        if self.request.retries < self.max_retries:
            countdown = 30 * (self.request.retries + 1)  # 30s, 60s
            logger.info(f"Retrying content regeneration in {countdown} seconds", content_id=content_id)
            raise self.retry(countdown=countdown, exc=exc)
        else:
            raise


@celery_app.task(bind=True)
def generate_content_for_approved_trends(self, correlation_id: str = None, limit: int = 10):
    """
    Generate content for all approved trends that don't have content yet
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info("Starting batch content generation", task_id=self.request.id, limit=limit)
    
    try:
        # Run the batch content generation
        result = asyncio.run(_generate_content_for_approved_trends_async(limit))
        
        # Record metrics
        app_metrics.record_celery_task('generate_content_for_approved_trends', 'success')
        
        logger.info(
            "Batch content generation completed",
            task_id=self.request.id,
            processed=result['processed'],
            successful=result['successful'],
            failed=result['failed']
        )
        
        return result
        
    except Exception as exc:
        # Record failure metrics
        app_metrics.record_celery_task('generate_content_for_approved_trends', 'failure')
        
        logger.error(
            f"Batch content generation failed: {str(exc)}",
            task_id=self.request.id,
            error=str(exc)
        )
        raise


async def _generate_content_for_trend_async(trend_id: str) -> Dict[str, Any]:
    """
    Async helper for generating content for a specific trend
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Get trend details
        trend = await trend_repo.get_by_id(trend_id)
        if not trend:
            raise GenerationError(f"Trend not found: {trend_id}")
        
        if trend.status != TrendStatus.APPROVED:
            raise GenerationError(f"Trend is not approved for content generation: {trend.status}")
        
        # Check if content already exists
        existing_content = await content_repo.get_by_trend_id(trend_id)
        if existing_content:
            logger.warning(f"Content already exists for trend {trend_id}, skipping generation")
            return {
                'content_id': existing_content[0].id,
                'status': 'already_exists',
                'generation_time': 0
            }
        
        # Generate content using orchestrator
        content_data = await content_orchestrator.generate_content_for_trend(trend)
        
        # Save content to database
        content = await content_repo.create(content_data)
        
        generation_time = asyncio.get_event_loop().time() - start_time
        
        return {
            'content_id': content.id,
            'trend_id': trend_id,
            'status': 'success',
            'generation_time': generation_time,
            'word_count': content.word_count,
            'readability_score': content.readability_score
        }
        
    except Exception as e:
        generation_time = asyncio.get_event_loop().time() - start_time
        logger.error(f"Content generation failed for trend {trend_id}: {str(e)}")
        raise GenerationError(f"Content generation failed: {str(e)}")


async def _regenerate_content_async(content_id: str) -> Dict[str, Any]:
    """
    Async helper for regenerating existing content
    """
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Get existing content
        content = await content_repo.get_by_id(content_id)
        if not content:
            raise GenerationError(f"Content not found: {content_id}")
        
        # Get associated trend
        trend = await trend_repo.get_by_id(content.trend_id)
        if not trend:
            raise GenerationError(f"Associated trend not found: {content.trend_id}")
        
        # Generate new content
        new_content_data = await content_orchestrator.generate_content_for_trend(trend)
        
        # Update existing content record
        update_data = {
            'title': new_content_data['title'],
            'description': new_content_data.get('description'),
            'body': new_content_data['body'],
            'meta_tags': new_content_data.get('meta_tags', {}),
            'hero_image_url': new_content_data.get('hero_image_url'),
            'hero_image_alt': new_content_data.get('hero_image_alt'),
            'code_snippet': new_content_data.get('code_snippet'),
            'code_language': new_content_data.get('code_language'),
            'word_count': new_content_data.get('word_count'),
            'readability_score': new_content_data.get('readability_score'),
            'ai_model_used': new_content_data.get('ai_model_used'),
            'generation_metadata': new_content_data.get('generation_metadata', {}),
            'updated_at': datetime.utcnow()
        }
        
        updated_content = await content_repo.update(content_id, update_data)
        
        generation_time = asyncio.get_event_loop().time() - start_time
        
        return {
            'content_id': content_id,
            'trend_id': content.trend_id,
            'status': 'regenerated',
            'generation_time': generation_time,
            'word_count': updated_content.word_count,
            'readability_score': updated_content.readability_score
        }
        
    except Exception as e:
        generation_time = asyncio.get_event_loop().time() - start_time
        logger.error(f"Content regeneration failed for content {content_id}: {str(e)}")
        raise GenerationError(f"Content regeneration failed: {str(e)}")


async def _generate_content_for_approved_trends_async(limit: int) -> Dict[str, Any]:
    """
    Async helper for batch content generation
    """
    try:
        # Get approved trends without content
        approved_trends = await trend_repo.get_approved_trends_without_content(limit=limit)
        
        results = {
            'processed': len(approved_trends),
            'successful': 0,
            'failed': 0,
            'errors': []
        }
        
        for trend in approved_trends:
            try:
                content_result = await _generate_content_for_trend_async(trend.id)
                results['successful'] += 1
                
                logger.info(
                    f"Generated content for trend: {trend.keyword}",
                    trend_id=trend.id,
                    content_id=content_result['content_id']
                )
                
            except Exception as e:
                results['failed'] += 1
                error_msg = f"Failed to generate content for trend {trend.id}: {str(e)}"
                results['errors'].append(error_msg)
                logger.error(error_msg, trend_id=trend.id)
        
        return results
        
    except Exception as e:
        logger.error(f"Batch content generation failed: {str(e)}")
        raise GenerationError(f"Batch content generation failed: {str(e)}")


async def _mark_trend_generation_failed(trend_id: str, error_message: str):
    """
    Mark a trend as failed for content generation
    """
    try:
        await trend_repo.update(trend_id, {
            'status': TrendStatus.FAILED,
            'error_message': f'Content generation failed: {error_message}',
            'updated_at': datetime.utcnow()
        })
        
        logger.warning(f"Marked trend as failed: {trend_id}", trend_id=trend_id, error=error_message)
        
    except Exception as e:
        logger.error(f"Failed to mark trend as failed: {str(e)}", trend_id=trend_id)

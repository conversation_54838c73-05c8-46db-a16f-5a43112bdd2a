{# Reusable template macros for content generation #}

{# Hero Image Component #}
{% macro hero_image(image_url, alt_text, title) %}
<div className="hero-image-wrapper">
  {% if image_url %}
  <img 
    src="{{ image_url }}" 
    alt="{{ alt_text or title }}"
    className="hero-image"
    loading="eager"
  />
  {% else %}
  <div className="hero-placeholder">
    <div className="placeholder-icon">🖼️</div>
    <p>{{ title }}</p>
  </div>
  {% endif %}
</div>
{% endmacro %}

{# Trending Badge Component #}
{% macro trending_badge(keyword, region, category) %}
<div className="trending-badges">
  <span className="trending-main">🔥 {{ keyword }}</span>
  <span className="trending-region">{{ region }}</span>
  <span className="trending-category">{{ category }}</span>
</div>
{% endmacro %}

{# Article Meta Component #}
{% macro article_meta(word_count, reading_time, readability_score, published_date) %}
<div className="article-meta-info">
  <div className="meta-item">
    <span className="meta-icon">📖</span>
    <span className="meta-text">{{ reading_time }} min read</span>
  </div>
  <div className="meta-item">
    <span className="meta-icon">📝</span>
    <span className="meta-text">{{ word_count }} words</span>
  </div>
  {% if readability_score %}
  <div className="meta-item">
    <span className="meta-icon">📊</span>
    <span className="meta-text">{{ readability_score | round }}% readable</span>
  </div>
  {% endif %}
  <div className="meta-item">
    <span className="meta-icon">📅</span>
    <span className="meta-text">{{ published_date | format_date('%b %d, %Y') }}</span>
  </div>
</div>
{% endmacro %}

{# Code Block Component #}
{% macro code_block(code, language, title, description) %}
<div className="code-block-container">
  {% if title %}
  <div className="code-header">
    <h3 className="code-title">{{ title }}</h3>
    {% if description %}
    <p className="code-description">{{ description }}</p>
    {% endif %}
  </div>
  {% endif %}
  
  <div className="code-wrapper">
    <div className="code-language-label">{{ language or 'code' }}</div>
    <pre className="code-block"><code className="language-{{ language or 'text' }}">{{ code }}</code></pre>
    <button className="copy-button" onclick="copyCode(this)">
      <span className="copy-icon">📋</span>
      <span className="copy-text">Copy</span>
    </button>
  </div>
</div>
{% endmacro %}

{# Tag Cloud Component #}
{% macro tag_cloud(tags, max_tags=8) %}
<div className="tag-cloud-container">
  {% for tag in tags[:max_tags] %}
  <span className="tag-item" data-tag="{{ tag }}">
    <span className="tag-hash">#</span>{{ tag }}
  </span>
  {% endfor %}
</div>
{% endmacro %}

{# Stats Card Component #}
{% macro stats_card(search_volume, growth_rate, score, region) %}
<div className="stats-card">
  <h3 className="stats-title">Trending Statistics</h3>
  <div className="stats-grid">
    <div className="stat-item">
      <div className="stat-value">{{ search_volume or 'High' }}</div>
      <div className="stat-label">Search Volume</div>
    </div>
    <div className="stat-item">
      <div className="stat-value">{{ growth_rate or 'Rising' }}%</div>
      <div className="stat-label">Growth Rate</div>
    </div>
    <div className="stat-item">
      <div className="stat-value">{{ score or 'Strong' }}/100</div>
      <div className="stat-label">Trend Score</div>
    </div>
    <div className="stat-item">
      <div className="stat-value">{{ region }}</div>
      <div className="stat-label">Region</div>
    </div>
  </div>
</div>
{% endmacro %}

{# Key Takeaways Component #}
{% macro key_takeaways(points, title="Key Takeaways") %}
<div className="key-takeaways-container">
  <h3 className="takeaways-title">{{ title }}</h3>
  <ul className="takeaways-list">
    {% for point in points %}
    <li className="takeaway-item">
      <span className="takeaway-icon">✓</span>
      <span className="takeaway-text">{{ point }}</span>
    </li>
    {% endfor %}
  </ul>
</div>
{% endmacro %}

{# Related Topics Component #}
{% macro related_topics(topics, title="Related Topics") %}
<div className="related-topics-container">
  <h3 className="related-title">{{ title }}</h3>
  <div className="related-grid">
    {% for topic in topics %}
    <div className="related-item">
      <span className="related-icon">🔗</span>
      <span className="related-text">{{ topic }}</span>
    </div>
    {% endfor %}
  </div>
</div>
{% endmacro %}

{# Call to Action Component #}
{% macro call_to_action(title, description, button_text, button_action) %}
<div className="cta-container">
  <div className="cta-content">
    <h3 className="cta-title">{{ title }}</h3>
    <p className="cta-description">{{ description }}</p>
    <button className="cta-button" onclick="{{ button_action }}">
      {{ button_text }}
    </button>
  </div>
</div>
{% endmacro %}

{# Footer Info Component #}
{% macro footer_info(ai_model, generated_date, disclaimer) %}
<div className="footer-info-container">
  <div className="generation-info">
    <h4>Content Information</h4>
    <ul className="info-list">
      <li><strong>Generated:</strong> {{ generated_date | format_date('%B %d, %Y at %I:%M %p') }}</li>
      <li><strong>AI Model:</strong> {{ ai_model or 'Advanced AI' }}</li>
      <li><strong>Content Type:</strong> AI-Generated Trending Analysis</li>
    </ul>
  </div>
  
  {% if disclaimer %}
  <div className="disclaimer">
    <p><small>{{ disclaimer }}</small></p>
  </div>
  {% endif %}
</div>
{% endmacro %}

{# Trending Analysis Component #}
{% macro trending_analysis(keyword, category, region, analysis_text) %}
<div className="trending-analysis-container">
  <div className="analysis-header">
    <h3>Why {{ keyword }} is Trending</h3>
    <div className="analysis-meta">
      <span className="analysis-category">{{ category }}</span>
      <span className="analysis-region">{{ region }}</span>
    </div>
  </div>
  
  <div className="analysis-content">
    {{ analysis_text | markdown_to_html | safe }}
  </div>
  
  <div className="analysis-footer">
    <p><em>This analysis is based on current trending data and search patterns.</em></p>
  </div>
</div>
{% endmacro %}

{# Social Share Component #}
{% macro social_share(title, url, description) %}
<div className="social-share-container">
  <h4 className="share-title">Share this trend</h4>
  <div className="share-buttons">
    <button className="share-button twitter" onclick="shareOnTwitter('{{ title }}', '{{ url }}')">
      <span className="share-icon">🐦</span>
      <span className="share-text">Twitter</span>
    </button>
    <button className="share-button facebook" onclick="shareOnFacebook('{{ url }}')">
      <span className="share-icon">📘</span>
      <span className="share-text">Facebook</span>
    </button>
    <button className="share-button linkedin" onclick="shareOnLinkedIn('{{ title }}', '{{ url }}', '{{ description }}')">
      <span className="share-icon">💼</span>
      <span className="share-text">LinkedIn</span>
    </button>
    <button className="share-button copy" onclick="copyToClipboard('{{ url }}')">
      <span className="share-icon">🔗</span>
      <span className="share-text">Copy Link</span>
    </button>
  </div>
</div>
{% endmacro %}

{# Styles for components #}
<style jsx>{`
  .hero-image-wrapper {
    width: 100%;
    max-width: 800px;
    margin: 2rem auto;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  
  .hero-image {
    width: 100%;
    height: auto;
    display: block;
  }
  
  .hero-placeholder {
    background: #f8f9fa;
    padding: 4rem 2rem;
    text-align: center;
    color: #6c757d;
  }
  
  .placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
  
  .trending-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin: 1rem 0;
  }
  
  .trending-main {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
  }
  
  .trending-region, .trending-category {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
  }
  
  .article-meta-info {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 1.5rem 0;
  }
  
  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #666;
  }
  
  .code-block-container {
    margin: 2rem 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .code-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
  }
  
  .code-title {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
  }
  
  .code-description {
    margin: 0;
    color: #666;
    font-size: 0.875rem;
  }
  
  .code-wrapper {
    position: relative;
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .code-language-label {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    text-transform: uppercase;
  }
  
  .code-block {
    padding: 1.5rem;
    margin: 0;
    overflow-x: auto;
  }
  
  .copy-button {
    position: absolute;
    bottom: 0.5rem;
    right: 0.5rem;
    background: #4a5568;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
  }
  
  .tag-cloud-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 1rem 0;
  }
  
  .tag-item {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .tag-item:hover {
    background: #dee2e6;
  }
  
  .tag-hash {
    color: #007bff;
    font-weight: 600;
  }
  
  .stats-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin: 2rem 0;
  }
  
  .stats-title {
    margin: 0 0 1rem 0;
    color: #2c3e50;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }
  
  .stat-item {
    text-align: center;
  }
  
  .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #007bff;
  }
  
  .stat-label {
    font-size: 0.875rem;
    color: #666;
    margin-top: 0.25rem;
  }
  
  .key-takeaways-container {
    background: #e8f5e8;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    margin: 2rem 0;
  }
  
  .takeaways-title {
    margin: 0 0 1rem 0;
    color: #155724;
  }
  
  .takeaways-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .takeaway-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }
  
  .takeaway-icon {
    color: #28a745;
    font-weight: bold;
    margin-top: 0.125rem;
  }
  
  .social-share-container {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
  }
  
  .share-title {
    margin: 0 0 1rem 0;
    color: #2c3e50;
  }
  
  .share-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  
  .share-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s;
  }
  
  .share-button.twitter {
    background: #1da1f2;
    color: white;
  }
  
  .share-button.facebook {
    background: #4267b2;
    color: white;
  }
  
  .share-button.linkedin {
    background: #0077b5;
    color: white;
  }
  
  .share-button.copy {
    background: #6c757d;
    color: white;
  }
  
  @media (max-width: 768px) {
    .article-meta-info {
      flex-direction: column;
      gap: 0.75rem;
    }
    
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .share-buttons {
      justify-content: center;
    }
  }
`}</style>

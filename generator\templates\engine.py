"""
MDX Template Engine for content generation
Provides Jinja2-based template rendering with custom filters and frontmatter support
"""

import os
import re
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from jinja2 import Environment, FileSystemLoader, select_autoescape, TemplateError as Jinja2TemplateError
import frontmatter
from slugify import slugify
from shared.config import settings
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger

logger = get_logger('generator.templates')


class TemplateError(TrendPlatformException):
    """Template-related errors"""
    pass


class MDXTemplateEngine:
    """
    MDX Template Engine with Jinja2 backend
    Handles template rendering, frontmatter generation, and content formatting
    """
    
    def __init__(self, template_dir: Optional[str] = None):
        """
        Initialize the template engine
        
        Args:
            template_dir: Directory containing template files
        """
        self.template_dir = template_dir or self._get_default_template_dir()
        self.env = self._setup_jinja_environment()
        self.logger = logger
        
        # Ensure template directory exists
        Path(self.template_dir).mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"MDX Template Engine initialized with template dir: {self.template_dir}")
    
    def _get_default_template_dir(self) -> str:
        """Get default template directory"""
        return os.path.join(os.path.dirname(__file__), 'templates')
    
    def _setup_jinja_environment(self) -> Environment:
        """Setup Jinja2 environment with custom filters"""
        env = Environment(
            loader=FileSystemLoader(self.template_dir),
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Add custom filters
        env.filters.update({
            'slugify': self.slugify_filter,
            'truncate_words': self.truncate_words_filter,
            'format_date': self.format_date_filter,
            'to_json': self.to_json_filter,
            'markdown_to_html': self.markdown_to_html_filter,
            'extract_excerpt': self.extract_excerpt_filter,
            'clean_html': self.clean_html_filter
        })
        
        return env
    
    def slugify_filter(self, text: str, max_length: int = 50) -> str:
        """
        Convert text to URL-friendly slug
        
        Args:
            text: Text to slugify
            max_length: Maximum length of slug
            
        Returns:
            URL-friendly slug
        """
        if not text:
            return ""
        
        slug = slugify(text, max_length=max_length, word_boundary=True)
        return slug
    
    def truncate_words_filter(self, text: str, length: int = 50, suffix: str = "...") -> str:
        """
        Truncate text to specified number of words
        
        Args:
            text: Text to truncate
            length: Number of words to keep
            suffix: Suffix to add if truncated
            
        Returns:
            Truncated text
        """
        if not text:
            return ""
        
        words = text.split()
        if len(words) <= length:
            return text
        
        return " ".join(words[:length]) + suffix
    
    def format_date_filter(self, date_obj: datetime, format_str: str = "%Y-%m-%d") -> str:
        """
        Format datetime object to string
        
        Args:
            date_obj: Datetime object to format
            format_str: Format string
            
        Returns:
            Formatted date string
        """
        if not date_obj:
            return ""
        
        if isinstance(date_obj, str):
            try:
                date_obj = datetime.fromisoformat(date_obj.replace('Z', '+00:00'))
            except ValueError:
                return date_obj
        
        return date_obj.strftime(format_str)
    
    def to_json_filter(self, obj: Any) -> str:
        """
        Convert object to JSON string
        
        Args:
            obj: Object to convert
            
        Returns:
            JSON string
        """
        try:
            return json.dumps(obj, default=str, ensure_ascii=False)
        except (TypeError, ValueError):
            return "{}"
    
    def markdown_to_html_filter(self, text: str) -> str:
        """
        Convert markdown to HTML (basic implementation)
        
        Args:
            text: Markdown text
            
        Returns:
            HTML text
        """
        if not text:
            return ""
        
        # Basic markdown conversion (can be enhanced with markdown library)
        # Convert headers
        text = re.sub(r'^### (.*$)', r'<h3>\1</h3>', text, flags=re.MULTILINE)
        text = re.sub(r'^## (.*$)', r'<h2>\1</h2>', text, flags=re.MULTILINE)
        text = re.sub(r'^# (.*$)', r'<h1>\1</h1>', text, flags=re.MULTILINE)
        
        # Convert bold and italic
        text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)
        text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)
        
        # Convert line breaks
        text = text.replace('\n\n', '</p><p>')
        text = f'<p>{text}</p>'
        
        return text
    
    def extract_excerpt_filter(self, text: str, length: int = 160) -> str:
        """
        Extract excerpt from text content
        
        Args:
            text: Full text content
            length: Maximum length of excerpt
            
        Returns:
            Text excerpt
        """
        if not text:
            return ""
        
        # Remove HTML tags and markdown
        clean_text = re.sub(r'<[^>]+>', '', text)
        clean_text = re.sub(r'[#*`_\[\]()]', '', clean_text)
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        
        if len(clean_text) <= length:
            return clean_text
        
        # Find last complete sentence within length
        truncated = clean_text[:length]
        last_period = truncated.rfind('.')
        
        if last_period > length * 0.7:  # If period is reasonably close to end
            return clean_text[:last_period + 1]
        else:
            return truncated.rstrip() + "..."
    
    def clean_html_filter(self, text: str) -> str:
        """
        Remove HTML tags from text
        
        Args:
            text: Text with HTML tags
            
        Returns:
            Clean text without HTML
        """
        if not text:
            return ""
        
        return re.sub(r'<[^>]+>', '', text)
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """
        Render template with given context
        
        Args:
            template_name: Name of template file
            context: Template context variables
            
        Returns:
            Rendered template content
            
        Raises:
            TemplateError: If template rendering fails
        """
        try:
            template = self.env.get_template(template_name)
            rendered = template.render(**context)
            
            self.logger.debug(
                f"Template rendered successfully",
                template_name=template_name,
                context_keys=list(context.keys())
            )
            
            return rendered
            
        except Jinja2TemplateError as e:
            error_msg = f"Template rendering failed for '{template_name}': {str(e)}"
            self.logger.error(error_msg, template_name=template_name, error=str(e))
            raise TemplateError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error rendering template '{template_name}': {str(e)}"
            self.logger.error(error_msg, template_name=template_name, error=str(e))
            raise TemplateError(error_msg)
    
    def generate_frontmatter(self, trend_data: Dict[str, Any], content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate frontmatter for MDX content
        
        Args:
            trend_data: Trend information
            content_data: Generated content data
            
        Returns:
            Frontmatter dictionary
        """
        frontmatter_data = {
            'title': content_data.get('title', ''),
            'description': content_data.get('description', ''),
            'slug': content_data.get('slug', ''),
            'publishedAt': datetime.utcnow().isoformat(),
            'updatedAt': datetime.utcnow().isoformat(),
            'tags': content_data.get('tags', []),
            'category': trend_data.get('category', ''),
            'region': trend_data.get('region', ''),
            'heroImage': content_data.get('hero_image_url', ''),
            'heroImageAlt': content_data.get('hero_image_alt', ''),
            'wordCount': content_data.get('word_count', 0),
            'readabilityScore': content_data.get('readability_score', 0),
            'aiModel': content_data.get('ai_model_used', ''),
            'trending': {
                'keyword': trend_data.get('keyword', ''),
                'searchVolume': trend_data.get('search_volume', 0),
                'growthRate': trend_data.get('growth_rate', 0),
                'score': trend_data.get('score', 0)
            },
            'seo': {
                'metaTitle': content_data.get('title', ''),
                'metaDescription': content_data.get('description', ''),
                'keywords': f"{trend_data.get('keyword', '')}, {trend_data.get('category', '')}, trending",
                'ogTitle': content_data.get('title', ''),
                'ogDescription': content_data.get('description', ''),
                'ogImage': content_data.get('hero_image_url', ''),
                'twitterCard': 'summary_large_image'
            }
        }
        
        return frontmatter_data
    
    def create_mdx_content(self, template_name: str, trend_data: Dict[str, Any], 
                          content_data: Dict[str, Any]) -> str:
        """
        Create complete MDX content with frontmatter and body
        
        Args:
            template_name: Template file to use
            trend_data: Trend information
            content_data: Generated content data
            
        Returns:
            Complete MDX content with frontmatter
        """
        # Generate frontmatter
        frontmatter_data = self.generate_frontmatter(trend_data, content_data)
        
        # Prepare template context
        context = {
            **trend_data,
            **content_data,
            'frontmatter': frontmatter_data,
            'generated_at': datetime.utcnow().isoformat()
        }
        
        # Render template
        rendered_content = self.render_template(template_name, context)
        
        # Create frontmatter post
        post = frontmatter.Post(rendered_content, **frontmatter_data)
        
        return frontmatter.dumps(post)
    
    def validate_template(self, template_name: str) -> bool:
        """
        Validate that template exists and can be loaded
        
        Args:
            template_name: Name of template to validate
            
        Returns:
            True if template is valid
        """
        try:
            self.env.get_template(template_name)
            return True
        except Jinja2TemplateError:
            return False
    
    def list_templates(self) -> List[str]:
        """
        List available templates
        
        Returns:
            List of template names
        """
        try:
            return self.env.list_templates()
        except Exception:
            return []


# Global template engine instance
template_engine = MDXTemplateEngine()

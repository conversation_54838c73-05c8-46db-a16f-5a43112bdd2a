---
title: "{{ title }}"
description: "{{ description }}"
slug: "{{ slug }}"
publishedAt: "{{ frontmatter.publishedAt }}"
updatedAt: "{{ frontmatter.updatedAt }}"
tags: {{ frontmatter.tags | to_json }}
category: "{{ frontmatter.category }}"
region: "{{ frontmatter.region }}"
heroImage: "{{ frontmatter.heroImage }}"
heroImageAlt: "{{ frontmatter.heroImageAlt }}"
layout: "landing"
trending:
  keyword: "{{ frontmatter.trending.keyword }}"
  searchVolume: {{ frontmatter.trending.searchVolume }}
  growthRate: {{ frontmatter.trending.growthRate }}
  score: {{ frontmatter.trending.score }}
seo:
  metaTitle: "{{ frontmatter.seo.metaTitle }}"
  metaDescription: "{{ frontmatter.seo.metaDescription }}"
  keywords: "{{ frontmatter.seo.keywords }}"
  ogTitle: "{{ frontmatter.seo.ogTitle }}"
  ogDescription: "{{ frontmatter.seo.ogDescription }}"
  ogImage: "{{ frontmatter.seo.ogImage }}"
  twitterCard: "{{ frontmatter.seo.twitterCard }}"
---

<div className="landing-hero">
  {% if hero_image_url %}
  <div className="hero-background" style={{ backgroundImage: `url({{ hero_image_url }})` }}>
    <div className="hero-overlay">
  {% endif %}
      <div className="hero-content">
        <div className="trending-indicator">
          <span className="trending-icon">🔥</span>
          <span className="trending-text">Trending Now in {{ region }}</span>
        </div>
        
        <h1 className="hero-title">{{ title }}</h1>
        
        <p className="hero-description">{{ description }}</p>
        
        <div className="hero-stats">
          <div className="stat-item">
            <span className="stat-number">{{ search_volume or 'High' }}</span>
            <span className="stat-label">Search Volume</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{{ growth_rate or 'Rising' }}%</span>
            <span className="stat-label">Growth Rate</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{{ score or 'Strong' }}/100</span>
            <span className="stat-label">Trend Score</span>
          </div>
        </div>
        
        <div className="hero-actions">
          <button className="cta-primary">Learn More</button>
          <button className="cta-secondary">Share Trend</button>
        </div>
      </div>
  {% if hero_image_url %}
    </div>
  </div>
  {% endif %}
</div>

<div className="content-section">
  <div className="container">
    <div className="content-grid">
      <div className="main-content">
        <h2>What is {{ keyword }}?</h2>
        
        <div className="content-excerpt">
          {{ body | extract_excerpt(300) | markdown_to_html | safe }}
        </div>
        
        {% if code_snippet and code_language %}
        <div className="code-preview">
          <h3>Quick Example</h3>
          <pre><code className="language-{{ code_language }}">{{ code_snippet | truncate_words(20) }}</code></pre>
          <p className="code-note">See full implementation details in the complete guide.</p>
        </div>
        {% endif %}
        
        <div className="key-points">
          <h3>Key Points</h3>
          <ul>
            {% for tag in tags[:4] %}
            <li>{{ tag | title }} insights and applications</li>
            {% endfor %}
          </ul>
        </div>
      </div>
      
      <div className="sidebar">
        <div className="trend-card">
          <h3>Trend Details</h3>
          <div className="trend-info">
            <div className="info-row">
              <span className="label">Category:</span>
              <span className="value">{{ category }}</span>
            </div>
            <div className="info-row">
              <span className="label">Region:</span>
              <span className="value">{{ region }}</span>
            </div>
            <div className="info-row">
              <span className="label">Status:</span>
              <span className="value trending">🔥 Hot</span>
            </div>
            <div className="info-row">
              <span className="label">Updated:</span>
              <span className="value">{{ generated_at | format_date('%b %d, %Y') }}</span>
            </div>
          </div>
        </div>
        
        {% if tags %}
        <div className="tags-card">
          <h3>Related Topics</h3>
          <div className="tag-cloud">
            {% for tag in tags %}
            <span className="tag">{{ tag }}</span>
            {% endfor %}
          </div>
        </div>
        {% endif %}
        
        <div className="cta-card">
          <h3>Stay Updated</h3>
          <p>Get the latest trends and insights delivered to your inbox.</p>
          <button className="subscribe-btn">Subscribe Now</button>
        </div>
      </div>
    </div>
  </div>
</div>

<div className="features-section">
  <div className="container">
    <h2>Why {{ keyword }} Matters</h2>
    <div className="features-grid">
      <div className="feature-card">
        <div className="feature-icon">📈</div>
        <h3>Growing Interest</h3>
        <p>{{ keyword }} shows {{ growth_rate or 'significant' }}% growth in search interest, indicating rising popularity.</p>
      </div>
      <div className="feature-card">
        <div className="feature-icon">🎯</div>
        <h3>{{ category }} Focus</h3>
        <p>Specifically relevant to {{ category | lower }} professionals and enthusiasts looking to stay current.</p>
      </div>
      <div className="feature-card">
        <div className="feature-icon">🌍</div>
        <h3>{{ region }} Trending</h3>
        <p>Particularly popular in {{ region }}, reflecting regional interests and market dynamics.</p>
      </div>
    </div>
  </div>
</div>

<style jsx>{`
  .landing-hero {
    position: relative;
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
  }
  
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  
  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 2rem;
  }
  
  .trending-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
  }
  
  .trending-icon {
    font-size: 1.2rem;
  }
  
  .hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin: 1rem 0;
    line-height: 1.2;
  }
  
  .hero-description {
    font-size: 1.25rem;
    margin: 1.5rem 0;
    opacity: 0.9;
    line-height: 1.6;
  }
  
  .hero-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
    flex-wrap: wrap;
  }
  
  .stat-item {
    text-align: center;
  }
  
  .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #ffd700;
  }
  
  .stat-label {
    display: block;
    font-size: 0.875rem;
    opacity: 0.8;
    margin-top: 0.25rem;
  }
  
  .hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
  }
  
  .cta-primary {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .cta-primary:hover {
    background: #ee5a24;
    transform: translateY(-2px);
  }
  
  .cta-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .cta-secondary:hover {
    background: white;
    color: #667eea;
  }
  
  .content-section {
    padding: 4rem 0;
    background: #f8f9fa;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }
  
  .content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
  }
  
  .main-content h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
  }
  
  .content-excerpt {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #555;
    margin-bottom: 2rem;
  }
  
  .code-preview {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
  }
  
  .code-preview h3 {
    color: #81c784;
    margin-top: 0;
  }
  
  .code-preview pre {
    margin: 1rem 0;
    overflow-x: auto;
  }
  
  .code-note {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-bottom: 0;
  }
  
  .key-points {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .key-points h3 {
    color: #2c3e50;
    margin-top: 0;
  }
  
  .key-points ul {
    list-style: none;
    padding: 0;
  }
  
  .key-points li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-left: 1.5rem;
  }
  
  .key-points li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
  }
  
  .sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
  
  .trend-card, .tags-card, .cta-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .trend-card h3, .tags-card h3, .cta-card h3 {
    margin-top: 0;
    color: #2c3e50;
  }
  
  .info-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
  }
  
  .info-row:last-child {
    border-bottom: none;
  }
  
  .label {
    font-weight: 600;
    color: #666;
  }
  
  .value.trending {
    color: #ff6b6b;
    font-weight: 600;
  }
  
  .tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
  }
  
  .subscribe-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    margin-top: 1rem;
  }
  
  .features-section {
    padding: 4rem 0;
    background: white;
  }
  
  .features-section h2 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 3rem;
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 8px;
    background: #f8f9fa;
  }
  
  .feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }
  
  .feature-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
  }
  
  @media (max-width: 768px) {
    .hero-title {
      font-size: 2rem;
    }
    
    .hero-stats {
      gap: 1rem;
    }
    
    .content-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
    
    .hero-actions {
      flex-direction: column;
      align-items: center;
    }
    
    .cta-primary, .cta-secondary {
      width: 200px;
    }
  }
`}</style>

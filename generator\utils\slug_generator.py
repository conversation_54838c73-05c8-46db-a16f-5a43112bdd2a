"""
Slug generation utilities with conflict resolution
Provides URL-friendly slug generation and uniqueness validation
"""

import re
import asyncio
from typing import Optional, Set, List
from slugify import slugify
from database.models.content_model import ContentRepository
from database.models.trend_model import TrendRepository
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger

logger = get_logger('generator.utils.slug')


class SlugConflictError(TrendPlatformException):
    """Raised when slug conflict cannot be resolved"""
    pass


class SlugGenerator:
    """
    Advanced slug generator with conflict resolution
    Ensures unique, SEO-friendly URL slugs for content
    """
    
    def __init__(self):
        self.content_repo = ContentRepository()
        self.trend_repo = TrendRepository()
        self.logger = logger
        
        # Reserved slugs that should not be used
        self.reserved_slugs = {
            'admin', 'api', 'www', 'mail', 'ftp', 'blog', 'shop', 'store',
            'app', 'mobile', 'web', 'site', 'home', 'index', 'about', 'contact',
            'privacy', 'terms', 'help', 'support', 'login', 'register', 'signup',
            'dashboard', 'profile', 'settings', 'account', 'user', 'users',
            'search', 'trending', 'popular', 'latest', 'new', 'hot', 'top'
        }
    
    def create_base_slug(self, text: str, max_length: int = 50) -> str:
        """
        Create base slug from text
        
        Args:
            text: Text to convert to slug
            max_length: Maximum length of slug
            
        Returns:
            Base slug string
        """
        if not text:
            return "untitled"
        
        # Clean and slugify the text
        slug = slugify(
            text,
            max_length=max_length,
            word_boundary=True,
            separator='-',
            lowercase=True
        )
        
        # Ensure slug is not empty
        if not slug:
            slug = "untitled"
        
        # Ensure slug doesn't start or end with hyphen
        slug = slug.strip('-')
        
        # Ensure minimum length
        if len(slug) < 3:
            slug = f"content-{slug}"
        
        return slug
    
    def is_reserved_slug(self, slug: str) -> bool:
        """
        Check if slug is reserved
        
        Args:
            slug: Slug to check
            
        Returns:
            True if slug is reserved
        """
        return slug.lower() in self.reserved_slugs
    
    def validate_slug_format(self, slug: str) -> bool:
        """
        Validate slug format
        
        Args:
            slug: Slug to validate
            
        Returns:
            True if slug format is valid
        """
        # Check basic format requirements
        if not slug:
            return False
        
        # Must be 3-50 characters
        if len(slug) < 3 or len(slug) > 50:
            return False
        
        # Must contain only lowercase letters, numbers, and hyphens
        if not re.match(r'^[a-z0-9-]+$', slug):
            return False
        
        # Must not start or end with hyphen
        if slug.startswith('-') or slug.endswith('-'):
            return False
        
        # Must not contain consecutive hyphens
        if '--' in slug:
            return False
        
        # Must not be reserved
        if self.is_reserved_slug(slug):
            return False
        
        return True
    
    async def is_slug_available(self, slug: str) -> bool:
        """
        Check if slug is available (not used by existing content or trends)
        
        Args:
            slug: Slug to check
            
        Returns:
            True if slug is available
        """
        try:
            # Check in trends table
            existing_trend = await self.trend_repo.get_by_slug(slug)
            if existing_trend:
                return False
            
            # Check in content table (if content has slugs)
            # This would require adding slug field to content model if not present
            # For now, we'll assume content inherits slug from trend
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Error checking slug availability: {str(e)}")
            return False
    
    async def generate_unique_slug(self, text: str, max_attempts: int = 10) -> str:
        """
        Generate unique slug with conflict resolution
        
        Args:
            text: Text to convert to slug
            max_attempts: Maximum attempts to find unique slug
            
        Returns:
            Unique slug string
            
        Raises:
            SlugConflictError: If unique slug cannot be generated
        """
        base_slug = self.create_base_slug(text)
        
        # Validate base slug format
        if not self.validate_slug_format(base_slug):
            # If base slug is invalid, create a fallback
            base_slug = self.create_base_slug(f"content {text}")
            if not self.validate_slug_format(base_slug):
                base_slug = "content-item"
        
        # Check if base slug is available
        if await self.is_slug_available(base_slug):
            self.logger.debug(f"Generated unique slug: {base_slug}")
            return base_slug
        
        # Try variations with numbers
        for attempt in range(1, max_attempts + 1):
            candidate_slug = f"{base_slug}-{attempt}"
            
            # Ensure candidate doesn't exceed length limit
            if len(candidate_slug) > 50:
                # Truncate base slug to make room for suffix
                truncated_base = base_slug[:45]
                candidate_slug = f"{truncated_base}-{attempt}"
            
            if self.validate_slug_format(candidate_slug) and await self.is_slug_available(candidate_slug):
                self.logger.debug(f"Generated unique slug after {attempt} attempts: {candidate_slug}")
                return candidate_slug
        
        # If we can't find a unique slug, try with timestamp
        import time
        timestamp_suffix = str(int(time.time()))[-6:]  # Last 6 digits of timestamp
        timestamp_slug = f"{base_slug[:40]}-{timestamp_suffix}"
        
        if self.validate_slug_format(timestamp_slug) and await self.is_slug_available(timestamp_slug):
            self.logger.warning(f"Used timestamp-based slug: {timestamp_slug}")
            return timestamp_slug
        
        # Last resort: generate random slug
        import random
        import string
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        random_slug = f"content-{random_suffix}"
        
        if await self.is_slug_available(random_slug):
            self.logger.warning(f"Used random slug: {random_slug}")
            return random_slug
        
        # If all else fails, raise error
        raise SlugConflictError(f"Unable to generate unique slug for text: {text}")
    
    async def suggest_alternative_slugs(self, text: str, count: int = 5) -> List[str]:
        """
        Suggest alternative slugs for given text
        
        Args:
            text: Text to generate alternatives for
            count: Number of alternatives to generate
            
        Returns:
            List of alternative slug suggestions
        """
        alternatives = []
        base_slug = self.create_base_slug(text)
        
        # Try different variations
        variations = [
            f"{base_slug}-guide",
            f"{base_slug}-explained",
            f"{base_slug}-overview",
            f"{base_slug}-analysis",
            f"{base_slug}-trends",
            f"understanding-{base_slug}",
            f"exploring-{base_slug}",
            f"latest-{base_slug}",
            f"{base_slug}-insights",
            f"{base_slug}-update"
        ]
        
        for variation in variations:
            if len(alternatives) >= count:
                break
            
            if self.validate_slug_format(variation) and await self.is_slug_available(variation):
                alternatives.append(variation)
        
        return alternatives
    
    def extract_keywords_for_slug(self, text: str, max_keywords: int = 3) -> str:
        """
        Extract key words from text for slug generation
        
        Args:
            text: Text to extract keywords from
            max_keywords: Maximum number of keywords to use
            
        Returns:
            Keywords joined for slug
        """
        if not text:
            return ""
        
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these',
            'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him',
            'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
        }
        
        # Extract words
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        
        # Filter out stop words and short words
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Take first max_keywords
        selected_keywords = keywords[:max_keywords]
        
        return '-'.join(selected_keywords) if selected_keywords else 'content'


# Global slug generator instance
slug_generator = SlugGenerator()


async def generate_unique_slug(text: str, max_attempts: int = 10) -> str:
    """
    Convenience function to generate unique slug
    
    Args:
        text: Text to convert to slug
        max_attempts: Maximum attempts to find unique slug
        
    Returns:
        Unique slug string
    """
    return await slug_generator.generate_unique_slug(text, max_attempts)

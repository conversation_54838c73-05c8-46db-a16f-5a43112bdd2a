"""
Text processing utilities for content generation
Provides text analysis, tag extraction, and content enhancement
"""

import re
import math
from typing import List, Dict, Set, Tuple
from collections import Counter
from monitoring.logger import get_logger

logger = get_logger('generator.utils.text')


class TextProcessor:
    """
    Advanced text processing for content analysis and enhancement
    """
    
    def __init__(self):
        self.logger = logger
        
        # Common stop words for tag extraction
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these',
            'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him',
            'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
            'about', 'into', 'through', 'during', 'before', 'after', 'above',
            'below', 'up', 'down', 'out', 'off', 'over', 'under', 'again',
            'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why',
            'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other',
            'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so',
            'than', 'too', 'very', 'just', 'now'
        }
    
    def clean_text(self, text: str) -> str:
        """
        Clean text by removing HTML tags, extra whitespace, etc.
        
        Args:
            text: Text to clean
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Remove markdown formatting
        text = re.sub(r'[#*`_\[\]()]', '', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove extra punctuation
        text = re.sub(r'[^\w\s.,!?;:-]', '', text)
        
        return text.strip()
    
    def extract_keywords(self, text: str, max_keywords: int = 10, min_length: int = 3) -> List[str]:
        """
        Extract keywords from text using frequency analysis
        
        Args:
            text: Text to analyze
            max_keywords: Maximum number of keywords to return
            min_length: Minimum length of keywords
            
        Returns:
            List of extracted keywords
        """
        if not text:
            return []
        
        # Clean and normalize text
        clean_text = self.clean_text(text.lower())
        
        # Extract words
        words = re.findall(r'\b[a-zA-Z]+\b', clean_text)
        
        # Filter words
        filtered_words = [
            word for word in words 
            if len(word) >= min_length and word not in self.stop_words
        ]
        
        # Count word frequency
        word_counts = Counter(filtered_words)
        
        # Get most common words
        keywords = [word for word, count in word_counts.most_common(max_keywords)]
        
        return keywords
    
    def extract_tags(self, text: str, keyword: str = "", category: str = "", 
                    max_tags: int = 8) -> List[str]:
        """
        Extract relevant tags from content
        
        Args:
            text: Content text
            keyword: Primary keyword/trend
            category: Content category
            max_tags: Maximum number of tags
            
        Returns:
            List of relevant tags
        """
        tags = set()
        
        # Add primary keyword as tag
        if keyword:
            tags.add(keyword.lower())
        
        # Add category as tag
        if category:
            tags.add(category.lower())
        
        # Extract keywords from text
        content_keywords = self.extract_keywords(text, max_keywords=max_tags)
        tags.update(content_keywords)
        
        # Add some common trending-related tags
        trending_tags = ['trending', 'latest', 'news', 'update']
        for tag in trending_tags:
            if len(tags) < max_tags:
                tags.add(tag)
        
        # Convert to list and limit
        tag_list = list(tags)[:max_tags]
        
        return tag_list
    
    def calculate_reading_time(self, text: str, words_per_minute: int = 200) -> int:
        """
        Calculate estimated reading time in minutes
        
        Args:
            text: Text content
            words_per_minute: Average reading speed
            
        Returns:
            Reading time in minutes
        """
        if not text:
            return 0
        
        # Count words
        word_count = len(text.split())
        
        # Calculate reading time
        reading_time = math.ceil(word_count / words_per_minute)
        
        return max(1, reading_time)  # Minimum 1 minute
    
    def extract_sentences(self, text: str) -> List[str]:
        """
        Extract sentences from text
        
        Args:
            text: Text to process
            
        Returns:
            List of sentences
        """
        if not text:
            return []
        
        # Split on sentence endings
        sentences = re.split(r'[.!?]+', text)
        
        # Clean and filter sentences
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:  # Minimum sentence length
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    def calculate_readability_score(self, text: str) -> float:
        """
        Calculate readability score (Flesch Reading Ease)
        
        Args:
            text: Text to analyze
            
        Returns:
            Readability score (0-100, higher is more readable)
        """
        if not text:
            return 0.0
        
        try:
            # Count sentences
            sentences = self.extract_sentences(text)
            sentence_count = len(sentences)
            
            if sentence_count == 0:
                return 0.0
            
            # Count words
            words = text.split()
            word_count = len(words)
            
            if word_count == 0:
                return 0.0
            
            # Count syllables (approximation)
            syllable_count = 0
            for word in words:
                word = word.lower().strip('.,!?;:"')
                syllables = len(re.findall(r'[aeiouy]+', word))
                if syllables == 0:
                    syllables = 1
                syllable_count += syllables
            
            # Calculate Flesch Reading Ease
            avg_sentence_length = word_count / sentence_count
            avg_syllables_per_word = syllable_count / word_count
            
            score = 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_syllables_per_word)
            
            # Normalize to 0-100 range
            return max(0, min(100, score))
            
        except Exception as e:
            self.logger.warning(f"Error calculating readability score: {str(e)}")
            return 50.0  # Default middle score
    
    def extract_key_phrases(self, text: str, max_phrases: int = 5) -> List[str]:
        """
        Extract key phrases (2-3 word combinations) from text
        
        Args:
            text: Text to analyze
            max_phrases: Maximum number of phrases to return
            
        Returns:
            List of key phrases
        """
        if not text:
            return []
        
        # Clean text
        clean_text = self.clean_text(text.lower())
        
        # Extract 2-3 word phrases
        words = clean_text.split()
        phrases = []
        
        # Extract 2-word phrases
        for i in range(len(words) - 1):
            phrase = f"{words[i]} {words[i+1]}"
            if all(word not in self.stop_words for word in phrase.split()):
                phrases.append(phrase)
        
        # Extract 3-word phrases
        for i in range(len(words) - 2):
            phrase = f"{words[i]} {words[i+1]} {words[i+2]}"
            if all(word not in self.stop_words for word in phrase.split()):
                phrases.append(phrase)
        
        # Count phrase frequency
        phrase_counts = Counter(phrases)
        
        # Get most common phrases
        key_phrases = [phrase for phrase, count in phrase_counts.most_common(max_phrases)]
        
        return key_phrases
    
    def generate_summary(self, text: str, max_sentences: int = 3) -> str:
        """
        Generate a summary of the text
        
        Args:
            text: Text to summarize
            max_sentences: Maximum number of sentences in summary
            
        Returns:
            Text summary
        """
        if not text:
            return ""
        
        sentences = self.extract_sentences(text)
        
        if len(sentences) <= max_sentences:
            return ". ".join(sentences) + "."
        
        # Simple extractive summarization - take first, middle, and last sentences
        if max_sentences == 3 and len(sentences) >= 3:
            summary_sentences = [
                sentences[0],
                sentences[len(sentences) // 2],
                sentences[-1]
            ]
        else:
            # Take evenly distributed sentences
            step = len(sentences) // max_sentences
            summary_sentences = [sentences[i * step] for i in range(max_sentences)]
        
        return ". ".join(summary_sentences) + "."
    
    def enhance_content_structure(self, text: str) -> str:
        """
        Enhance content structure by adding proper formatting
        
        Args:
            text: Text to enhance
            
        Returns:
            Enhanced text with better structure
        """
        if not text:
            return ""
        
        # Split into paragraphs
        paragraphs = text.split('\n\n')
        enhanced_paragraphs = []
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if paragraph:
                # Ensure proper sentence spacing
                paragraph = re.sub(r'\.([A-Z])', r'. \1', paragraph)
                enhanced_paragraphs.append(paragraph)
        
        return '\n\n'.join(enhanced_paragraphs)


# Global text processor instance
text_processor = TextProcessor()


def extract_tags(text: str, keyword: str = "", category: str = "", max_tags: int = 8) -> List[str]:
    """
    Convenience function to extract tags from text
    
    Args:
        text: Content text
        keyword: Primary keyword
        category: Content category
        max_tags: Maximum number of tags
        
    Returns:
        List of tags
    """
    return text_processor.extract_tags(text, keyword, category, max_tags)


def calculate_reading_time(text: str, words_per_minute: int = 200) -> int:
    """
    Convenience function to calculate reading time
    
    Args:
        text: Text content
        words_per_minute: Reading speed
        
    Returns:
        Reading time in minutes
    """
    return text_processor.calculate_reading_time(text, words_per_minute)

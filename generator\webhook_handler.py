"""
Webhook handler for git deployment triggers
Handles incoming webhooks from git repositories and CI/CD systems
"""

import hmac
import hashlib
import json
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import Request, HTTPException
from dataclasses import dataclass

from shared.config import settings
from shared.exceptions import TrendPlatformException
from monitoring.logger import get_logger
from .deployment_service import deployment_service

logger = get_logger('generator.webhook')


class WebhookError(TrendPlatformException):
    """Webhook-related errors"""
    pass


@dataclass
class WebhookPayload:
    """Parsed webhook payload"""
    event_type: str
    repository_url: str
    branch: str
    commit_hash: str
    commit_message: str
    author: str
    timestamp: datetime
    files_changed: list
    raw_payload: dict


class GitWebhookHandler:
    """
    Handles git webhooks for deployment triggers
    Supports GitHub, GitLab, and generic webhook formats
    """
    
    def __init__(self):
        self.logger = logger
        self.deployment_service = deployment_service
        
        # Webhook secrets for verification
        self.github_secret = settings.github_webhook_secret
        self.gitlab_secret = settings.*********************
        self.generic_secret = settings.webhook_secret
    
    async def handle_webhook(
        self, 
        request: Request, 
        provider: str = "github"
    ) -> Dict[str, Any]:
        """
        Handle incoming webhook
        
        Args:
            request: FastAPI request object
            provider: Webhook provider (github, gitlab, generic)
            
        Returns:
            Webhook processing result
        """
        try:
            # Get request body and headers
            body = await request.body()
            headers = dict(request.headers)
            
            # Verify webhook signature
            if not self._verify_signature(body, headers, provider):
                raise WebhookError("Invalid webhook signature")
            
            # Parse payload
            payload_data = json.loads(body.decode('utf-8'))
            payload = self._parse_payload(payload_data, provider)
            
            self.logger.info(
                f"Received {provider} webhook",
                event_type=payload.event_type,
                repository=payload.repository_url,
                branch=payload.branch,
                commit=payload.commit_hash[:8]
            )
            
            # Process webhook based on event type
            result = await self._process_webhook(payload)
            
            return {
                'success': True,
                'event_type': payload.event_type,
                'repository': payload.repository_url,
                'branch': payload.branch,
                'commit_hash': payload.commit_hash,
                'processing_result': result,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            error_msg = f"Webhook processing failed: {str(e)}"
            self.logger.error(error_msg)
            
            return {
                'success': False,
                'error': error_msg,
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def _verify_signature(self, body: bytes, headers: Dict[str, str], provider: str) -> bool:
        """
        Verify webhook signature
        
        Args:
            body: Request body
            headers: Request headers
            provider: Webhook provider
            
        Returns:
            True if signature is valid
        """
        try:
            if provider == "github":
                return self._verify_github_signature(body, headers)
            elif provider == "gitlab":
                return self._verify_gitlab_signature(body, headers)
            elif provider == "generic":
                return self._verify_generic_signature(body, headers)
            else:
                self.logger.warning(f"Unknown webhook provider: {provider}")
                return False
                
        except Exception as e:
            self.logger.error(f"Signature verification failed: {str(e)}")
            return False
    
    def _verify_github_signature(self, body: bytes, headers: Dict[str, str]) -> bool:
        """Verify GitHub webhook signature"""
        if not self.github_secret:
            self.logger.warning("GitHub webhook secret not configured")
            return True  # Allow if no secret configured
        
        signature_header = headers.get('x-hub-signature-256')
        if not signature_header:
            return False
        
        expected_signature = hmac.new(
            self.github_secret.encode('utf-8'),
            body,
            hashlib.sha256
        ).hexdigest()
        
        received_signature = signature_header.replace('sha256=', '')
        
        return hmac.compare_digest(expected_signature, received_signature)
    
    def _verify_gitlab_signature(self, body: bytes, headers: Dict[str, str]) -> bool:
        """Verify GitLab webhook signature"""
        if not self.gitlab_secret:
            self.logger.warning("GitLab webhook secret not configured")
            return True  # Allow if no secret configured
        
        token_header = headers.get('x-gitlab-token')
        if not token_header:
            return False
        
        return hmac.compare_digest(self.gitlab_secret, token_header)
    
    def _verify_generic_signature(self, body: bytes, headers: Dict[str, str]) -> bool:
        """Verify generic webhook signature"""
        if not self.generic_secret:
            return True  # Allow if no secret configured
        
        signature_header = headers.get('x-webhook-signature')
        if not signature_header:
            return False
        
        expected_signature = hmac.new(
            self.generic_secret.encode('utf-8'),
            body,
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(expected_signature, signature_header)
    
    def _parse_payload(self, payload_data: Dict[str, Any], provider: str) -> WebhookPayload:
        """
        Parse webhook payload based on provider
        
        Args:
            payload_data: Raw payload data
            provider: Webhook provider
            
        Returns:
            Parsed webhook payload
        """
        if provider == "github":
            return self._parse_github_payload(payload_data)
        elif provider == "gitlab":
            return self._parse_gitlab_payload(payload_data)
        elif provider == "generic":
            return self._parse_generic_payload(payload_data)
        else:
            raise WebhookError(f"Unsupported webhook provider: {provider}")
    
    def _parse_github_payload(self, payload: Dict[str, Any]) -> WebhookPayload:
        """Parse GitHub webhook payload"""
        try:
            # Determine event type
            event_type = "push"  # Default to push event
            
            # Extract repository info
            repository = payload.get('repository', {})
            repository_url = repository.get('clone_url', '')
            
            # Extract commit info
            head_commit = payload.get('head_commit', {})
            if not head_commit and payload.get('commits'):
                head_commit = payload['commits'][-1]  # Get latest commit
            
            commit_hash = head_commit.get('id', '')
            commit_message = head_commit.get('message', '')
            author = head_commit.get('author', {}).get('name', '')
            
            # Extract branch
            ref = payload.get('ref', '')
            branch = ref.replace('refs/heads/', '') if ref.startswith('refs/heads/') else 'main'
            
            # Extract changed files
            files_changed = []
            if head_commit:
                files_changed.extend(head_commit.get('added', []))
                files_changed.extend(head_commit.get('modified', []))
                files_changed.extend(head_commit.get('removed', []))
            
            return WebhookPayload(
                event_type=event_type,
                repository_url=repository_url,
                branch=branch,
                commit_hash=commit_hash,
                commit_message=commit_message,
                author=author,
                timestamp=datetime.utcnow(),
                files_changed=files_changed,
                raw_payload=payload
            )
            
        except Exception as e:
            raise WebhookError(f"Failed to parse GitHub payload: {str(e)}")
    
    def _parse_gitlab_payload(self, payload: Dict[str, Any]) -> WebhookPayload:
        """Parse GitLab webhook payload"""
        try:
            event_type = payload.get('object_kind', 'push')
            
            # Extract repository info
            repository = payload.get('repository', {})
            repository_url = repository.get('git_http_url', '')
            
            # Extract commit info
            commits = payload.get('commits', [])
            if commits:
                head_commit = commits[-1]
                commit_hash = head_commit.get('id', '')
                commit_message = head_commit.get('message', '')
                author = head_commit.get('author', {}).get('name', '')
            else:
                commit_hash = payload.get('after', '')
                commit_message = ''
                author = ''
            
            # Extract branch
            ref = payload.get('ref', '')
            branch = ref.replace('refs/heads/', '') if ref.startswith('refs/heads/') else 'main'
            
            # Extract changed files
            files_changed = []
            for commit in commits:
                files_changed.extend(commit.get('added', []))
                files_changed.extend(commit.get('modified', []))
                files_changed.extend(commit.get('removed', []))
            
            return WebhookPayload(
                event_type=event_type,
                repository_url=repository_url,
                branch=branch,
                commit_hash=commit_hash,
                commit_message=commit_message,
                author=author,
                timestamp=datetime.utcnow(),
                files_changed=files_changed,
                raw_payload=payload
            )
            
        except Exception as e:
            raise WebhookError(f"Failed to parse GitLab payload: {str(e)}")
    
    def _parse_generic_payload(self, payload: Dict[str, Any]) -> WebhookPayload:
        """Parse generic webhook payload"""
        try:
            return WebhookPayload(
                event_type=payload.get('event_type', 'push'),
                repository_url=payload.get('repository_url', ''),
                branch=payload.get('branch', 'main'),
                commit_hash=payload.get('commit_hash', ''),
                commit_message=payload.get('commit_message', ''),
                author=payload.get('author', ''),
                timestamp=datetime.utcnow(),
                files_changed=payload.get('files_changed', []),
                raw_payload=payload
            )
            
        except Exception as e:
            raise WebhookError(f"Failed to parse generic payload: {str(e)}")
    
    async def _process_webhook(self, payload: WebhookPayload) -> Dict[str, Any]:
        """
        Process webhook payload
        
        Args:
            payload: Parsed webhook payload
            
        Returns:
            Processing result
        """
        try:
            # Check if this is a deployment trigger
            if not self._should_trigger_deployment(payload):
                return {
                    'action': 'ignored',
                    'reason': 'Not a deployment trigger'
                }
            
            # Check if content files were changed
            content_files_changed = self._check_content_files_changed(payload.files_changed)
            
            if content_files_changed:
                # Trigger content regeneration/redeployment
                result = await self._trigger_content_redeployment(payload)
                return {
                    'action': 'content_redeployment',
                    'result': result
                }
            else:
                # Log the webhook but don't take action
                return {
                    'action': 'logged',
                    'reason': 'No content files changed'
                }
                
        except Exception as e:
            self.logger.error(f"Webhook processing failed: {str(e)}")
            return {
                'action': 'error',
                'error': str(e)
            }
    
    def _should_trigger_deployment(self, payload: WebhookPayload) -> bool:
        """
        Check if webhook should trigger deployment
        
        Args:
            payload: Webhook payload
            
        Returns:
            True if deployment should be triggered
        """
        # Only process push events to main/master branch
        if payload.event_type != 'push':
            return False
        
        if payload.branch not in ['main', 'master', 'production']:
            return False
        
        # Skip if commit message contains [skip ci] or [skip deploy]
        skip_patterns = ['[skip ci]', '[skip deploy]', '[ci skip]', '[deploy skip]']
        commit_message_lower = payload.commit_message.lower()
        
        for pattern in skip_patterns:
            if pattern in commit_message_lower:
                return False
        
        return True
    
    def _check_content_files_changed(self, files_changed: List[str]) -> bool:
        """
        Check if any content files were changed
        
        Args:
            files_changed: List of changed file paths
            
        Returns:
            True if content files were changed
        """
        content_extensions = ['.md', '.mdx', '.json', '.yaml', '.yml']
        content_directories = ['content', 'data', 'posts', 'articles']
        
        for file_path in files_changed:
            # Check file extension
            for ext in content_extensions:
                if file_path.endswith(ext):
                    return True
            
            # Check directory
            for directory in content_directories:
                if file_path.startswith(f"{directory}/"):
                    return True
        
        return False
    
    async def _trigger_content_redeployment(self, payload: WebhookPayload) -> Dict[str, Any]:
        """
        Trigger content redeployment
        
        Args:
            payload: Webhook payload
            
        Returns:
            Redeployment result
        """
        try:
            self.logger.info(
                f"Triggering content redeployment",
                repository=payload.repository_url,
                commit=payload.commit_hash[:8]
            )
            
            # For now, just log the trigger
            # In a full implementation, this could:
            # 1. Trigger a rebuild of the static site
            # 2. Invalidate CDN cache
            # 3. Send notifications
            # 4. Update deployment status
            
            return {
                'triggered': True,
                'commit_hash': payload.commit_hash,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Content redeployment trigger failed: {str(e)}")
            return {
                'triggered': False,
                'error': str(e)
            }


# Global webhook handler instance
webhook_handler = GitWebhookHandler()

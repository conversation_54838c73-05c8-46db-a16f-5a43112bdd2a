"""
Cleanup and maintenance manager for the Trend Platform
Handles automated cleanup of expired content, DNS records, and database maintenance
"""
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from shared.config import settings, HOUSEKEEPING_CONFIG
from shared.exceptions import HousekeepingError
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository
from database.models.deployment_model import DeploymentRepository
from database.models.dns_model import DNSRepository
from database.connection import get_db_manager

logger = get_logger('housekeeping')


class ContentCleanupManager:
    """Manages cleanup of expired content and associated resources"""
    
    def __init__(self):
        self.trend_repo = TrendRepository()
        self.content_repo = ContentRepository()
        self.deployment_repo = DeploymentRepository()
        self.dns_repo = DNSRepository()
        self.logger = logger
        self.metrics = app_metrics
    
    async def cleanup_expired_content(self) -> Dict[str, Any]:
        """Clean up expired trends and associated content"""
        start_time = asyncio.get_event_loop().time()
        
        results = {
            'expired_trends_processed': 0,
            'trends_marked_expired': 0,
            'dns_records_deleted': 0,
            'deployments_cancelled': 0,
            'errors': []
        }
        
        try:
            self.logger.info("Starting expired content cleanup")
            
            # Get expired trends
            expired_trends = await self.trend_repo.get_expired_trends()
            results['expired_trends_processed'] = len(expired_trends)
            
            for trend in expired_trends:
                try:
                    # Mark trend as expired
                    await self.trend_repo.mark_as_expired(trend.id)
                    results['trends_marked_expired'] += 1
                    
                    # Clean up DNS records
                    dns_record = await self.dns_repo.get_by_trend_id(trend.id)
                    if dns_record and dns_record.status == 'active':
                        # Import DNS tasks to trigger cleanup
                        from dns.tasks import delete_dns_record
                        delete_dns_record.delay(dns_record.id)
                        results['dns_records_deleted'] += 1
                    
                    # Cancel any pending deployments
                    deployments = await self.deployment_repo.get_by_trend_id(trend.id)
                    for deployment in deployments:
                        if deployment.status in ['pending', 'building']:
                            await self.deployment_repo.update(deployment.id, {
                                'status': 'cancelled',
                                'error_message': 'Trend expired during deployment'
                            })
                            results['deployments_cancelled'] += 1
                    
                    self.logger.info(
                        f"Cleaned up expired trend: {trend.keyword}",
                        trend_id=trend.id,
                        keyword=trend.keyword
                    )
                    
                except Exception as e:
                    error_msg = f"Failed to cleanup trend {trend.id}: {str(e)}"
                    results['errors'].append(error_msg)
                    self.logger.error(error_msg, trend_id=trend.id)
            
            duration = asyncio.get_event_loop().time() - start_time
            
            self.logger.info(
                "Expired content cleanup completed",
                duration=duration,
                **{k: v for k, v in results.items() if k != 'errors'}
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Expired content cleanup failed: {str(e)}")
            raise HousekeepingError(f"Content cleanup failed: {str(e)}")
    
    async def cleanup_old_logs(self, retention_days: int = None) -> Dict[str, Any]:
        """Clean up old system logs"""
        retention_days = retention_days or HOUSEKEEPING_CONFIG['database_maintenance']['log_retention_days']
        
        try:
            db = await get_db_manager()
            
            # Delete old system logs
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            delete_query = """
                DELETE FROM system_logs 
                WHERE created_at < $1
            """
            
            result = await db.execute(delete_query, cutoff_date)
            deleted_count = int(result.split()[-1]) if result else 0
            
            self.logger.info(
                f"Cleaned up {deleted_count} old log entries",
                retention_days=retention_days,
                cutoff_date=cutoff_date.isoformat()
            )
            
            return {
                'deleted_logs': deleted_count,
                'retention_days': retention_days,
                'cutoff_date': cutoff_date.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Log cleanup failed: {str(e)}")
            raise HousekeepingError(f"Log cleanup failed: {str(e)}")
    
    async def cleanup_old_analytics(self, retention_days: int = None) -> Dict[str, Any]:
        """Clean up old analytics data"""
        retention_days = retention_days or HOUSEKEEPING_CONFIG['database_maintenance']['analytics_retention_days']
        
        try:
            db = await get_db_manager()
            
            # Delete old analytics data
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            delete_query = """
                DELETE FROM analytics 
                WHERE created_at < $1
            """
            
            result = await db.execute(delete_query, cutoff_date)
            deleted_count = int(result.split()[-1]) if result else 0
            
            self.logger.info(
                f"Cleaned up {deleted_count} old analytics entries",
                retention_days=retention_days,
                cutoff_date=cutoff_date.isoformat()
            )
            
            return {
                'deleted_analytics': deleted_count,
                'retention_days': retention_days,
                'cutoff_date': cutoff_date.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Analytics cleanup failed: {str(e)}")
            raise HousekeepingError(f"Analytics cleanup failed: {str(e)}")


class DatabaseMaintenanceManager:
    """Manages database maintenance tasks"""
    
    def __init__(self):
        self.logger = logger
        self.metrics = app_metrics
    
    async def vacuum_database(self) -> Dict[str, Any]:
        """Perform database vacuum operation"""
        try:
            db = await get_db_manager()
            
            # Get database statistics before vacuum
            stats_before = await self._get_database_stats(db)
            
            # Perform vacuum analyze on main tables
            tables = ['trends', 'content', 'deployments', 'dns_records', 'analytics', 'system_logs']
            
            for table in tables:
                try:
                    await db.execute(f"VACUUM ANALYZE {table}")
                    self.logger.debug(f"Vacuumed table: {table}")
                except Exception as e:
                    self.logger.warning(f"Failed to vacuum table {table}: {str(e)}")
            
            # Get database statistics after vacuum
            stats_after = await self._get_database_stats(db)
            
            self.logger.info("Database vacuum completed")
            
            return {
                'tables_vacuumed': len(tables),
                'stats_before': stats_before,
                'stats_after': stats_after
            }
            
        except Exception as e:
            self.logger.error(f"Database vacuum failed: {str(e)}")
            raise HousekeepingError(f"Database vacuum failed: {str(e)}")
    
    async def reindex_database(self) -> Dict[str, Any]:
        """Reindex database tables"""
        try:
            db = await get_db_manager()
            
            # Reindex main tables
            tables = ['trends', 'content', 'deployments', 'dns_records']
            reindexed_tables = []
            
            for table in tables:
                try:
                    await db.execute(f"REINDEX TABLE {table}")
                    reindexed_tables.append(table)
                    self.logger.debug(f"Reindexed table: {table}")
                except Exception as e:
                    self.logger.warning(f"Failed to reindex table {table}: {str(e)}")
            
            self.logger.info(f"Database reindexing completed for {len(reindexed_tables)} tables")
            
            return {
                'tables_reindexed': len(reindexed_tables),
                'reindexed_tables': reindexed_tables
            }
            
        except Exception as e:
            self.logger.error(f"Database reindexing failed: {str(e)}")
            raise HousekeepingError(f"Database reindexing failed: {str(e)}")
    
    async def update_statistics(self) -> Dict[str, Any]:
        """Update database statistics"""
        try:
            db = await get_db_manager()
            
            # Update statistics for all tables
            await db.execute("ANALYZE")
            
            # Get updated statistics
            stats = await self._get_database_stats(db)
            
            self.logger.info("Database statistics updated")
            
            return {
                'statistics_updated': True,
                'database_stats': stats
            }
            
        except Exception as e:
            self.logger.error(f"Statistics update failed: {str(e)}")
            raise HousekeepingError(f"Statistics update failed: {str(e)}")
    
    async def _get_database_stats(self, db) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            # Get database size
            size_query = """
                SELECT pg_size_pretty(pg_database_size(current_database())) as database_size
            """
            size_result = await db.fetchrow(size_query)
            
            # Get table sizes
            table_sizes_query = """
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                LIMIT 10
            """
            table_sizes = await db.fetch(table_sizes_query)
            
            # Get connection stats
            connection_stats = await db.get_stats()
            
            return {
                'database_size': size_result['database_size'] if size_result else 'unknown',
                'table_sizes': [dict(row) for row in table_sizes],
                'connection_stats': connection_stats
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to get database stats: {str(e)}")
            return {}


class HousekeepingOrchestrator:
    """Orchestrates all housekeeping operations"""
    
    def __init__(self):
        self.content_cleanup = ContentCleanupManager()
        self.db_maintenance = DatabaseMaintenanceManager()
        self.logger = logger
        self.metrics = app_metrics
    
    async def run_daily_maintenance(self) -> Dict[str, Any]:
        """Run daily maintenance tasks"""
        start_time = asyncio.get_event_loop().time()
        
        results = {
            'content_cleanup': {},
            'log_cleanup': {},
            'analytics_cleanup': {},
            'errors': []
        }
        
        try:
            self.logger.info("Starting daily maintenance")
            
            # Content cleanup
            if HOUSEKEEPING_CONFIG['content_cleanup']['enabled']:
                try:
                    results['content_cleanup'] = await self.content_cleanup.cleanup_expired_content()
                except Exception as e:
                    results['errors'].append(f"Content cleanup failed: {str(e)}")
            
            # Log cleanup
            try:
                results['log_cleanup'] = await self.content_cleanup.cleanup_old_logs()
            except Exception as e:
                results['errors'].append(f"Log cleanup failed: {str(e)}")
            
            # Analytics cleanup
            try:
                results['analytics_cleanup'] = await self.content_cleanup.cleanup_old_analytics()
            except Exception as e:
                results['errors'].append(f"Analytics cleanup failed: {str(e)}")
            
            duration = asyncio.get_event_loop().time() - start_time
            
            self.logger.info(
                "Daily maintenance completed",
                duration=duration,
                error_count=len(results['errors'])
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Daily maintenance failed: {str(e)}")
            raise HousekeepingError(f"Daily maintenance failed: {str(e)}")
    
    async def run_weekly_maintenance(self) -> Dict[str, Any]:
        """Run weekly maintenance tasks"""
        start_time = asyncio.get_event_loop().time()
        
        results = {
            'vacuum': {},
            'reindex': {},
            'statistics': {},
            'errors': []
        }
        
        try:
            self.logger.info("Starting weekly maintenance")
            
            # Database vacuum
            try:
                results['vacuum'] = await self.db_maintenance.vacuum_database()
            except Exception as e:
                results['errors'].append(f"Database vacuum failed: {str(e)}")
            
            # Database reindex
            try:
                results['reindex'] = await self.db_maintenance.reindex_database()
            except Exception as e:
                results['errors'].append(f"Database reindex failed: {str(e)}")
            
            # Update statistics
            try:
                results['statistics'] = await self.db_maintenance.update_statistics()
            except Exception as e:
                results['errors'].append(f"Statistics update failed: {str(e)}")
            
            duration = asyncio.get_event_loop().time() - start_time
            
            self.logger.info(
                "Weekly maintenance completed",
                duration=duration,
                error_count=len(results['errors'])
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Weekly maintenance failed: {str(e)}")
            raise HousekeepingError(f"Weekly maintenance failed: {str(e)}")


# Global orchestrator instance
housekeeping_orchestrator = HousekeepingOrchestrator()

"""
Base cleanup task abstraction for housekeeping operations
Provides common interface and functionality for all cleanup tasks
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from shared.exceptions import HousekeepingError


class TaskStatus(str, Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class TaskPriority(str, Enum):
    """Task priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class TaskResult:
    """Result of a cleanup task execution"""
    status: TaskStatus
    items_processed: int = 0
    items_cleaned: int = 0
    bytes_freed: int = 0
    execution_time: float = 0.0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class TaskConfig:
    """Configuration for cleanup tasks"""
    enabled: bool = True
    schedule: str = "0 2 * * *"  # Daily at 2 AM
    retention_days: int = 30
    batch_size: int = 1000
    max_execution_time: int = 3600  # 1 hour
    dry_run: bool = False
    priority: TaskPriority = TaskPriority.MEDIUM
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class BaseCleanupTask(ABC):
    """Abstract base class for all cleanup tasks"""
    
    def __init__(self, name: str, config: TaskConfig = None):
        self.name = name
        self.config = config or TaskConfig()
        self.logger = get_logger(f'housekeeping.{name}')
        self._is_running = False
        self._start_time = None
        self._last_run = None
        self._run_count = 0
    
    @abstractmethod
    async def _execute_cleanup(self) -> TaskResult:
        """
        Execute the cleanup operation
        
        Returns:
            TaskResult with cleanup statistics
        """
        pass
    
    @abstractmethod
    def get_task_description(self) -> str:
        """
        Get human-readable description of what this task does
        
        Returns:
            Description string
        """
        pass
    
    @abstractmethod
    async def estimate_cleanup_size(self) -> Dict[str, Any]:
        """
        Estimate the size of cleanup operation without executing it
        
        Returns:
            Dictionary with estimated items count, size, etc.
        """
        pass
    
    async def execute(self) -> TaskResult:
        """Execute the cleanup task with error handling and metrics"""
        if self._is_running:
            return TaskResult(
                status=TaskStatus.SKIPPED,
                error_message="Task is already running"
            )
        
        if not self.config.enabled:
            return TaskResult(
                status=TaskStatus.SKIPPED,
                error_message="Task is disabled"
            )
        
        self._is_running = True
        self._start_time = asyncio.get_event_loop().time()
        
        try:
            self.logger.info(f"Starting cleanup task: {self.name}")
            
            # Check dependencies
            dependency_check = await self._check_dependencies()
            if not dependency_check:
                return TaskResult(
                    status=TaskStatus.FAILED,
                    error_message="Dependency check failed"
                )
            
            # Execute with timeout
            result = await asyncio.wait_for(
                self._execute_cleanup(),
                timeout=self.config.max_execution_time
            )
            
            # Update execution metrics
            execution_time = asyncio.get_event_loop().time() - self._start_time
            result.execution_time = execution_time
            
            # Record metrics
            app_metrics.record_housekeeping_task(
                task_name=self.name,
                status=result.status,
                items_processed=result.items_processed,
                items_cleaned=result.items_cleaned,
                execution_time=execution_time
            )
            
            self._last_run = datetime.utcnow()
            self._run_count += 1
            
            if result.status == TaskStatus.COMPLETED:
                self.logger.info(
                    f"Cleanup task completed: {self.name}",
                    items_processed=result.items_processed,
                    items_cleaned=result.items_cleaned,
                    bytes_freed=result.bytes_freed,
                    execution_time=execution_time
                )
            else:
                self.logger.warning(
                    f"Cleanup task failed: {self.name} - {result.error_message}",
                    execution_time=execution_time
                )
            
            return result
            
        except asyncio.TimeoutError:
            execution_time = asyncio.get_event_loop().time() - self._start_time
            self.logger.error(f"Cleanup task timed out: {self.name}", execution_time=execution_time)
            
            return TaskResult(
                status=TaskStatus.FAILED,
                execution_time=execution_time,
                error_message=f"Task timed out after {self.config.max_execution_time}s"
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - self._start_time
            self.logger.error(f"Cleanup task error: {self.name} - {str(e)}", execution_time=execution_time)
            
            return TaskResult(
                status=TaskStatus.FAILED,
                execution_time=execution_time,
                error_message=str(e)
            )
            
        finally:
            self._is_running = False
    
    async def _check_dependencies(self) -> bool:
        """Check if task dependencies are satisfied"""
        if not self.config.dependencies:
            return True
        
        try:
            # This would check if dependent tasks have completed successfully
            # For now, return True as a placeholder
            return True
            
        except Exception as e:
            self.logger.error(f"Dependency check failed: {str(e)}")
            return False
    
    def _calculate_retention_cutoff(self, retention_days: Optional[int] = None) -> datetime:
        """Calculate cutoff date for retention"""
        days = retention_days or self.config.retention_days
        return datetime.utcnow() - timedelta(days=days)
    
    def _format_bytes(self, bytes_count: int) -> str:
        """Format bytes count in human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_count < 1024.0:
                return f"{bytes_count:.1f} {unit}"
            bytes_count /= 1024.0
        return f"{bytes_count:.1f} PB"
    
    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"
    
    async def dry_run(self) -> TaskResult:
        """Execute task in dry-run mode (no actual cleanup)"""
        original_dry_run = self.config.dry_run
        self.config.dry_run = True
        
        try:
            result = await self.execute()
            result.metadata['dry_run'] = True
            return result
        finally:
            self.config.dry_run = original_dry_run
    
    def get_task_info(self) -> Dict[str, Any]:
        """Get task information and status"""
        return {
            'name': self.name,
            'description': self.get_task_description(),
            'config': {
                'enabled': self.config.enabled,
                'schedule': self.config.schedule,
                'retention_days': self.config.retention_days,
                'batch_size': self.config.batch_size,
                'max_execution_time': self.config.max_execution_time,
                'priority': self.config.priority,
                'dependencies': self.config.dependencies
            },
            'status': {
                'is_running': self._is_running,
                'last_run': self._last_run.isoformat() if self._last_run else None,
                'run_count': self._run_count,
                'current_execution_time': (
                    asyncio.get_event_loop().time() - self._start_time
                    if self._is_running and self._start_time else None
                )
            }
        }
    
    def __str__(self):
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self):
        return f"{self.__class__.__name__}(name='{self.name}', enabled={self.config.enabled})"


class CleanupTaskRegistry:
    """Registry for managing cleanup tasks"""
    
    def __init__(self):
        self._tasks: Dict[str, BaseCleanupTask] = {}
        self.logger = get_logger('housekeeping.registry')
    
    def register(self, task: BaseCleanupTask):
        """Register a cleanup task"""
        self._tasks[task.name] = task
        self.logger.info(f"Registered cleanup task: {task.name}")
    
    def unregister(self, name: str):
        """Unregister a cleanup task"""
        if name in self._tasks:
            del self._tasks[name]
            self.logger.info(f"Unregistered cleanup task: {name}")
    
    def get_task(self, name: str) -> Optional[BaseCleanupTask]:
        """Get task by name"""
        return self._tasks.get(name)
    
    def get_all_tasks(self) -> List[BaseCleanupTask]:
        """Get all registered tasks"""
        return list(self._tasks.values())
    
    def get_enabled_tasks(self) -> List[BaseCleanupTask]:
        """Get all enabled tasks"""
        return [task for task in self._tasks.values() if task.config.enabled]
    
    def get_tasks_by_priority(self, priority: TaskPriority) -> List[BaseCleanupTask]:
        """Get tasks by priority level"""
        return [task for task in self._tasks.values() if task.config.priority == priority]
    
    async def execute_all_tasks(self, priority_filter: Optional[TaskPriority] = None) -> Dict[str, TaskResult]:
        """Execute all enabled tasks"""
        tasks = self.get_enabled_tasks()
        
        if priority_filter:
            tasks = [task for task in tasks if task.config.priority == priority_filter]
        
        # Sort by priority (critical first)
        priority_order = {
            TaskPriority.CRITICAL: 0,
            TaskPriority.HIGH: 1,
            TaskPriority.MEDIUM: 2,
            TaskPriority.LOW: 3
        }
        tasks.sort(key=lambda t: priority_order.get(t.config.priority, 999))
        
        results = {}
        
        for task in tasks:
            try:
                result = await task.execute()
                results[task.name] = result
                
                # Stop execution if a critical task fails
                if (task.config.priority == TaskPriority.CRITICAL and 
                    result.status == TaskStatus.FAILED):
                    self.logger.error(f"Critical task failed, stopping execution: {task.name}")
                    break
                    
            except Exception as e:
                self.logger.error(f"Task execution error: {task.name} - {str(e)}")
                results[task.name] = TaskResult(
                    status=TaskStatus.FAILED,
                    error_message=str(e)
                )
        
        return results
    
    async def get_cleanup_estimates(self) -> Dict[str, Dict[str, Any]]:
        """Get cleanup estimates for all tasks"""
        estimates = {}
        
        for task in self.get_enabled_tasks():
            try:
                estimate = await task.estimate_cleanup_size()
                estimates[task.name] = estimate
            except Exception as e:
                estimates[task.name] = {
                    'error': str(e),
                    'estimated_items': 0,
                    'estimated_size': 0
                }
        
        return estimates
    
    def get_registry_status(self) -> Dict[str, Any]:
        """Get registry status and statistics"""
        tasks = self.get_all_tasks()
        enabled_tasks = self.get_enabled_tasks()
        
        priority_counts = {}
        for priority in TaskPriority:
            priority_counts[priority.value] = len(self.get_tasks_by_priority(priority))
        
        return {
            'total_tasks': len(tasks),
            'enabled_tasks': len(enabled_tasks),
            'disabled_tasks': len(tasks) - len(enabled_tasks),
            'priority_breakdown': priority_counts,
            'tasks': [task.get_task_info() for task in tasks]
        }


# Global task registry
cleanup_task_registry = CleanupTaskRegistry()

"""
Cache and temporary file cleanup tasks
Handles cleanup of application caches, temporary files, and logs
"""
import os
import asyncio
from typing import Dict, Any, List
from datetime import datetime, timedelta
from pathlib import Path

from housekeeping.tasks.base_cleanup_task import Base<PERSON>leanupTask, TaskResult, TaskStatus, TaskConfig, TaskPriority
from database.models.analytics_model import AnalyticsRepository
from database.models.system_logs_model import SystemLogRepository
from monitoring.logger import get_logger


class LogCleanupTask(BaseCleanupTask):
    """Cleanup old system logs from database"""
    
    def __init__(self, config: TaskConfig = None):
        default_config = TaskConfig(
            enabled=True,
            schedule="0 1 * * *",  # Daily at 1 AM
            retention_days=30,  # Keep logs for 30 days
            batch_size=1000,
            priority=TaskPriority.MEDIUM
        )
        
        if config:
            # Merge with defaults
            for key, value in config.__dict__.items():
                if hasattr(default_config, key):
                    setattr(default_config, key, value)
        
        super().__init__("log_cleanup", default_config)
        self.log_repo = SystemLogRepository()
    
    def get_task_description(self) -> str:
        return f"Cleanup system logs older than {self.config.retention_days} days (keeps ERROR and CRITICAL logs longer)"
    
    async def estimate_cleanup_size(self) -> Dict[str, Any]:
        """Estimate number of logs to cleanup"""
        try:
            cutoff_date = self._calculate_retention_cutoff()
            
            # Get count of logs older than cutoff (excluding ERROR/CRITICAL)
            from database.connection import get_db_manager
            db = await get_db_manager()
            
            query = """
                SELECT COUNT(*) as count, 
                       COALESCE(SUM(LENGTH(message)), 0) as total_size
                FROM system_logs 
                WHERE timestamp < $1 
                AND level NOT IN ('ERROR', 'CRITICAL')
            """
            
            row = await db.fetchrow(query, cutoff_date)
            
            return {
                'estimated_items': row['count'] if row else 0,
                'estimated_size': row['total_size'] if row else 0,
                'cutoff_date': cutoff_date.isoformat(),
                'retention_days': self.config.retention_days
            }
            
        except Exception as e:
            self.logger.error(f"Failed to estimate log cleanup size: {str(e)}")
            return {
                'estimated_items': 0,
                'estimated_size': 0,
                'error': str(e)
            }
    
    async def _execute_cleanup(self) -> TaskResult:
        """Execute the log cleanup"""
        try:
            if not self.config.dry_run:
                # Perform actual cleanup
                deleted_count = await self.log_repo.cleanup_old_logs(self.config.retention_days)
                
                self.logger.info(f"Cleaned up {deleted_count} old log records")
                
                return TaskResult(
                    status=TaskStatus.COMPLETED,
                    items_processed=deleted_count,
                    items_cleaned=deleted_count,
                    bytes_freed=deleted_count * 256,  # Rough estimate per log
                    metadata={
                        'retention_days': self.config.retention_days
                    }
                )
            else:
                # Dry run - estimate only
                estimate = await self.estimate_cleanup_size()
                
                return TaskResult(
                    status=TaskStatus.COMPLETED,
                    items_processed=estimate['estimated_items'],
                    items_cleaned=estimate['estimated_items'],
                    bytes_freed=estimate['estimated_size'],
                    metadata={
                        'dry_run': True,
                        'retention_days': self.config.retention_days
                    }
                )
            
        except Exception as e:
            return TaskResult(
                status=TaskStatus.FAILED,
                error_message=str(e)
            )


class AnalyticsCleanupTask(BaseCleanupTask):
    """Cleanup old analytics data from database"""
    
    def __init__(self, config: TaskConfig = None):
        default_config = TaskConfig(
            enabled=True,
            schedule="0 2 * * 0",  # Weekly on Sunday at 2 AM
            retention_days=90,  # Keep analytics for 90 days
            batch_size=5000,
            priority=TaskPriority.LOW
        )
        
        if config:
            # Merge with defaults
            for key, value in config.__dict__.items():
                if hasattr(default_config, key):
                    setattr(default_config, key, value)
        
        super().__init__("analytics_cleanup", default_config)
        self.analytics_repo = AnalyticsRepository()
    
    def get_task_description(self) -> str:
        return f"Cleanup analytics data older than {self.config.retention_days} days"
    
    async def estimate_cleanup_size(self) -> Dict[str, Any]:
        """Estimate number of analytics records to cleanup"""
        try:
            cutoff_date = self._calculate_retention_cutoff()
            
            # Get count of analytics records older than cutoff
            from database.connection import get_db_manager
            db = await get_db_manager()
            
            query = """
                SELECT COUNT(*) as count,
                       COALESCE(SUM(LENGTH(event_data::text) + LENGTH(metrics::text)), 0) as total_size
                FROM analytics 
                WHERE timestamp < $1
            """
            
            row = await db.fetchrow(query, cutoff_date)
            
            return {
                'estimated_items': row['count'] if row else 0,
                'estimated_size': row['total_size'] if row else 0,
                'cutoff_date': cutoff_date.isoformat(),
                'retention_days': self.config.retention_days
            }
            
        except Exception as e:
            self.logger.error(f"Failed to estimate analytics cleanup size: {str(e)}")
            return {
                'estimated_items': 0,
                'estimated_size': 0,
                'error': str(e)
            }
    
    async def _execute_cleanup(self) -> TaskResult:
        """Execute the analytics cleanup"""
        try:
            if not self.config.dry_run:
                # Perform actual cleanup
                deleted_count = await self.analytics_repo.cleanup_old_analytics(self.config.retention_days)
                
                self.logger.info(f"Cleaned up {deleted_count} old analytics records")
                
                return TaskResult(
                    status=TaskStatus.COMPLETED,
                    items_processed=deleted_count,
                    items_cleaned=deleted_count,
                    bytes_freed=deleted_count * 512,  # Rough estimate per record
                    metadata={
                        'retention_days': self.config.retention_days
                    }
                )
            else:
                # Dry run - estimate only
                estimate = await self.estimate_cleanup_size()
                
                return TaskResult(
                    status=TaskStatus.COMPLETED,
                    items_processed=estimate['estimated_items'],
                    items_cleaned=estimate['estimated_items'],
                    bytes_freed=estimate['estimated_size'],
                    metadata={
                        'dry_run': True,
                        'retention_days': self.config.retention_days
                    }
                )
            
        except Exception as e:
            return TaskResult(
                status=TaskStatus.FAILED,
                error_message=str(e)
            )


class TempFileCleanupTask(BaseCleanupTask):
    """Cleanup temporary files and directories"""
    
    def __init__(self, config: TaskConfig = None):
        default_config = TaskConfig(
            enabled=True,
            schedule="0 */12 * * *",  # Every 12 hours
            retention_days=1,  # Keep temp files for 1 day
            batch_size=100,
            priority=TaskPriority.LOW
        )
        
        if config:
            # Merge with defaults
            for key, value in config.__dict__.items():
                if hasattr(default_config, key):
                    setattr(default_config, key, value)
        
        super().__init__("temp_file_cleanup", default_config)
        
        # Define temp directories to clean
        self.temp_directories = [
            "/tmp/trendsite",
            "./temp",
            "./cache",
            "./logs/temp",
            "./generator/temp",
            "./scraper/temp"
        ]
    
    def get_task_description(self) -> str:
        return f"Cleanup temporary files older than {self.config.retention_days} days from temp directories"
    
    async def estimate_cleanup_size(self) -> Dict[str, Any]:
        """Estimate size of temp files to cleanup"""
        try:
            cutoff_time = (datetime.utcnow() - timedelta(days=self.config.retention_days)).timestamp()
            
            total_files = 0
            total_size = 0
            
            for temp_dir in self.temp_directories:
                if os.path.exists(temp_dir):
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                stat = os.stat(file_path)
                                if stat.st_mtime < cutoff_time:
                                    total_files += 1
                                    total_size += stat.st_size
                            except (OSError, IOError):
                                continue
            
            return {
                'estimated_items': total_files,
                'estimated_size': total_size,
                'temp_directories': self.temp_directories,
                'retention_days': self.config.retention_days
            }
            
        except Exception as e:
            self.logger.error(f"Failed to estimate temp file cleanup size: {str(e)}")
            return {
                'estimated_items': 0,
                'estimated_size': 0,
                'error': str(e)
            }
    
    async def _execute_cleanup(self) -> TaskResult:
        """Execute the temp file cleanup"""
        try:
            cutoff_time = (datetime.utcnow() - timedelta(days=self.config.retention_days)).timestamp()
            
            files_processed = 0
            files_cleaned = 0
            bytes_freed = 0
            errors = []
            
            for temp_dir in self.temp_directories:
                if not os.path.exists(temp_dir):
                    continue
                
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        files_processed += 1
                        
                        try:
                            stat = os.stat(file_path)
                            if stat.st_mtime < cutoff_time:
                                file_size = stat.st_size
                                
                                if not self.config.dry_run:
                                    os.remove(file_path)
                                
                                files_cleaned += 1
                                bytes_freed += file_size
                                
                                self.logger.debug(
                                    f"{'Would remove' if self.config.dry_run else 'Removed'} temp file: {file_path}"
                                )
                        
                        except (OSError, IOError) as e:
                            error_msg = f"Failed to process {file_path}: {str(e)}"
                            errors.append(error_msg)
                            self.logger.warning(error_msg)
                        
                        # Process in batches
                        if files_processed % self.config.batch_size == 0:
                            await asyncio.sleep(0.01)  # Brief pause
            
            # Clean up empty directories
            if not self.config.dry_run:
                for temp_dir in self.temp_directories:
                    if os.path.exists(temp_dir):
                        try:
                            # Remove empty subdirectories
                            for root, dirs, files in os.walk(temp_dir, topdown=False):
                                for dir_name in dirs:
                                    dir_path = os.path.join(root, dir_name)
                                    try:
                                        if not os.listdir(dir_path):  # Directory is empty
                                            os.rmdir(dir_path)
                                            self.logger.debug(f"Removed empty directory: {dir_path}")
                                    except (OSError, IOError):
                                        pass
                        except Exception as e:
                            self.logger.warning(f"Failed to clean empty directories in {temp_dir}: {str(e)}")
            
            return TaskResult(
                status=TaskStatus.COMPLETED,
                items_processed=files_processed,
                items_cleaned=files_cleaned,
                bytes_freed=bytes_freed,
                metadata={
                    'temp_directories': self.temp_directories,
                    'retention_days': self.config.retention_days,
                    'errors': errors[:10],  # Keep only first 10 errors
                    'error_count': len(errors)
                }
            )
            
        except Exception as e:
            return TaskResult(
                status=TaskStatus.FAILED,
                error_message=str(e)
            )


class CacheRefreshTask(BaseCleanupTask):
    """Refresh materialized views and update caches"""
    
    def __init__(self, config: TaskConfig = None):
        default_config = TaskConfig(
            enabled=True,
            schedule="0 */4 * * *",  # Every 4 hours
            retention_days=0,  # Not applicable for refresh
            batch_size=1,
            priority=TaskPriority.MEDIUM
        )
        
        if config:
            # Merge with defaults
            for key, value in config.__dict__.items():
                if hasattr(default_config, key):
                    setattr(default_config, key, value)
        
        super().__init__("cache_refresh", default_config)
    
    def get_task_description(self) -> str:
        return "Refresh materialized views and update application caches"
    
    async def estimate_cleanup_size(self) -> Dict[str, Any]:
        """Estimate cache refresh operations"""
        return {
            'estimated_items': 3,  # Number of materialized views to refresh
            'estimated_size': 0,  # Refresh doesn't free space
            'operations': ['daily_analytics_summary', 'hourly_log_summary', 'application_cache']
        }
    
    async def _execute_cleanup(self) -> TaskResult:
        """Execute the cache refresh"""
        try:
            operations_completed = 0
            errors = []
            
            if not self.config.dry_run:
                from database.connection import get_db_manager
                db = await get_db_manager()
                
                # Refresh analytics summary
                try:
                    await db.execute("REFRESH MATERIALIZED VIEW CONCURRENTLY daily_analytics_summary")
                    operations_completed += 1
                    self.logger.info("Refreshed daily_analytics_summary materialized view")
                except Exception as e:
                    errors.append(f"Failed to refresh daily_analytics_summary: {str(e)}")
                
                # Refresh log summary
                try:
                    await db.execute("REFRESH MATERIALIZED VIEW CONCURRENTLY hourly_log_summary")
                    operations_completed += 1
                    self.logger.info("Refreshed hourly_log_summary materialized view")
                except Exception as e:
                    errors.append(f"Failed to refresh hourly_log_summary: {str(e)}")
                
                # Clear application cache (if implemented)
                try:
                    # This would clear Redis cache or other application caches
                    operations_completed += 1
                    self.logger.info("Refreshed application cache")
                except Exception as e:
                    errors.append(f"Failed to refresh application cache: {str(e)}")
            else:
                operations_completed = 3  # Dry run
            
            return TaskResult(
                status=TaskStatus.COMPLETED,
                items_processed=3,
                items_cleaned=operations_completed,
                bytes_freed=0,
                metadata={
                    'operations_completed': operations_completed,
                    'errors': errors,
                    'error_count': len(errors)
                }
            )
            
        except Exception as e:
            return TaskResult(
                status=TaskStatus.FAILED,
                error_message=str(e)
            )

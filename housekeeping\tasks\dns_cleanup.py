"""
DNS cleanup tasks for housekeeping operations
Handles cleanup of orphaned DNS records and analytics data
"""
import asyncio
from typing import Dict, Any, List
from datetime import datetime, timedelta

from housekeeping.tasks.base_cleanup_task import Base<PERSON>leanupTask, TaskResult, TaskStatus, TaskConfig, TaskPriority
from database.models.dns_model import DNSRepository, DNSStatus
from database.models.deployment_model import DeploymentRepository, DeploymentStatus
from dns.dns_manager import DNSManager
from dns.kv_store import get_kv_store_manager
from monitoring.logger import get_logger


class OrphanedDNSCleanupTask(BaseCleanupTask):
    """Cleanup orphaned DNS records that no longer have active deployments"""
    
    def __init__(self, config: TaskConfig = None):
        default_config = TaskConfig(
            enabled=True,
            schedule="0 3 * * *",  # Daily at 3 AM
            retention_days=7,  # Keep records for 7 days after deployment ends
            batch_size=50,
            priority=TaskPriority.MEDIUM
        )
        
        if config:
            # Merge with defaults
            for key, value in config.__dict__.items():
                if hasattr(default_config, key):
                    setattr(default_config, key, value)
        
        super().__init__("orphaned_dns_cleanup", default_config)
        self.dns_repo = DNSRepository()
        self.deployment_repo = DeploymentRepository()
        self.dns_manager = DNSManager()
    
    def get_task_description(self) -> str:
        return "Cleanup DNS records for deployments that have been completed or failed for more than the retention period"
    
    async def estimate_cleanup_size(self) -> Dict[str, Any]:
        """Estimate number of orphaned DNS records"""
        try:
            cutoff_date = self._calculate_retention_cutoff()
            
            # Get all active DNS records
            active_dns_records = await self.dns_repo.get_active_records()
            
            orphaned_count = 0
            
            for dns_record in active_dns_records:
                # Check if associated deployment is completed/failed and old enough
                if dns_record.trend_id:
                    deployments = await self.deployment_repo.get_by_trend_id(dns_record.trend_id)
                    
                    if deployments:
                        latest_deployment = max(deployments, key=lambda d: d.created_at)
                        
                        # Check if deployment is completed/failed and old enough
                        if (latest_deployment.status in [DeploymentStatus.COMPLETED, DeploymentStatus.FAILED, DeploymentStatus.CANCELLED] and
                            latest_deployment.completed_at and
                            latest_deployment.completed_at < cutoff_date):
                            orphaned_count += 1
                    else:
                        # No deployments found for this DNS record
                        orphaned_count += 1
            
            return {
                'estimated_items': orphaned_count,
                'estimated_size': orphaned_count * 1024,  # Rough estimate
                'total_dns_records': len(active_dns_records),
                'cutoff_date': cutoff_date.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to estimate cleanup size: {str(e)}")
            return {
                'estimated_items': 0,
                'estimated_size': 0,
                'error': str(e)
            }
    
    async def _execute_cleanup(self) -> TaskResult:
        """Execute the orphaned DNS cleanup"""
        try:
            cutoff_date = self._calculate_retention_cutoff()
            
            # Get all active DNS records
            active_dns_records = await self.dns_repo.get_active_records()
            
            items_processed = 0
            items_cleaned = 0
            errors = []
            
            for dns_record in active_dns_records:
                items_processed += 1
                
                try:
                    should_cleanup = False
                    
                    # Check if associated deployment is completed/failed and old enough
                    if dns_record.trend_id:
                        deployments = await self.deployment_repo.get_by_trend_id(dns_record.trend_id)
                        
                        if deployments:
                            latest_deployment = max(deployments, key=lambda d: d.created_at)
                            
                            # Check if deployment is completed/failed and old enough
                            if (latest_deployment.status in [DeploymentStatus.COMPLETED, DeploymentStatus.FAILED, DeploymentStatus.CANCELLED] and
                                latest_deployment.completed_at and
                                latest_deployment.completed_at < cutoff_date):
                                should_cleanup = True
                        else:
                            # No deployments found for this DNS record
                            should_cleanup = True
                    
                    if should_cleanup:
                        if not self.config.dry_run:
                            # Delete from Cloudflare if record ID exists
                            if dns_record.cloudflare_record_id:
                                delete_result = await self.dns_manager.delete_dns_record(dns_record.cloudflare_record_id)
                                if not delete_result['success']:
                                    self.logger.warning(
                                        f"Failed to delete DNS record from Cloudflare: {delete_result.get('error')}",
                                        record_id=dns_record.id
                                    )
                            
                            # Update database record status
                            await self.dns_repo.update(dns_record.id, {
                                'status': DNSStatus.DELETED,
                                'updated_at': datetime.utcnow()
                            })
                        
                        items_cleaned += 1
                        
                        self.logger.info(
                            f"{'Would cleanup' if self.config.dry_run else 'Cleaned up'} orphaned DNS record: {dns_record.subdomain}",
                            record_id=dns_record.id,
                            trend_id=dns_record.trend_id
                        )
                
                except Exception as e:
                    error_msg = f"Failed to cleanup DNS record {dns_record.id}: {str(e)}"
                    errors.append(error_msg)
                    self.logger.error(error_msg)
                
                # Process in batches
                if items_processed % self.config.batch_size == 0:
                    await asyncio.sleep(0.1)  # Brief pause between batches
            
            return TaskResult(
                status=TaskStatus.COMPLETED,
                items_processed=items_processed,
                items_cleaned=items_cleaned,
                bytes_freed=items_cleaned * 1024,  # Rough estimate
                metadata={
                    'cutoff_date': cutoff_date.isoformat(),
                    'errors': errors[:10],  # Keep only first 10 errors
                    'error_count': len(errors)
                }
            )
            
        except Exception as e:
            return TaskResult(
                status=TaskStatus.FAILED,
                error_message=str(e)
            )


class DNSAnalyticsCleanupTask(BaseCleanupTask):
    """Cleanup old DNS analytics data from KV store"""
    
    def __init__(self, config: TaskConfig = None):
        default_config = TaskConfig(
            enabled=True,
            schedule="0 4 * * *",  # Daily at 4 AM
            retention_days=30,  # Keep analytics for 30 days
            batch_size=1000,
            priority=TaskPriority.LOW
        )
        
        if config:
            # Merge with defaults
            for key, value in config.__dict__.items():
                if hasattr(default_config, key):
                    setattr(default_config, key, value)
        
        super().__init__("dns_analytics_cleanup", default_config)
        
        # Initialize KV store manager (would need proper config)
        try:
            from shared.config import settings
            kv_config = {
                'account_id': settings.cloudflare_account_id,
                'api_token': settings.cloudflare_api_token,
                'redirects_namespace_id': settings.cloudflare_redirects_namespace_id,
                'analytics_namespace_id': settings.cloudflare_analytics_namespace_id
            }
            self.kv_manager = get_kv_store_manager(kv_config)
        except Exception as e:
            self.logger.warning(f"KV store not configured: {str(e)}")
            self.kv_manager = None
    
    def get_task_description(self) -> str:
        return f"Cleanup DNS analytics data older than {self.config.retention_days} days from Cloudflare KV store"
    
    async def estimate_cleanup_size(self) -> Dict[str, Any]:
        """Estimate size of analytics data to cleanup"""
        try:
            if not self.kv_manager:
                return {
                    'estimated_items': 0,
                    'estimated_size': 0,
                    'error': 'KV store not configured'
                }
            
            # Get KV stats
            kv_stats = await self.kv_manager.kv_client.get_kv_stats()
            
            analytics_namespace = kv_stats.get('analytics_namespace', {})
            total_keys = analytics_namespace.get('key_count', 0)
            
            # Estimate that about 20% of keys are older than retention period
            estimated_old_keys = int(total_keys * 0.2)
            
            return {
                'estimated_items': estimated_old_keys,
                'estimated_size': estimated_old_keys * 512,  # Rough estimate per record
                'total_analytics_keys': total_keys,
                'retention_days': self.config.retention_days
            }
            
        except Exception as e:
            self.logger.error(f"Failed to estimate analytics cleanup size: {str(e)}")
            return {
                'estimated_items': 0,
                'estimated_size': 0,
                'error': str(e)
            }
    
    async def _execute_cleanup(self) -> TaskResult:
        """Execute the DNS analytics cleanup"""
        try:
            if not self.kv_manager:
                return TaskResult(
                    status=TaskStatus.SKIPPED,
                    error_message="KV store not configured"
                )
            
            if not self.config.dry_run:
                # Perform actual cleanup
                deleted_count = await self.kv_manager.kv_client.cleanup_old_analytics(self.config.retention_days)
                
                self.logger.info(f"Cleaned up {deleted_count} old analytics records")
                
                return TaskResult(
                    status=TaskStatus.COMPLETED,
                    items_processed=deleted_count,
                    items_cleaned=deleted_count,
                    bytes_freed=deleted_count * 512,  # Rough estimate
                    metadata={
                        'retention_days': self.config.retention_days
                    }
                )
            else:
                # Dry run - estimate only
                estimate = await self.estimate_cleanup_size()
                
                return TaskResult(
                    status=TaskStatus.COMPLETED,
                    items_processed=estimate['estimated_items'],
                    items_cleaned=estimate['estimated_items'],
                    bytes_freed=estimate['estimated_size'],
                    metadata={
                        'dry_run': True,
                        'retention_days': self.config.retention_days
                    }
                )
            
        except Exception as e:
            return TaskResult(
                status=TaskStatus.FAILED,
                error_message=str(e)
            )


class DNSSyncTask(BaseCleanupTask):
    """Sync DNS records between database and Cloudflare"""
    
    def __init__(self, config: TaskConfig = None):
        default_config = TaskConfig(
            enabled=True,
            schedule="0 */6 * * *",  # Every 6 hours
            retention_days=0,  # Not applicable for sync
            batch_size=100,
            priority=TaskPriority.HIGH
        )
        
        if config:
            # Merge with defaults
            for key, value in config.__dict__.items():
                if hasattr(default_config, key):
                    setattr(default_config, key, value)
        
        super().__init__("dns_sync", default_config)
        self.dns_manager = DNSManager()
    
    def get_task_description(self) -> str:
        return "Sync DNS records between database and Cloudflare to identify and resolve inconsistencies"
    
    async def estimate_cleanup_size(self) -> Dict[str, Any]:
        """Estimate sync operation size"""
        try:
            # Get sync status without making changes
            sync_result = await self.dns_manager.sync_dns_records()
            
            return {
                'estimated_items': sync_result.get('orphaned_cf', 0) + sync_result.get('orphaned_db', 0),
                'estimated_size': 0,  # Sync doesn't free space
                'cloudflare_records': sync_result.get('cloudflare_records', 0),
                'database_records': sync_result.get('database_records', 0),
                'orphaned_cf': sync_result.get('orphaned_cf', 0),
                'orphaned_db': sync_result.get('orphaned_db', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to estimate sync size: {str(e)}")
            return {
                'estimated_items': 0,
                'estimated_size': 0,
                'error': str(e)
            }
    
    async def _execute_cleanup(self) -> TaskResult:
        """Execute the DNS sync operation"""
        try:
            # Perform DNS sync
            sync_result = await self.dns_manager.sync_dns_records()
            
            items_processed = sync_result.get('cloudflare_records', 0) + sync_result.get('database_records', 0)
            items_synced = sync_result.get('synced', 0)
            
            self.logger.info(f"DNS sync completed: {sync_result}")
            
            return TaskResult(
                status=TaskStatus.COMPLETED,
                items_processed=items_processed,
                items_cleaned=items_synced,
                bytes_freed=0,  # Sync doesn't free space
                metadata=sync_result
            )
            
        except Exception as e:
            return TaskResult(
                status=TaskStatus.FAILED,
                error_message=str(e)
            )

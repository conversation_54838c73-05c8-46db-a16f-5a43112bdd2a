version: '3.8'

services:
  # Main API Application
  api:
    build:
      context: ..
      dockerfile: infrastructure/Dockerfile.api
    container_name: trend-platform-api
    restart: unless-stopped
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      - VAULT_URL=${VAULT_URL}
      - VAULT_TOKEN=${VAULT_TOKEN}
    ports:
      - "8000:8000"
    depends_on:
      - redis
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
      - ../shared:/app/shared:ro
    networks:
      - app_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Dashboard Application
  dashboard:
    build:
      context: ..
      dockerfile: infrastructure/Dockerfile.dashboard
    container_name: trend-platform-dashboard
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${JWT_SECRET}
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    ports:
      - "3000:3000"
    depends_on:
      - api
    volumes:
      - dashboard_data:/app/.next
    networks:
      - app_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    container_name: trend-platform-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for Background Tasks
  celery_worker:
    build:
      context: ..
      dockerfile: infrastructure/Dockerfile.api
    container_name: trend-platform-worker
    restart: unless-stopped
    command: celery -A shared.celery_app worker --loglevel=info --concurrency=4
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${REDIS_URL}
      - CELERY_RESULT_BACKEND=${REDIS_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      - VAULT_URL=${VAULT_URL}
      - VAULT_TOKEN=${VAULT_TOKEN}
    depends_on:
      - redis
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
      - ../shared:/app/shared:ro
    networks:
      - app_network

  # Celery Beat Scheduler
  celery_beat:
    build:
      context: ..
      dockerfile: infrastructure/Dockerfile.api
    container_name: trend-platform-scheduler
    restart: unless-stopped
    command: celery -A shared.celery_app beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${REDIS_URL}
      - CELERY_RESULT_BACKEND=${REDIS_URL}
    depends_on:
      - redis
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
      - ../shared:/app/shared:ro
    networks:
      - app_network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: trend-platform-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - app_network

  # Grafana Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: trend-platform-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - app_network
    depends_on:
      - prometheus

  # Loki Log Aggregation
  loki:
    image: grafana/loki:latest
    container_name: trend-platform-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - app_network

  # Promtail Log Collector
  promtail:
    image: grafana/promtail:latest
    container_name: trend-platform-promtail
    restart: unless-stopped
    volumes:
      - ./monitoring/promtail-config.yml:/etc/promtail/config.yml:ro
      - app_logs:/var/log/app:ro
      - /var/log:/var/log/host:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - app_network
    depends_on:
      - loki

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: trend-platform-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
      - dashboard
    networks:
      - app_network

  # Vault for Secret Management (Development only)
  vault:
    image: vault:latest
    container_name: trend-platform-vault
    restart: unless-stopped
    ports:
      - "8200:8200"
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=${VAULT_TOKEN:-myroot}
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
    cap_add:
      - IPC_LOCK
    volumes:
      - vault_data:/vault/data
    networks:
      - app_network
    profiles:
      - development

volumes:
  app_data:
    driver: local
  app_logs:
    driver: local
  dashboard_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  vault_data:
    driver: local

networks:
  app_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

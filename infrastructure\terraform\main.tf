# Main Terraform configuration for Trend Platform Infrastructure
terraform {
  required_version = ">= 1.0"
  
  required_providers {
    oci = {
      source  = "oracle/oci"
      version = "~> 5.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
  
  backend "s3" {
    bucket = "trend-platform-terraform-state"
    key    = "infrastructure/terraform.tfstate"
    region = "us-east-1"
  }
}

# Local values
locals {
  common_tags = {
    Environment = var.environment
    Project     = "trend-platform"
    ManagedBy   = "terraform"
    CreatedAt   = timestamp()
  }
}

# Data sources
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_id
}

data "oci_core_images" "ubuntu_images" {
  compartment_id           = var.compartment_id
  operating_system         = "Canonical Ubuntu"
  operating_system_version = "22.04"
  shape                    = "VM.Standard.A1.Flex"
  sort_by                  = "TIMECREATED"
  sort_order               = "DESC"
}

# Virtual Cloud Network
resource "oci_core_vcn" "trend_platform_vcn" {
  compartment_id = var.compartment_id
  cidr_blocks    = ["10.0.0.0/16"]
  display_name   = "trend-platform-vcn"
  dns_label      = "trendplatform"
  
  tags = local.common_tags
}

# Internet Gateway
resource "oci_core_internet_gateway" "trend_platform_igw" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "trend-platform-igw"
  enabled        = true
  
  tags = local.common_tags
}

# Public Subnet
resource "oci_core_subnet" "public_subnet" {
  compartment_id      = var.compartment_id
  vcn_id              = oci_core_vcn.trend_platform_vcn.id
  cidr_block          = "10.0.1.0/24"
  display_name        = "public-subnet"
  dns_label           = "public"
  route_table_id      = oci_core_route_table.public_route_table.id
  security_list_ids   = [oci_core_security_list.public_security_list.id]
  
  tags = local.common_tags
}

# Route Table for Public Subnet
resource "oci_core_route_table" "public_route_table" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "public-route-table"
  
  route_rules {
    destination       = "0.0.0.0/0"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_internet_gateway.trend_platform_igw.id
  }
  
  tags = local.common_tags
}

# Security List for Public Subnet
resource "oci_core_security_list" "public_security_list" {
  compartment_id = var.compartment_id
  vcn_id         = oci_core_vcn.trend_platform_vcn.id
  display_name   = "public-security-list"
  
  # HTTP
  ingress_security_rules {
    protocol = "6"
    source   = "0.0.0.0/0"
    tcp_options {
      min = 80
      max = 80
    }
  }
  
  # HTTPS
  ingress_security_rules {
    protocol = "6"
    source   = "0.0.0.0/0"
    tcp_options {
      min = 443
      max = 443
    }
  }
  
  # SSH
  ingress_security_rules {
    protocol = "6"
    source   = var.admin_cidr_blocks[0]
    tcp_options {
      min = 22
      max = 22
    }
  }
  
  # Application ports
  ingress_security_rules {
    protocol = "6"
    source   = "0.0.0.0/0"
    tcp_options {
      min = 3000
      max = 3000
    }
  }
  
  # API port
  ingress_security_rules {
    protocol = "6"
    source   = "0.0.0.0/0"
    tcp_options {
      min = 8000
      max = 8000
    }
  }
  
  # Monitoring ports
  ingress_security_rules {
    protocol = "6"
    source   = "10.0.0.0/16"
    tcp_options {
      min = 9090
      max = 9090
    }
  }
  
  # All outbound traffic
  egress_security_rules {
    protocol    = "all"
    destination = "0.0.0.0/0"
  }
  
  tags = local.common_tags
}

# Main Application Server
resource "oci_core_instance" "app_server" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_id
  display_name        = "trend-platform-app-server"
  shape               = "VM.Standard.A1.Flex"
  
  shape_config {
    ocpus         = 4
    memory_in_gbs = 24
  }
  
  create_vnic_details {
    subnet_id                 = oci_core_subnet.public_subnet.id
    display_name              = "app-server-vnic"
    assign_public_ip          = true
    assign_private_dns_record = true
    hostname_label            = "app-server"
  }
  
  source_details {
    source_type = "image"
    source_id   = data.oci_core_images.ubuntu_images.images[0].id
    boot_volume_size_in_gbs = 100
  }
  
  metadata = {
    ssh_authorized_keys = file(var.ssh_public_key_path)
    user_data = base64encode(templatefile("${path.module}/scripts/app-server-init.sh", {
      environment = var.environment
    }))
  }
  
  tags = merge(local.common_tags, {
    Role = "application-server"
  })
}

# Load Balancer
resource "oci_load_balancer_load_balancer" "app_load_balancer" {
  compartment_id = var.compartment_id
  display_name   = "trend-platform-lb"
  shape          = "flexible"
  
  shape_details {
    minimum_bandwidth_in_mbps = 10
    maximum_bandwidth_in_mbps = 100
  }
  
  subnet_ids = [oci_core_subnet.public_subnet.id]
  is_private = false
  
  tags = local.common_tags
}

# Backend Set for Load Balancer
resource "oci_load_balancer_backend_set" "app_backend_set" {
  name             = "app-backend-set"
  load_balancer_id = oci_load_balancer_load_balancer.app_load_balancer.id
  policy           = "ROUND_ROBIN"
  
  health_checker {
    port                = "8000"
    protocol            = "HTTP"
    response_body_regex = ".*"
    url_path            = "/api/health"
    interval_ms         = 10000
    timeout_in_millis   = 3000
    retries             = 3
  }
}

# Backend for Application Server
resource "oci_load_balancer_backend" "app_backend" {
  load_balancer_id = oci_load_balancer_load_balancer.app_load_balancer.id
  backendset_name  = oci_load_balancer_backend_set.app_backend_set.name
  ip_address       = oci_core_instance.app_server.private_ip
  port             = 8000
  backup           = false
  drain            = false
  offline          = false
  weight           = 1
}

# Listener for Load Balancer
resource "oci_load_balancer_listener" "app_listener" {
  load_balancer_id         = oci_load_balancer_load_balancer.app_load_balancer.id
  name                     = "app-listener"
  default_backend_set_name = oci_load_balancer_backend_set.app_backend_set.name
  port                     = 80
  protocol                 = "HTTP"
}

# Block Storage for Application Data
resource "oci_core_volume" "app_data_volume" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_id
  display_name        = "app-data-volume"
  size_in_gbs         = 200
  
  tags = local.common_tags
}

# Attach Block Storage to App Server
resource "oci_core_volume_attachment" "app_data_attachment" {
  attachment_type = "iscsi"
  instance_id     = oci_core_instance.app_server.id
  volume_id       = oci_core_volume.app_data_volume.id
  display_name    = "app-data-attachment"
}

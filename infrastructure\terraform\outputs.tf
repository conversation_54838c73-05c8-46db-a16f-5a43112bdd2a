# Terraform outputs for infrastructure resources

output "vcn_id" {
  description = "ID of the Virtual Cloud Network"
  value       = oci_core_vcn.trend_platform_vcn.id
}

output "public_subnet_id" {
  description = "ID of the public subnet"
  value       = oci_core_subnet.public_subnet.id
}

output "app_server_id" {
  description = "ID of the application server instance"
  value       = oci_core_instance.app_server.id
}

output "app_server_public_ip" {
  description = "Public IP address of the application server"
  value       = oci_core_instance.app_server.public_ip
}

output "app_server_private_ip" {
  description = "Private IP address of the application server"
  value       = oci_core_instance.app_server.private_ip
}

output "load_balancer_id" {
  description = "ID of the load balancer"
  value       = oci_load_balancer_load_balancer.app_load_balancer.id
}

output "load_balancer_ip" {
  description = "IP address of the load balancer"
  value       = oci_load_balancer_load_balancer.app_load_balancer.ip_address_details[0].ip_address
}

output "app_data_volume_id" {
  description = "ID of the application data volume"
  value       = oci_core_volume.app_data_volume.id
}

output "ssh_connection_command" {
  description = "SSH command to connect to the application server"
  value       = "ssh -i ~/.ssh/id_rsa ubuntu@${oci_core_instance.app_server.public_ip}"
}

output "application_urls" {
  description = "URLs for accessing the application"
  value = {
    load_balancer = "http://${oci_load_balancer_load_balancer.app_load_balancer.ip_address_details[0].ip_address}"
    direct_server = "http://${oci_core_instance.app_server.public_ip}:8000"
    dashboard     = "http://${oci_core_instance.app_server.public_ip}:3000"
    monitoring    = "http://${oci_core_instance.app_server.public_ip}:9090"
  }
}

output "infrastructure_summary" {
  description = "Summary of deployed infrastructure"
  value = {
    environment           = var.environment
    region               = var.oci_region
    app_server_shape     = var.app_instance_shape
    app_server_ocpus     = var.app_instance_ocpus
    app_server_memory_gb = var.app_instance_memory_gb
    load_balancer_shape  = var.load_balancer_shape
    monitoring_enabled   = var.enable_monitoring
    backup_enabled       = var.enable_backup
  }
}

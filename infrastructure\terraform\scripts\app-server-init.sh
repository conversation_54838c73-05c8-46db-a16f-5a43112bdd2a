#!/bin/bash
# Application server initialization script
# This script sets up the basic environment for the Trend Platform

set -e

# Variables
ENVIRONMENT="${environment}"
LOG_FILE="/var/log/app-server-init.log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log "Starting application server initialization for environment: $ENVIRONMENT"

# Update system packages
log "Updating system packages..."
apt-get update -y
apt-get upgrade -y

# Install essential packages
log "Installing essential packages..."
apt-get install -y \
    curl \
    wget \
    git \
    htop \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    jq \
    vim \
    fail2ban \
    ufw

# Install Docker
log "Installing Docker..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt-get update -y
apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Start and enable Docker
systemctl start docker
systemctl enable docker

# Add ubuntu user to docker group
usermod -aG docker ubuntu

# Install Docker Compose
log "Installing Docker Compose..."
DOCKER_COMPOSE_VERSION="v2.21.0"
curl -L "https://github.com/docker/compose/releases/download/$DOCKER_COMPOSE_VERSION/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose

# Install Node.js
log "Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Install Python and pip
log "Installing Python and pip..."
apt-get install -y python3 python3-pip python3-venv python3-dev

# Install Nginx
log "Installing Nginx..."
apt-get install -y nginx

# Configure firewall
log "Configuring firewall..."
ufw --force enable
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3000/tcp
ufw allow 8000/tcp
ufw allow from 10.0.0.0/16 to any port 9090

# Configure fail2ban
log "Configuring fail2ban..."
systemctl start fail2ban
systemctl enable fail2ban

# Create application directory
log "Creating application directory..."
mkdir -p /opt/trend-platform
chown ubuntu:ubuntu /opt/trend-platform

# Create data directory
log "Creating data directory..."
mkdir -p /opt/trend-platform/data
mkdir -p /opt/trend-platform/logs
mkdir -p /opt/trend-platform/backups
chown -R ubuntu:ubuntu /opt/trend-platform

# Install monitoring tools
log "Installing monitoring tools..."
# Prometheus Node Exporter
NODE_EXPORTER_VERSION="1.6.1"
wget https://github.com/prometheus/node_exporter/releases/download/v$NODE_EXPORTER_VERSION/node_exporter-$NODE_EXPORTER_VERSION.linux-amd64.tar.gz
tar xvfz node_exporter-$NODE_EXPORTER_VERSION.linux-amd64.tar.gz
mv node_exporter-$NODE_EXPORTER_VERSION.linux-amd64/node_exporter /usr/local/bin/
rm -rf node_exporter-$NODE_EXPORTER_VERSION.linux-amd64*

# Create node_exporter user
useradd --no-create-home --shell /bin/false node_exporter

# Create systemd service for node_exporter
cat > /etc/systemd/system/node_exporter.service << EOF
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl start node_exporter
systemctl enable node_exporter

# Configure log rotation
log "Configuring log rotation..."
cat > /etc/logrotate.d/trend-platform << EOF
/opt/trend-platform/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        systemctl reload rsyslog > /dev/null 2>&1 || true
    endscript
}
EOF

# Set up basic Nginx configuration
log "Configuring Nginx..."
cat > /etc/nginx/sites-available/default << EOF
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    
    server_name _;
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Dashboard proxy
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# Test and reload Nginx
nginx -t
systemctl restart nginx
systemctl enable nginx

# Create environment file template
log "Creating environment file template..."
cat > /opt/trend-platform/.env.template << EOF
# Environment Configuration
ENVIRONMENT=$ENVIRONMENT
DEBUG=false

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/trenddb
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Security Configuration
JWT_SECRET=your-secret-key-change-in-production
VAULT_URL=http://localhost:8200
VAULT_TOKEN=
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# API Keys
OPENAI_API_KEY=
CLOUDFLARE_API_TOKEN=
GITHUB_TOKEN=

# External Services
COOLIFY_API_URL=
COOLIFY_API_TOKEN=
COOLIFY_TEAM_ID=

# Monitoring
LOKI_URL=http://localhost:3100
PROMETHEUS_URL=http://localhost:9090
GRAFANA_URL=http://localhost:3000
EOF

chown ubuntu:ubuntu /opt/trend-platform/.env.template

# Create systemd service for the application
log "Creating systemd service..."
cat > /etc/systemd/system/trend-platform.service << EOF
[Unit]
Description=Trend Platform Application
After=network.target docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/trend-platform
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
User=ubuntu
Group=ubuntu

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload

# Set up automatic security updates
log "Configuring automatic security updates..."
apt-get install -y unattended-upgrades
echo 'Unattended-Upgrade::Automatic-Reboot "false";' >> /etc/apt/apt.conf.d/50unattended-upgrades

# Create backup script
log "Creating backup script..."
cat > /opt/trend-platform/backup.sh << 'EOF'
#!/bin/bash
# Backup script for Trend Platform

BACKUP_DIR="/opt/trend-platform/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup_$DATE.tar.gz"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Create backup
tar -czf "$BACKUP_FILE" \
    --exclude="$BACKUP_DIR" \
    --exclude="/opt/trend-platform/logs" \
    /opt/trend-platform

# Keep only last 7 backups
find "$BACKUP_DIR" -name "backup_*.tar.gz" -type f -mtime +7 -delete

echo "Backup created: $BACKUP_FILE"
EOF

chmod +x /opt/trend-platform/backup.sh
chown ubuntu:ubuntu /opt/trend-platform/backup.sh

# Set up cron job for backups
log "Setting up backup cron job..."
(crontab -u ubuntu -l 2>/dev/null; echo "0 2 * * * /opt/trend-platform/backup.sh >> /opt/trend-platform/logs/backup.log 2>&1") | crontab -u ubuntu -

# Final system configuration
log "Performing final system configuration..."
# Increase file limits
echo "ubuntu soft nofile 65536" >> /etc/security/limits.conf
echo "ubuntu hard nofile 65536" >> /etc/security/limits.conf

# Configure kernel parameters
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p

log "Application server initialization completed successfully!"
log "Next steps:"
log "1. Copy your application code to /opt/trend-platform/"
log "2. Configure environment variables in /opt/trend-platform/.env"
log "3. Start the application with: systemctl start trend-platform"

# Create completion marker
touch /opt/trend-platform/.init-complete
chown ubuntu:ubuntu /opt/trend-platform/.init-complete

log "Initialization script finished. Server is ready for application deployment."

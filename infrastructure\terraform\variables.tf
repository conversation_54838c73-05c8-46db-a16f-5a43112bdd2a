# Terraform variables for infrastructure configuration

variable "environment" {
  description = "Environment name (development, staging, production)"
  type        = string
  default     = "production"
  
  validation {
    condition     = contains(["development", "staging", "production"], var.environment)
    error_message = "Environment must be development, staging, or production."
  }
}

variable "oci_region" {
  description = "OCI region for resource deployment"
  type        = string
  default     = "us-ashburn-1"
}

variable "compartment_id" {
  description = "OCI compartment ID for resource deployment"
  type        = string
}

variable "oci_tenancy_ocid" {
  description = "OCI tenancy OCID"
  type        = string
}

variable "oci_user_ocid" {
  description = "OCI user OCID"
  type        = string
}

variable "oci_fingerprint" {
  description = "OCI API key fingerprint"
  type        = string
}

variable "oci_private_key_path" {
  description = "Path to OCI private key file"
  type        = string
  default     = "~/.oci/oci_api_key.pem"
}

variable "ssh_public_key_path" {
  description = "Path to SSH public key for instance access"
  type        = string
  default     = "~/.ssh/id_rsa.pub"
}

variable "admin_cidr_blocks" {
  description = "CIDR blocks allowed for admin access"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "app_instance_count" {
  description = "Number of application server instances"
  type        = number
  default     = 1
  
  validation {
    condition     = var.app_instance_count >= 1 && var.app_instance_count <= 10
    error_message = "App instance count must be between 1 and 10."
  }
}

variable "app_instance_shape" {
  description = "Shape for application server instances"
  type        = string
  default     = "VM.Standard.A1.Flex"
}

variable "app_instance_ocpus" {
  description = "Number of OCPUs for application server"
  type        = number
  default     = 4
}

variable "app_instance_memory_gb" {
  description = "Memory in GB for application server"
  type        = number
  default     = 24
}

variable "app_boot_volume_size_gb" {
  description = "Boot volume size in GB for application server"
  type        = number
  default     = 100
}

variable "app_data_volume_size_gb" {
  description = "Data volume size in GB for application server"
  type        = number
  default     = 200
}

variable "load_balancer_shape" {
  description = "Load balancer shape"
  type        = string
  default     = "flexible"
}

variable "load_balancer_min_bandwidth" {
  description = "Minimum bandwidth for load balancer in Mbps"
  type        = number
  default     = 10
}

variable "load_balancer_max_bandwidth" {
  description = "Maximum bandwidth for load balancer in Mbps"
  type        = number
  default     = 100
}

variable "enable_monitoring" {
  description = "Enable monitoring and logging"
  type        = bool
  default     = true
}

variable "enable_backup" {
  description = "Enable automated backups"
  type        = bool
  default     = true
}

variable "backup_retention_days" {
  description = "Number of days to retain backups"
  type        = number
  default     = 30
}

variable "cloudflare_api_token" {
  description = "Cloudflare API token for DNS management"
  type        = string
  sensitive   = true
}

variable "cloudflare_zone_id" {
  description = "Cloudflare zone ID for DNS management"
  type        = string
}

variable "domain_name" {
  description = "Primary domain name for the application"
  type        = string
  default     = "yourdomain.com"
}

variable "subdomain_prefix" {
  description = "Subdomain prefix for trends"
  type        = string
  default     = "trends"
}

variable "enable_ssl" {
  description = "Enable SSL/TLS certificates"
  type        = bool
  default     = true
}

variable "ssl_certificate_source" {
  description = "SSL certificate source (letsencrypt, custom)"
  type        = string
  default     = "letsencrypt"
  
  validation {
    condition     = contains(["letsencrypt", "custom"], var.ssl_certificate_source)
    error_message = "SSL certificate source must be letsencrypt or custom."
  }
}

variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

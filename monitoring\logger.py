"""
Structured logging system for the Trend Platform
Provides JSON logging with correlation IDs and module-specific loggers
"""
import json
import logging
import sys
from datetime import datetime
from typing import Dict, Any, Optional
from contextvars import ContextVar
import uuid
from shared.config import settings, MONITORING_CONFIG

# Context variable for correlation ID
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class StructuredFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        # Base log data
        log_data = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': getattr(record, 'module', record.name.split('.')[0]),
            'service': 'trend-platform',
            'environment': settings.environment
        }
        
        # Add correlation ID if available
        corr_id = correlation_id.get()
        if corr_id:
            log_data['correlation_id'] = corr_id
        
        # Add extra fields from record
        extra_fields = ['user_id', 'trend_id', 'deployment_id', 'operation', 'duration', 'status_code']
        for field in extra_fields:
            if hasattr(record, field):
                log_data[field] = getattr(record, field)
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }
        
        # Add any additional metadata
        if hasattr(record, 'metadata') and record.metadata:
            log_data['metadata'] = record.metadata
        
        return json.dumps(log_data, default=str)


class TrendPlatformLogger:
    """Enhanced logger for the Trend Platform"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.module_name = name.split('.')[0]
        
        # Configure logger if not already configured
        if not self.logger.handlers:
            self._configure_logger()
    
    def _configure_logger(self):
        """Configure logger with structured formatter"""
        # Set level
        level = getattr(logging, MONITORING_CONFIG['logging']['level'])
        self.logger.setLevel(level)
        
        # Create handler
        handler = logging.StreamHandler(sys.stdout)
        handler.setLevel(level)
        
        # Set formatter
        if MONITORING_CONFIG['logging']['format'] == 'json':
            formatter = StructuredFormatter()
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        
        # Prevent duplicate logs
        self.logger.propagate = False
    
    def _log(self, level: str, message: str, **kwargs):
        """Internal logging method with extra context"""
        extra = {
            'module': self.module_name,
            **kwargs
        }
        
        getattr(self.logger, level.lower())(message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self._log('DEBUG', message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self._log('INFO', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self._log('WARNING', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self._log('ERROR', message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self._log('CRITICAL', message, **kwargs)
    
    def log_operation_start(self, operation: str, **kwargs):
        """Log operation start"""
        self.info(f"Starting operation: {operation}", operation=operation, **kwargs)
    
    def log_operation_complete(self, operation: str, duration: float, **kwargs):
        """Log operation completion"""
        self.info(
            f"Operation completed: {operation}",
            operation=operation,
            duration=duration,
            **kwargs
        )
    
    def log_operation_error(self, operation: str, error: str, **kwargs):
        """Log operation error"""
        self.error(
            f"Operation failed: {operation} - {error}",
            operation=operation,
            error=error,
            **kwargs
        )
    
    def log_api_request(self, method: str, path: str, status_code: int, duration: float, **kwargs):
        """Log API request"""
        self.info(
            f"{method} {path} - {status_code}",
            operation="api_request",
            method=method,
            path=path,
            status_code=status_code,
            duration=duration,
            **kwargs
        )
    
    def log_database_query(self, query_type: str, duration: float, **kwargs):
        """Log database query"""
        self.debug(
            f"Database query: {query_type}",
            operation="database_query",
            query_type=query_type,
            duration=duration,
            **kwargs
        )
    
    def log_external_api_call(self, service: str, endpoint: str, status_code: int, duration: float, **kwargs):
        """Log external API call"""
        self.info(
            f"External API call: {service} {endpoint} - {status_code}",
            operation="external_api_call",
            service=service,
            endpoint=endpoint,
            status_code=status_code,
            duration=duration,
            **kwargs
        )
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], **kwargs):
        """Log security event"""
        self.warning(
            f"Security event: {event_type}",
            operation="security_event",
            event_type=event_type,
            metadata=details,
            **kwargs
        )
    
    def log_business_event(self, event_type: str, details: Dict[str, Any], **kwargs):
        """Log business event"""
        self.info(
            f"Business event: {event_type}",
            operation="business_event",
            event_type=event_type,
            metadata=details,
            **kwargs
        )


def get_logger(name: str) -> TrendPlatformLogger:
    """Get logger instance for module"""
    return TrendPlatformLogger(name)


def set_correlation_id(corr_id: str = None) -> str:
    """Set correlation ID for request tracing"""
    if not corr_id:
        corr_id = str(uuid.uuid4())
    
    correlation_id.set(corr_id)
    return corr_id


def get_correlation_id() -> Optional[str]:
    """Get current correlation ID"""
    return correlation_id.get()


# Module-specific loggers
scraper_logger = get_logger('scraper')
generator_logger = get_logger('generator')
deploy_logger = get_logger('deploy')
dns_logger = get_logger('dns')
dashboard_logger = get_logger('dashboard')
database_logger = get_logger('database')
security_logger = get_logger('security')
housekeeping_logger = get_logger('housekeeping')
monitoring_logger = get_logger('monitoring')


# Configure root logger
def configure_logging():
    """Configure application logging"""
    # Disable default loggers to prevent duplicate logs
    logging.getLogger('uvicorn').handlers = []
    logging.getLogger('uvicorn.access').handlers = []
    
    # Set levels for third-party loggers
    logging.getLogger('asyncpg').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


# Initialize logging configuration
configure_logging()

"""
Metrics collection and Prometheus integration for the Trend Platform
Provides application and business metrics
"""
import time
import asyncio
from typing import Dict, Any, Optional
from functools import wraps
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
from prometheus_client.exposition import choose_encoder
from shared.config import MONITORING_CONFIG
from monitoring.logger import get_logger

logger = get_logger('monitoring.metrics')


class ApplicationMetrics:
    """Application metrics collector"""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or CollectorRegistry()
        self._setup_metrics()
    
    def _setup_metrics(self):
        """Initialize Prometheus metrics"""
        # HTTP metrics
        self.http_requests_total = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
        
        self.http_request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['method', 'endpoint'],
            registry=self.registry
        )
        
        # Trend scraping metrics
        self.trends_scraped_total = Counter(
            'trends_scraped_total',
            'Total trends scraped',
            ['source', 'region', 'category'],
            registry=self.registry
        )
        
        self.scraping_duration = Histogram(
            'scraping_duration_seconds',
            'Time spent scraping trends',
            ['source'],
            registry=self.registry
        )
        
        self.scraping_errors_total = Counter(
            'scraping_errors_total',
            'Total scraping errors',
            ['source', 'error_type'],
            registry=self.registry
        )
        
        # Content generation metrics
        self.content_generated_total = Counter(
            'content_generated_total',
            'Total content pieces generated',
            ['category', 'ai_provider'],
            registry=self.registry
        )
        
        self.content_generation_duration = Histogram(
            'content_generation_duration_seconds',
            'Time spent generating content',
            ['content_type'],
            registry=self.registry
        )
        
        self.ai_api_calls_total = Counter(
            'ai_api_calls_total',
            'Total AI API calls',
            ['provider', 'model', 'call_type'],
            registry=self.registry
        )
        
        self.ai_api_errors_total = Counter(
            'ai_api_errors_total',
            'Total AI API errors',
            ['provider', 'model', 'error_type'],
            registry=self.registry
        )
        
        # Deployment metrics
        self.deployments_total = Counter(
            'deployments_total',
            'Total deployments',
            ['status'],
            registry=self.registry
        )
        
        self.deployment_duration = Histogram(
            'deployment_duration_seconds',
            'Time spent on deployments',
            registry=self.registry
        )
        
        # DNS metrics
        self.dns_operations_total = Counter(
            'dns_operations_total',
            'Total DNS operations',
            ['operation', 'status'],
            registry=self.registry
        )
        
        self.dns_operation_duration = Histogram(
            'dns_operation_duration_seconds',
            'DNS operation duration',
            ['operation'],
            registry=self.registry
        )
        
        # Database metrics
        self.database_queries_total = Counter(
            'database_queries_total',
            'Total database queries',
            ['query_type', 'status'],
            registry=self.registry
        )
        
        self.database_query_duration = Histogram(
            'database_query_duration_seconds',
            'Database query duration',
            ['query_type'],
            registry=self.registry
        )
        
        self.database_connections_active = Gauge(
            'database_connections_active',
            'Active database connections',
            registry=self.registry
        )
        
        # System health metrics
        self.active_trends = Gauge(
            'active_trends_total',
            'Number of currently active trends',
            registry=self.registry
        )
        
        self.pending_trends = Gauge(
            'pending_trends_total',
            'Number of pending trends',
            registry=self.registry
        )
        
        self.live_deployments = Gauge(
            'live_deployments_total',
            'Number of live deployments',
            registry=self.registry
        )
        
        # Celery metrics
        self.celery_tasks_total = Counter(
            'celery_tasks_total',
            'Total Celery tasks',
            ['task_name', 'status'],
            registry=self.registry
        )
        
        self.celery_task_duration = Histogram(
            'celery_task_duration_seconds',
            'Celery task duration',
            ['task_name'],
            registry=self.registry
        )
        
        self.celery_queue_length = Gauge(
            'celery_queue_length',
            'Celery queue length',
            ['queue'],
            registry=self.registry
        )
        
        # Security metrics
        self.security_events_total = Counter(
            'security_events_total',
            'Total security events',
            ['event_type', 'severity'],
            registry=self.registry
        )
        
        self.rate_limit_hits_total = Counter(
            'rate_limit_hits_total',
            'Total rate limit hits',
            ['endpoint'],
            registry=self.registry
        )
        
        # Business metrics
        self.page_views_total = Counter(
            'page_views_total',
            'Total page views',
            ['trend_id', 'country'],
            registry=self.registry
        )
        
        self.unique_visitors = Gauge(
            'unique_visitors_total',
            'Total unique visitors',
            ['period'],
            registry=self.registry
        )
    
    # HTTP metrics methods
    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics"""
        self.http_requests_total.labels(
            method=method,
            endpoint=endpoint,
            status=str(status_code)
        ).inc()
        
        self.http_request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    # Scraping metrics methods
    def record_trend_scraped(self, source: str, region: str, category: str, count: int = 1):
        """Record trends scraped"""
        self.trends_scraped_total.labels(
            source=source,
            region=region,
            category=category
        ).inc(count)
    
    def record_scraping_duration(self, source: str, duration: float):
        """Record scraping duration"""
        self.scraping_duration.labels(source=source).observe(duration)
    
    def record_scraping_error(self, source: str, error_type: str):
        """Record scraping error"""
        self.scraping_errors_total.labels(
            source=source,
            error_type=error_type
        ).inc()
    
    # Content generation metrics methods
    def record_content_generated(self, category: str, ai_provider: str):
        """Record content generation"""
        self.content_generated_total.labels(
            category=category,
            ai_provider=ai_provider
        ).inc()
    
    def record_content_generation_duration(self, content_type: str, duration: float):
        """Record content generation duration"""
        self.content_generation_duration.labels(
            content_type=content_type
        ).observe(duration)
    
    def record_ai_api_call(self, provider: str, model: str, call_type: str):
        """Record AI API call"""
        self.ai_api_calls_total.labels(
            provider=provider,
            model=model,
            call_type=call_type
        ).inc()
    
    def record_ai_api_error(self, provider: str, model: str, error_type: str):
        """Record AI API error"""
        self.ai_api_errors_total.labels(
            provider=provider,
            model=model,
            error_type=error_type
        ).inc()
    
    # Deployment metrics methods
    def record_deployment(self, status: str, duration: float = None):
        """Record deployment"""
        self.deployments_total.labels(status=status).inc()
        if duration:
            self.deployment_duration.observe(duration)
    
    # DNS metrics methods
    def record_dns_operation(self, operation: str, status: str, duration: float):
        """Record DNS operation"""
        self.dns_operations_total.labels(
            operation=operation,
            status=status
        ).inc()
        
        self.dns_operation_duration.labels(
            operation=operation
        ).observe(duration)
    
    # Database metrics methods
    def record_database_query(self, query_type: str, status: str, duration: float):
        """Record database query"""
        self.database_queries_total.labels(
            query_type=query_type,
            status=status
        ).inc()
        
        self.database_query_duration.labels(
            query_type=query_type
        ).observe(duration)
    
    def update_database_connections(self, active_connections: int):
        """Update active database connections"""
        self.database_connections_active.set(active_connections)
    
    # System health methods
    def update_trend_counts(self, active: int, pending: int):
        """Update trend count gauges"""
        self.active_trends.set(active)
        self.pending_trends.set(pending)
    
    def update_live_deployments(self, count: int):
        """Update live deployments count"""
        self.live_deployments.set(count)
    
    # Celery metrics methods
    def record_celery_task(self, task_name: str, status: str, duration: float = None):
        """Record Celery task execution"""
        self.celery_tasks_total.labels(
            task_name=task_name,
            status=status
        ).inc()
        
        if duration:
            self.celery_task_duration.labels(
                task_name=task_name
            ).observe(duration)
    
    def update_celery_queue_length(self, queue: str, length: int):
        """Update Celery queue length"""
        self.celery_queue_length.labels(queue=queue).set(length)
    
    # Security metrics methods
    def record_security_event(self, event_type: str, severity: str):
        """Record security event"""
        self.security_events_total.labels(
            event_type=event_type,
            severity=severity
        ).inc()
    
    def record_rate_limit_hit(self, endpoint: str):
        """Record rate limit hit"""
        self.rate_limit_hits_total.labels(endpoint=endpoint).inc()
    
    # Business metrics methods
    def record_page_view(self, trend_id: str, country: str):
        """Record page view"""
        self.page_views_total.labels(
            trend_id=trend_id,
            country=country
        ).inc()
    
    def update_unique_visitors(self, period: str, count: int):
        """Update unique visitors count"""
        self.unique_visitors.labels(period=period).set(count)
    
    def time_function(self, metric_name: str, labels: Dict[str, str] = None):
        """Decorator to time function execution"""
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    metric = getattr(self, metric_name)
                    if labels:
                        metric.labels(**labels).observe(duration)
                    else:
                        metric.observe(duration)
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    metric = getattr(self, metric_name)
                    if labels:
                        metric.labels(**labels).observe(duration)
                    else:
                        metric.observe(duration)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def get_metrics(self) -> str:
        """Get metrics in Prometheus format"""
        return generate_latest(self.registry).decode('utf-8')


# Global metrics instance
app_metrics = ApplicationMetrics()


def get_metrics() -> str:
    """Get all metrics in Prometheus format"""
    return app_metrics.get_metrics()

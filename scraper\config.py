"""
Scraper configuration management
Centralizes all scraper-related settings and configurations
"""
import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum

from shared.config import settings


class ScrapingMode(str, Enum):
    """Scraping operation modes"""
    AGGRESSIVE = "aggressive"  # High frequency, multiple sources
    BALANCED = "balanced"     # Moderate frequency, quality focus
    CONSERVATIVE = "conservative"  # Low frequency, high quality only


@dataclass
class SourceConfig:
    """Configuration for individual scraping sources"""
    name: str
    enabled: bool = True
    rate_limit_delay: float = 1.0
    max_retries: int = 3
    timeout: int = 30
    max_trends_per_request: int = 50
    supported_regions: List[str] = field(default_factory=list)
    supported_categories: List[str] = field(default_factory=list)
    api_key: Optional[str] = None
    custom_headers: Dict[str, str] = field(default_factory=dict)
    proxy_required: bool = False
    quality_threshold: float = 0.5
    
    def __post_init__(self):
        # Load API key from environment if not provided
        if not self.api_key and self.name:
            env_key = f"{self.name.upper()}_API_KEY"
            self.api_key = os.getenv(env_key)


@dataclass
class ProxyConfig:
    """Proxy configuration"""
    enabled: bool = False
    proxy_list: List[str] = field(default_factory=list)
    max_failures: int = 3
    ban_duration: int = 300  # seconds
    health_check_interval: int = 60  # seconds
    test_url: str = "http://httpbin.org/ip"
    request_timeout: int = 10
    rotation_strategy: str = "weighted_random"  # weighted_random, round_robin, random


@dataclass
class DeduplicationConfig:
    """Deduplication configuration"""
    enabled: bool = True
    exact_match_threshold: float = 0.95
    fuzzy_match_threshold: float = 0.85
    semantic_match_threshold: float = 0.80
    min_keyword_length: int = 2
    resolution_strategy: str = "best_quality"  # best_quality, most_recent, highest_volume


@dataclass
class FilterConfig:
    """Content filtering configuration"""
    min_search_volume: Optional[int] = None
    max_search_volume: Optional[int] = None
    min_growth_rate: Optional[float] = None
    max_growth_rate: Optional[float] = None
    blocked_keywords: List[str] = field(default_factory=list)
    required_keywords: List[str] = field(default_factory=list)
    blocked_categories: List[str] = field(default_factory=list)
    allowed_categories: List[str] = field(default_factory=list)
    min_confidence_score: float = 0.3


@dataclass
class ScrapingSchedule:
    """Scraping schedule configuration"""
    enabled: bool = True
    interval_minutes: int = 15
    max_concurrent_scrapers: int = 3
    stagger_delay_seconds: int = 5
    retry_failed_after_minutes: int = 60
    max_daily_runs: int = 96  # 24 hours * 4 runs per hour


class ScraperConfig:
    """Main scraper configuration manager"""
    
    def __init__(self):
        self.mode = ScrapingMode(os.getenv('SCRAPING_MODE', 'balanced'))
        
        # Load configurations based on mode
        self._load_mode_specific_config()
        
        # Source configurations
        self.sources = self._load_source_configs()
        
        # Component configurations
        self.proxy = self._load_proxy_config()
        self.deduplication = self._load_deduplication_config()
        self.filtering = self._load_filter_config()
        self.schedule = self._load_schedule_config()
        
        # General settings
        self.max_trends_per_run = int(os.getenv('MAX_TRENDS_PER_RUN', '200'))
        self.data_retention_days = int(os.getenv('SCRAPER_DATA_RETENTION_DAYS', '30'))
        self.enable_metrics = os.getenv('ENABLE_SCRAPER_METRICS', 'true').lower() == 'true'
        self.log_level = os.getenv('SCRAPER_LOG_LEVEL', 'INFO')
    
    def _load_mode_specific_config(self):
        """Load configuration based on scraping mode"""
        if self.mode == ScrapingMode.AGGRESSIVE:
            self.default_rate_limit = 0.5
            self.default_max_retries = 5
            self.default_timeout = 45
            self.quality_threshold = 0.3
        elif self.mode == ScrapingMode.CONSERVATIVE:
            self.default_rate_limit = 3.0
            self.default_max_retries = 2
            self.default_timeout = 20
            self.quality_threshold = 0.7
        else:  # BALANCED
            self.default_rate_limit = 1.0
            self.default_max_retries = 3
            self.default_timeout = 30
            self.quality_threshold = 0.5
    
    def _load_source_configs(self) -> Dict[str, SourceConfig]:
        """Load individual source configurations"""
        sources = {}
        
        # Google Trends configuration
        sources['google_trends'] = SourceConfig(
            name='google_trends',
            enabled=os.getenv('GOOGLE_TRENDS_ENABLED', 'true').lower() == 'true',
            rate_limit_delay=float(os.getenv('GOOGLE_TRENDS_RATE_LIMIT', str(self.default_rate_limit))),
            max_retries=int(os.getenv('GOOGLE_TRENDS_MAX_RETRIES', str(self.default_max_retries))),
            timeout=int(os.getenv('GOOGLE_TRENDS_TIMEOUT', str(self.default_timeout))),
            max_trends_per_request=int(os.getenv('GOOGLE_TRENDS_MAX_TRENDS', '50')),
            supported_regions=['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'JP', 'IN', 'BR'],
            supported_categories=['Technology', 'Health', 'Entertainment', 'Sports', 'Business', 'Science'],
            proxy_required=os.getenv('GOOGLE_TRENDS_PROXY_REQUIRED', 'false').lower() == 'true',
            quality_threshold=self.quality_threshold
        )
        
        # Trends24 configuration
        sources['trends24'] = SourceConfig(
            name='trends24',
            enabled=os.getenv('TRENDS24_ENABLED', 'true').lower() == 'true',
            rate_limit_delay=float(os.getenv('TRENDS24_RATE_LIMIT', str(self.default_rate_limit))),
            max_retries=int(os.getenv('TRENDS24_MAX_RETRIES', str(self.default_max_retries))),
            timeout=int(os.getenv('TRENDS24_TIMEOUT', str(self.default_timeout))),
            max_trends_per_request=int(os.getenv('TRENDS24_MAX_TRENDS', '30')),
            supported_regions=['US', 'UK', 'CA', 'AU', 'DE', 'FR'],
            supported_categories=['Technology', 'Entertainment', 'Sports', 'Business'],
            proxy_required=os.getenv('TRENDS24_PROXY_REQUIRED', 'true').lower() == 'true',
            quality_threshold=self.quality_threshold
        )
        
        # Twitter Trends configuration (if enabled)
        if os.getenv('TWITTER_ENABLED', 'false').lower() == 'true':
            sources['twitter'] = SourceConfig(
                name='twitter',
                enabled=True,
                rate_limit_delay=float(os.getenv('TWITTER_RATE_LIMIT', '2.0')),
                max_retries=int(os.getenv('TWITTER_MAX_RETRIES', '2')),
                timeout=int(os.getenv('TWITTER_TIMEOUT', '25')),
                max_trends_per_request=int(os.getenv('TWITTER_MAX_TRENDS', '20')),
                supported_regions=['US', 'UK', 'CA', 'AU'],
                supported_categories=['Technology', 'Entertainment', 'Sports'],
                api_key=os.getenv('TWITTER_API_KEY'),
                custom_headers={'User-Agent': 'TrendSite-Bot/1.0'},
                proxy_required=True,
                quality_threshold=self.quality_threshold
            )
        
        return sources
    
    def _load_proxy_config(self) -> ProxyConfig:
        """Load proxy configuration"""
        proxy_list = []
        proxy_list_env = os.getenv('PROXY_LIST', '')
        if proxy_list_env:
            proxy_list = [p.strip() for p in proxy_list_env.split(',') if p.strip()]
        
        return ProxyConfig(
            enabled=os.getenv('PROXY_ENABLED', 'false').lower() == 'true',
            proxy_list=proxy_list,
            max_failures=int(os.getenv('PROXY_MAX_FAILURES', '3')),
            ban_duration=int(os.getenv('PROXY_BAN_DURATION', '300')),
            health_check_interval=int(os.getenv('PROXY_HEALTH_CHECK_INTERVAL', '60')),
            test_url=os.getenv('PROXY_TEST_URL', 'http://httpbin.org/ip'),
            request_timeout=int(os.getenv('PROXY_REQUEST_TIMEOUT', '10')),
            rotation_strategy=os.getenv('PROXY_ROTATION_STRATEGY', 'weighted_random')
        )
    
    def _load_deduplication_config(self) -> DeduplicationConfig:
        """Load deduplication configuration"""
        return DeduplicationConfig(
            enabled=os.getenv('DEDUPLICATION_ENABLED', 'true').lower() == 'true',
            exact_match_threshold=float(os.getenv('DEDUPLICATION_EXACT_THRESHOLD', '0.95')),
            fuzzy_match_threshold=float(os.getenv('DEDUPLICATION_FUZZY_THRESHOLD', '0.85')),
            semantic_match_threshold=float(os.getenv('DEDUPLICATION_SEMANTIC_THRESHOLD', '0.80')),
            min_keyword_length=int(os.getenv('DEDUPLICATION_MIN_KEYWORD_LENGTH', '2')),
            resolution_strategy=os.getenv('DEDUPLICATION_RESOLUTION_STRATEGY', 'best_quality')
        )
    
    def _load_filter_config(self) -> FilterConfig:
        """Load filtering configuration"""
        blocked_keywords = []
        blocked_keywords_env = os.getenv('BLOCKED_KEYWORDS', '')
        if blocked_keywords_env:
            blocked_keywords = [k.strip().lower() for k in blocked_keywords_env.split(',') if k.strip()]
        
        required_keywords = []
        required_keywords_env = os.getenv('REQUIRED_KEYWORDS', '')
        if required_keywords_env:
            required_keywords = [k.strip().lower() for k in required_keywords_env.split(',') if k.strip()]
        
        blocked_categories = []
        blocked_categories_env = os.getenv('BLOCKED_CATEGORIES', '')
        if blocked_categories_env:
            blocked_categories = [c.strip() for c in blocked_categories_env.split(',') if c.strip()]
        
        allowed_categories = []
        allowed_categories_env = os.getenv('ALLOWED_CATEGORIES', '')
        if allowed_categories_env:
            allowed_categories = [c.strip() for c in allowed_categories_env.split(',') if c.strip()]
        
        return FilterConfig(
            min_search_volume=int(os.getenv('MIN_SEARCH_VOLUME')) if os.getenv('MIN_SEARCH_VOLUME') else None,
            max_search_volume=int(os.getenv('MAX_SEARCH_VOLUME')) if os.getenv('MAX_SEARCH_VOLUME') else None,
            min_growth_rate=float(os.getenv('MIN_GROWTH_RATE')) if os.getenv('MIN_GROWTH_RATE') else None,
            max_growth_rate=float(os.getenv('MAX_GROWTH_RATE')) if os.getenv('MAX_GROWTH_RATE') else None,
            blocked_keywords=blocked_keywords,
            required_keywords=required_keywords,
            blocked_categories=blocked_categories,
            allowed_categories=allowed_categories,
            min_confidence_score=float(os.getenv('MIN_CONFIDENCE_SCORE', '0.3'))
        )
    
    def _load_schedule_config(self) -> ScrapingSchedule:
        """Load scheduling configuration"""
        return ScrapingSchedule(
            enabled=os.getenv('SCRAPING_SCHEDULE_ENABLED', 'true').lower() == 'true',
            interval_minutes=int(os.getenv('SCRAPING_INTERVAL_MINUTES', '15')),
            max_concurrent_scrapers=int(os.getenv('MAX_CONCURRENT_SCRAPERS', '3')),
            stagger_delay_seconds=int(os.getenv('SCRAPER_STAGGER_DELAY', '5')),
            retry_failed_after_minutes=int(os.getenv('RETRY_FAILED_AFTER_MINUTES', '60')),
            max_daily_runs=int(os.getenv('MAX_DAILY_SCRAPING_RUNS', '96'))
        )
    
    def get_source_config(self, source_name: str) -> Optional[SourceConfig]:
        """Get configuration for a specific source"""
        return self.sources.get(source_name)
    
    def get_enabled_sources(self) -> List[SourceConfig]:
        """Get list of enabled sources"""
        return [config for config in self.sources.values() if config.enabled]
    
    def is_source_enabled(self, source_name: str) -> bool:
        """Check if a source is enabled"""
        config = self.get_source_config(source_name)
        return config.enabled if config else False
    
    def get_supported_regions(self) -> List[str]:
        """Get all supported regions across all sources"""
        regions = set()
        for config in self.get_enabled_sources():
            regions.update(config.supported_regions)
        return sorted(list(regions))
    
    def get_supported_categories(self) -> List[str]:
        """Get all supported categories across all sources"""
        categories = set()
        for config in self.get_enabled_sources():
            categories.update(config.supported_categories)
        return sorted(list(categories))
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Check if at least one source is enabled
        enabled_sources = self.get_enabled_sources()
        if not enabled_sources:
            issues.append("No scraping sources are enabled")
        
        # Validate proxy configuration
        if self.proxy.enabled and not self.proxy.proxy_list:
            issues.append("Proxy is enabled but no proxy list provided")
        
        # Check for required API keys
        for source_config in enabled_sources:
            if source_config.name == 'twitter' and not source_config.api_key:
                issues.append(f"Twitter source enabled but no API key provided")
        
        # Validate thresholds
        if not (0.0 <= self.deduplication.exact_match_threshold <= 1.0):
            issues.append("Exact match threshold must be between 0.0 and 1.0")
        
        if not (0.0 <= self.deduplication.fuzzy_match_threshold <= 1.0):
            issues.append("Fuzzy match threshold must be between 0.0 and 1.0")
        
        if not (0.0 <= self.filtering.min_confidence_score <= 1.0):
            issues.append("Minimum confidence score must be between 0.0 and 1.0")
        
        return issues
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'mode': self.mode,
            'sources': {name: {
                'name': config.name,
                'enabled': config.enabled,
                'rate_limit_delay': config.rate_limit_delay,
                'max_retries': config.max_retries,
                'timeout': config.timeout,
                'max_trends_per_request': config.max_trends_per_request,
                'supported_regions': config.supported_regions,
                'supported_categories': config.supported_categories,
                'proxy_required': config.proxy_required,
                'quality_threshold': config.quality_threshold
            } for name, config in self.sources.items()},
            'proxy': {
                'enabled': self.proxy.enabled,
                'proxy_count': len(self.proxy.proxy_list),
                'max_failures': self.proxy.max_failures,
                'ban_duration': self.proxy.ban_duration,
                'health_check_interval': self.proxy.health_check_interval,
                'rotation_strategy': self.proxy.rotation_strategy
            },
            'deduplication': {
                'enabled': self.deduplication.enabled,
                'exact_match_threshold': self.deduplication.exact_match_threshold,
                'fuzzy_match_threshold': self.deduplication.fuzzy_match_threshold,
                'semantic_match_threshold': self.deduplication.semantic_match_threshold,
                'resolution_strategy': self.deduplication.resolution_strategy
            },
            'filtering': {
                'min_search_volume': self.filtering.min_search_volume,
                'max_search_volume': self.filtering.max_search_volume,
                'min_growth_rate': self.filtering.min_growth_rate,
                'max_growth_rate': self.filtering.max_growth_rate,
                'blocked_keywords_count': len(self.filtering.blocked_keywords),
                'required_keywords_count': len(self.filtering.required_keywords),
                'min_confidence_score': self.filtering.min_confidence_score
            },
            'schedule': {
                'enabled': self.schedule.enabled,
                'interval_minutes': self.schedule.interval_minutes,
                'max_concurrent_scrapers': self.schedule.max_concurrent_scrapers,
                'max_daily_runs': self.schedule.max_daily_runs
            },
            'general': {
                'max_trends_per_run': self.max_trends_per_run,
                'data_retention_days': self.data_retention_days,
                'enable_metrics': self.enable_metrics,
                'log_level': self.log_level
            }
        }


# Global scraper configuration instance
scraper_config = ScraperConfig()

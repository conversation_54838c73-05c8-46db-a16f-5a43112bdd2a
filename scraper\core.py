"""
Core scraper orchestration module
Manages multiple scrapers and coordinates trend collection
"""
import asyncio
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from .sources.base_scraper import BaseScraper, TrendData, ScrapingResult, ScrapingStatus, scraper_registry
from .sources.google_trends import GoogleTrendsScraper
from .sources.trends24_scraper import Trends24Scraper
from .deduplication import FuzzyMatcher
from .filters import TrendFilter
from .validators import TrendValidator
from .scoring import TrendScorer
from .utils.error_handling import retry_handler, CircuitBreaker
from .utils.rate_limiting import create_rate_limiter, RateLimitConfig

from shared.config import SCRAPER_CONFIG
from shared.exceptions import ScrapingError
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from database.models.trend_model import TrendRepository


class TrendScrapingOrchestrator:
    """Orchestrates trend scraping from multiple sources"""
    
    def __init__(self):
        self.logger = get_logger('scraper.orchestrator')
        self.metrics = app_metrics
        self.trend_repo = TrendRepository()
        
        # Initialize components
        self.deduplicator = FuzzyMatcher(threshold=0.85)
        self.filter = TrendFilter()
        self.validator = TrendValidator()
        self.scorer = TrendScorer()
        
        # Initialize scrapers
        self.scrapers = {}
        self._initialize_scrapers()
    
    def _initialize_scrapers(self):
        """Initialize and register all scrapers"""
        try:
            # Initialize Google Trends scraper
            google_scraper = GoogleTrendsScraper()
            self.scrapers['google_trends'] = google_scraper
            scraper_registry.register(google_scraper)
            
            # Initialize Trends24 scraper
            trends24_scraper = Trends24Scraper()
            self.scrapers['trends24'] = trends24_scraper
            scraper_registry.register(trends24_scraper)
            
            self.logger.info(f"Initialized {len(self.scrapers)} scrapers")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize scrapers: {str(e)}")
            raise ScrapingError(f"Scraper initialization failed: {str(e)}")
    
    async def scrape_single_source(
        self,
        scraper_name: str,
        region: str,
        category: Optional[str] = None,
        limit: int = 50
    ) -> ScrapingResult:
        """Scrape trends from a single source"""
        if scraper_name not in self.scrapers:
            raise ScrapingError(f"Unknown scraper: {scraper_name}")
        
        scraper = self.scrapers[scraper_name]
        
        try:
            self.logger.info(
                f"Scraping from {scraper_name}",
                scraper=scraper_name,
                region=region,
                category=category,
                limit=limit
            )
            
            result = await scraper.scrape_trends(region, category, limit)
            
            self.logger.info(
                f"Scraping completed for {scraper_name}",
                scraper=scraper_name,
                status=result.status,
                trends_found=result.total_found,
                processing_time=result.processing_time
            )
            
            return result
            
        except Exception as e:
            self.logger.error(
                f"Scraping failed for {scraper_name}: {str(e)}",
                scraper=scraper_name,
                region=region,
                category=category,
                error=str(e)
            )
            
            return ScrapingResult(
                status=ScrapingStatus.FAILED,
                trends=[],
                total_found=0,
                processing_time=0,
                error_message=str(e)
            )
    
    async def scrape_all_sources(
        self,
        regions: Optional[List[str]] = None,
        categories: Optional[List[str]] = None,
        limit_per_source: int = 50
    ) -> Dict[str, Any]:
        """Scrape trends from all sources and regions"""
        start_time = time.time()
        
        # Use config defaults if not specified
        regions = regions or SCRAPER_CONFIG['regions']
        categories = categories or SCRAPER_CONFIG['categories']
        
        results = {
            'total_scraped': 0,
            'total_processed': 0,
            'total_saved': 0,
            'sources': {},
            'errors': [],
            'processing_time': 0
        }
        
        self.logger.info(
            "Starting comprehensive trend scraping",
            regions=regions,
            categories=categories,
            scrapers=list(self.scrapers.keys())
        )
        
        all_trends = []
        
        # Scrape from all sources
        for scraper_name, scraper in self.scrapers.items():
            source_results = {
                'scraped': 0,
                'processed': 0,
                'saved': 0,
                'errors': [],
                'regions': {}
            }
            
            for region in regions:
                for category in categories:
                    try:
                        # Scrape trends
                        scraping_result = await self.scrape_single_source(
                            scraper_name, region, category, limit_per_source
                        )
                        
                        if scraping_result.status == ScrapingStatus.COMPLETED:
                            source_results['scraped'] += scraping_result.total_found
                            all_trends.extend(scraping_result.trends)
                            
                            # Store region-specific results
                            if region not in source_results['regions']:
                                source_results['regions'][region] = {}
                            source_results['regions'][region][category] = {
                                'trends_found': scraping_result.total_found,
                                'processing_time': scraping_result.processing_time
                            }
                        else:
                            error_msg = f"Failed to scrape {scraper_name} for {region}/{category}: {scraping_result.error_message}"
                            source_results['errors'].append(error_msg)
                        
                        # Small delay between requests
                        await asyncio.sleep(1)
                        
                    except Exception as e:
                        error_msg = f"Exception in {scraper_name} for {region}/{category}: {str(e)}"
                        source_results['errors'].append(error_msg)
                        self.logger.error(error_msg)
            
            results['sources'][scraper_name] = source_results
            results['total_scraped'] += source_results['scraped']
            results['errors'].extend(source_results['errors'])
        
        # Process all collected trends
        if all_trends:
            processed_trends = await self._process_trends(all_trends)
            results['total_processed'] = len(processed_trends)
            
            # Save processed trends
            saved_count = await self._save_trends(processed_trends)
            results['total_saved'] = saved_count
        
        results['processing_time'] = time.time() - start_time
        
        self.logger.info(
            "Trend scraping completed",
            total_scraped=results['total_scraped'],
            total_processed=results['total_processed'],
            total_saved=results['total_saved'],
            processing_time=results['processing_time'],
            error_count=len(results['errors'])
        )
        
        return results
    
    async def _process_trends(self, trends: List[TrendData]) -> List[TrendData]:
        """Process trends through filtering, validation, and deduplication"""
        self.logger.info(f"Processing {len(trends)} raw trends")
        
        # Filter trends
        filtered_trends = self.filter.filter_trends(trends)
        self.logger.debug(f"After filtering: {len(filtered_trends)} trends")
        
        # Validate trends
        validated_trends = []
        for trend in filtered_trends:
            if self.validator.validate_trend(trend):
                validated_trends.append(trend)
        self.logger.debug(f"After validation: {len(validated_trends)} trends")
        
        # Deduplicate trends
        deduplicated_trends = self.deduplicator.deduplicate_trends(validated_trends)
        self.logger.debug(f"After deduplication: {len(deduplicated_trends)} trends")
        
        # Score trends
        scored_trends = []
        for trend in deduplicated_trends:
            score = self.scorer.calculate_score(trend)
            if score >= SCRAPER_CONFIG['scoring']['minimum_score']:
                # Add score to metadata
                trend.metadata['calculated_score'] = score
                scored_trends.append(trend)
        
        self.logger.info(f"Processing completed: {len(scored_trends)} final trends")
        return scored_trends
    
    async def _save_trends(self, trends: List[TrendData]) -> int:
        """Save processed trends to database"""
        saved_count = 0
        
        for trend in trends:
            try:
                # Convert TrendData to database format
                trend_data = {
                    'keyword': trend.keyword,
                    'region': trend.region,
                    'category': trend.category,
                    'search_volume': trend.search_volume,
                    'growth_rate': trend.growth_rate,
                    'source': trend.source,
                    'raw_data': trend.metadata,
                    'score': trend.metadata.get('calculated_score', 0)
                }
                
                # Check if trend already exists
                existing = await self.trend_repo.get_by_keyword_and_region(
                    trend.keyword, trend.region
                )
                
                if existing:
                    # Update existing trend
                    await self.trend_repo.update(existing.id, trend_data)
                else:
                    # Create new trend
                    from shared.utils import generate_slug
                    trend_data['slug'] = generate_slug(f"{trend.keyword}-{trend.region}")
                    await self.trend_repo.create(trend_data)
                
                saved_count += 1
                
            except Exception as e:
                self.logger.error(
                    f"Failed to save trend '{trend.keyword}': {str(e)}",
                    keyword=trend.keyword,
                    region=trend.region,
                    error=str(e)
                )
        
        return saved_count
    
    async def get_scraper_health(self) -> Dict[str, Any]:
        """Get health status of all scrapers"""
        return await scraper_registry.get_health_status()
    
    async def cleanup(self):
        """Cleanup all scrapers"""
        await scraper_registry.cleanup_all()


# Global orchestrator instance
trend_orchestrator = TrendScrapingOrchestrator()

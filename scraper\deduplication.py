"""
Trend deduplication system using fuzzy matching
Identifies and removes duplicate trends from different sources
"""
import re
from typing import List, Dict, Any, Set, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher
from collections import defaultdict

from scraper.sources.base_scraper import TrendData
from monitoring.logger import get_logger


@dataclass
class DuplicationMatch:
    """Represents a potential duplication match"""
    trend1: TrendData
    trend2: TrendData
    similarity_score: float
    match_type: str
    confidence: float


class TrendDeduplicator:
    """Handles trend deduplication using various matching strategies"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = get_logger('scraper.deduplicator')
        
        # Configuration thresholds
        self.exact_match_threshold = self.config.get('exact_match_threshold', 0.95)
        self.fuzzy_match_threshold = self.config.get('fuzzy_match_threshold', 0.85)
        self.semantic_match_threshold = self.config.get('semantic_match_threshold', 0.80)
        self.min_keyword_length = self.config.get('min_keyword_length', 2)
        
        # Preprocessing patterns
        self.normalization_patterns = [
            (r'\s+', ' '),  # Multiple spaces to single space
            (r'[^\w\s-]', ''),  # Remove special characters except hyphens
            (r'\b(the|a|an|and|or|but|in|on|at|to|for|of|with|by)\b', ''),  # Remove common words
        ]
        
        # Common abbreviations and expansions
        self.abbreviation_map = {
            'ai': 'artificial intelligence',
            'ml': 'machine learning',
            'vr': 'virtual reality',
            'ar': 'augmented reality',
            'iot': 'internet of things',
            'api': 'application programming interface',
            'ui': 'user interface',
            'ux': 'user experience',
            'seo': 'search engine optimization',
            'ceo': 'chief executive officer',
            'cto': 'chief technology officer',
            'usa': 'united states',
            'uk': 'united kingdom',
            'eu': 'european union',
        }
    
    def normalize_keyword(self, keyword: str) -> str:
        """Normalize keyword for comparison"""
        if not keyword:
            return ""
        
        # Convert to lowercase
        normalized = keyword.lower().strip()
        
        # Apply normalization patterns
        for pattern, replacement in self.normalization_patterns:
            normalized = re.sub(pattern, replacement, normalized)
        
        # Expand abbreviations
        words = normalized.split()
        expanded_words = []
        for word in words:
            if word in self.abbreviation_map:
                expanded_words.append(self.abbreviation_map[word])
            else:
                expanded_words.append(word)
        
        normalized = ' '.join(expanded_words).strip()
        
        return normalized
    
    def extract_keywords(self, text: str) -> Set[str]:
        """Extract meaningful keywords from text"""
        normalized = self.normalize_keyword(text)
        words = normalized.split()
        
        # Filter out short words and common terms
        keywords = set()
        for word in words:
            if len(word) >= self.min_keyword_length and word.isalpha():
                keywords.add(word)
        
        return keywords
    
    def calculate_exact_similarity(self, keyword1: str, keyword2: str) -> float:
        """Calculate exact string similarity"""
        norm1 = self.normalize_keyword(keyword1)
        norm2 = self.normalize_keyword(keyword2)
        
        if norm1 == norm2:
            return 1.0
        
        # Use SequenceMatcher for character-level similarity
        return SequenceMatcher(None, norm1, norm2).ratio()
    
    def calculate_fuzzy_similarity(self, keyword1: str, keyword2: str) -> float:
        """Calculate fuzzy similarity using multiple methods"""
        keywords1 = self.extract_keywords(keyword1)
        keywords2 = self.extract_keywords(keyword2)
        
        if not keywords1 or not keywords2:
            return 0.0
        
        # Jaccard similarity (intersection over union)
        intersection = keywords1.intersection(keywords2)
        union = keywords1.union(keywords2)
        jaccard_score = len(intersection) / len(union) if union else 0.0
        
        # Word-level sequence matching
        words1 = sorted(keywords1)
        words2 = sorted(keywords2)
        sequence_score = SequenceMatcher(None, words1, words2).ratio()
        
        # Combine scores with weights
        fuzzy_score = (jaccard_score * 0.7) + (sequence_score * 0.3)
        
        return fuzzy_score
    
    def calculate_semantic_similarity(self, trend1: TrendData, trend2: TrendData) -> float:
        """Calculate semantic similarity using metadata"""
        score = 0.0
        factors = 0
        
        # Category similarity
        if trend1.category and trend2.category:
            if trend1.category.lower() == trend2.category.lower():
                score += 0.3
            factors += 1
        
        # Region similarity
        if trend1.region == trend2.region:
            score += 0.2
            factors += 1
        
        # Search volume similarity (if available)
        if trend1.search_volume and trend2.search_volume:
            vol1, vol2 = trend1.search_volume, trend2.search_volume
            max_vol = max(vol1, vol2)
            min_vol = min(vol1, vol2)
            volume_similarity = min_vol / max_vol if max_vol > 0 else 0
            score += volume_similarity * 0.2
            factors += 1
        
        # Growth rate similarity (if available)
        if trend1.growth_rate and trend2.growth_rate:
            rate1, rate2 = trend1.growth_rate, trend2.growth_rate
            if abs(rate1 - rate2) < 0.1:  # Within 10% difference
                score += 0.2
            factors += 1
        
        # Source diversity bonus (different sources are less likely to be duplicates)
        if trend1.source != trend2.source:
            score += 0.1
            factors += 1
        
        return score / factors if factors > 0 else 0.0
    
    def find_potential_duplicates(
        self, 
        trends: List[TrendData]
    ) -> List[DuplicationMatch]:
        """Find potential duplicate trends"""
        matches = []
        
        for i in range(len(trends)):
            for j in range(i + 1, len(trends)):
                trend1, trend2 = trends[i], trends[j]
                
                # Calculate different similarity scores
                exact_score = self.calculate_exact_similarity(trend1.keyword, trend2.keyword)
                fuzzy_score = self.calculate_fuzzy_similarity(trend1.keyword, trend2.keyword)
                semantic_score = self.calculate_semantic_similarity(trend1, trend2)
                
                # Determine match type and overall confidence
                match_type = "none"
                confidence = 0.0
                
                if exact_score >= self.exact_match_threshold:
                    match_type = "exact"
                    confidence = exact_score
                elif fuzzy_score >= self.fuzzy_match_threshold:
                    match_type = "fuzzy"
                    confidence = fuzzy_score
                elif semantic_score >= self.semantic_match_threshold:
                    match_type = "semantic"
                    confidence = semantic_score
                
                if match_type != "none":
                    # Combined similarity score
                    combined_score = (exact_score * 0.5) + (fuzzy_score * 0.3) + (semantic_score * 0.2)
                    
                    matches.append(DuplicationMatch(
                        trend1=trend1,
                        trend2=trend2,
                        similarity_score=combined_score,
                        match_type=match_type,
                        confidence=confidence
                    ))
        
        # Sort by confidence score (highest first)
        matches.sort(key=lambda m: m.confidence, reverse=True)
        
        return matches
    
    def resolve_duplicates(
        self, 
        trends: List[TrendData],
        strategy: str = "best_quality"
    ) -> List[TrendData]:
        """Resolve duplicates and return deduplicated list"""
        if not trends:
            return []
        
        matches = self.find_potential_duplicates(trends)
        
        if not matches:
            return trends
        
        # Group trends by similarity
        duplicate_groups = self._group_duplicates(trends, matches)
        
        # Resolve each group
        deduplicated = []
        processed_trends = set()
        
        for group in duplicate_groups:
            if any(id(trend) in processed_trends for trend in group):
                continue
            
            # Select best trend from group based on strategy
            best_trend = self._select_best_trend(group, strategy)
            deduplicated.append(best_trend)
            
            # Mark all trends in group as processed
            for trend in group:
                processed_trends.add(id(trend))
        
        # Add non-duplicate trends
        for trend in trends:
            if id(trend) not in processed_trends:
                deduplicated.append(trend)
        
        self.logger.info(
            f"Deduplication completed: {len(trends)} -> {len(deduplicated)} trends "
            f"({len(trends) - len(deduplicated)} duplicates removed)"
        )
        
        return deduplicated
    
    def _group_duplicates(
        self, 
        trends: List[TrendData], 
        matches: List[DuplicationMatch]
    ) -> List[List[TrendData]]:
        """Group trends that are duplicates of each other"""
        # Build adjacency list
        adjacency = defaultdict(set)
        for match in matches:
            id1, id2 = id(match.trend1), id(match.trend2)
            adjacency[id1].add(id2)
            adjacency[id2].add(id1)
        
        # Find connected components (groups of duplicates)
        visited = set()
        groups = []
        trend_by_id = {id(trend): trend for trend in trends}
        
        def dfs(trend_id, current_group):
            if trend_id in visited:
                return
            visited.add(trend_id)
            current_group.append(trend_by_id[trend_id])
            
            for neighbor_id in adjacency[trend_id]:
                dfs(neighbor_id, current_group)
        
        for trend in trends:
            trend_id = id(trend)
            if trend_id not in visited and trend_id in adjacency:
                group = []
                dfs(trend_id, group)
                if len(group) > 1:
                    groups.append(group)
        
        return groups
    
    def _select_best_trend(
        self, 
        trends: List[TrendData], 
        strategy: str
    ) -> TrendData:
        """Select the best trend from a group of duplicates"""
        if len(trends) == 1:
            return trends[0]
        
        if strategy == "best_quality":
            # Select trend with highest confidence score and most complete data
            def quality_score(trend):
                score = trend.confidence_score
                
                # Bonus for having search volume
                if trend.search_volume:
                    score += 0.1
                
                # Bonus for having growth rate
                if trend.growth_rate:
                    score += 0.1
                
                # Bonus for having category
                if trend.category:
                    score += 0.05
                
                # Bonus for metadata richness
                metadata_bonus = min(0.1, len(trend.metadata) * 0.01)
                score += metadata_bonus
                
                return score
            
            return max(trends, key=quality_score)
        
        elif strategy == "most_recent":
            return max(trends, key=lambda t: t.scraped_at)
        
        elif strategy == "highest_volume":
            trends_with_volume = [t for t in trends if t.search_volume]
            if trends_with_volume:
                return max(trends_with_volume, key=lambda t: t.search_volume)
            return trends[0]
        
        else:
            # Default: return first trend
            return trends[0]
    
    def get_deduplication_stats(
        self, 
        original_trends: List[TrendData], 
        deduplicated_trends: List[TrendData]
    ) -> Dict[str, Any]:
        """Get deduplication statistics"""
        duplicates_removed = len(original_trends) - len(deduplicated_trends)
        
        # Analyze by source
        source_stats = defaultdict(lambda: {'original': 0, 'final': 0})
        
        for trend in original_trends:
            source_stats[trend.source]['original'] += 1
        
        for trend in deduplicated_trends:
            source_stats[trend.source]['final'] += 1
        
        return {
            'original_count': len(original_trends),
            'deduplicated_count': len(deduplicated_trends),
            'duplicates_removed': duplicates_removed,
            'deduplication_rate': duplicates_removed / len(original_trends) if original_trends else 0,
            'source_breakdown': dict(source_stats),
            'thresholds': {
                'exact_match': self.exact_match_threshold,
                'fuzzy_match': self.fuzzy_match_threshold,
                'semantic_match': self.semantic_match_threshold
            }
        }


# Alias for backward compatibility
class FuzzyMatcher(TrendDeduplicator):
    """Backward compatibility alias for TrendDeduplicator"""

    def __init__(self, threshold: float = 0.85):
        config = {
            'fuzzy_match_threshold': threshold,
            'exact_match_threshold': 0.95,
            'semantic_match_threshold': 0.80
        }
        super().__init__(config)

    def deduplicate_trends(self, trends: List[TrendData]) -> List[TrendData]:
        """Deduplicate trends using fuzzy matching"""
        return self.resolve_duplicates(trends, strategy="best_quality")

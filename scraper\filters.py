"""
Trend filtering module
Provides filtering capabilities for trends based on various criteria
"""
import re
from typing import List, Set, Dict, Any, Optional
from datetime import datetime, timedelta

from .sources.base_scraper import TrendData
from shared.config import SCRAPER_CONFIG
from monitoring.logger import get_logger


class TrendFilter:
    """Filters trends based on configurable criteria"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = get_logger('scraper.filter')
        self.config = config or SCRAPER_CONFIG.get('filtering', {})
        
        # Default filter settings
        self.min_keyword_length = self.config.get('min_keyword_length', 3)
        self.max_keyword_length = self.config.get('max_keyword_length', 100)
        self.min_search_volume = self.config.get('min_search_volume', 0)
        self.min_growth_rate = self.config.get('min_growth_rate', -100)  # Allow negative growth
        
        # Blocked keywords and patterns
        self.blocked_keywords = set(self.config.get('blocked_keywords', [
            'porn', 'sex', 'nude', 'xxx', 'adult', 'nsfw',
            'gambling', 'casino', 'bet', 'lottery',
            'drugs', 'cocaine', 'marijuana', 'weed',
            'violence', 'kill', 'murder', 'death',
            'hate', 'racist', 'nazi', 'terrorism'
        ]))
        
        self.blocked_patterns = [
            re.compile(pattern, re.IGNORECASE) 
            for pattern in self.config.get('blocked_patterns', [
                r'\b(buy|sell|cheap|discount|sale)\b.*\b(online|now|today)\b',
                r'\b(click|visit|download)\b.*\b(here|now|link)\b',
                r'\b(free|win|prize|money)\b.*\b(now|today|click)\b'
            ])
        ]
        
        # Allowed regions and categories
        self.allowed_regions = set(self.config.get('allowed_regions', SCRAPER_CONFIG.get('regions', [])))
        self.allowed_categories = set(self.config.get('allowed_categories', SCRAPER_CONFIG.get('categories', [])))
        
        # Language detection patterns (basic)
        self.english_pattern = re.compile(r'^[a-zA-Z0-9\s\-_#@.,!?]+$')
        
    def filter_trends(self, trends: List[TrendData]) -> List[TrendData]:
        """Filter a list of trends based on all criteria"""
        filtered_trends = []
        
        for trend in trends:
            if self._should_include_trend(trend):
                filtered_trends.append(trend)
            else:
                self.logger.debug(
                    f"Filtered out trend: {trend.keyword}",
                    keyword=trend.keyword,
                    region=trend.region,
                    source=trend.source
                )
        
        self.logger.info(
            f"Filtered trends: {len(trends)} -> {len(filtered_trends)}",
            original_count=len(trends),
            filtered_count=len(filtered_trends),
            filter_rate=f"{((len(trends) - len(filtered_trends)) / len(trends) * 100):.1f}%" if trends else "0%"
        )
        
        return filtered_trends
    
    def _should_include_trend(self, trend: TrendData) -> bool:
        """Check if a trend should be included based on all filter criteria"""
        
        # Basic validation
        if not trend.keyword or not trend.keyword.strip():
            return False
        
        # Length filters
        if not self._check_keyword_length(trend.keyword):
            return False
        
        # Content filters
        if not self._check_content_safety(trend.keyword):
            return False
        
        # Language filter
        if not self._check_language(trend.keyword):
            return False
        
        # Region filter
        if not self._check_region(trend.region):
            return False
        
        # Category filter
        if not self._check_category(trend.category):
            return False
        
        # Metrics filters
        if not self._check_metrics(trend):
            return False
        
        # Quality filters
        if not self._check_quality(trend):
            return False
        
        return True
    
    def _check_keyword_length(self, keyword: str) -> bool:
        """Check if keyword length is within acceptable range"""
        length = len(keyword.strip())
        return self.min_keyword_length <= length <= self.max_keyword_length
    
    def _check_content_safety(self, keyword: str) -> bool:
        """Check if keyword contains blocked content"""
        keyword_lower = keyword.lower()
        
        # Check blocked keywords
        for blocked in self.blocked_keywords:
            if blocked in keyword_lower:
                return False
        
        # Check blocked patterns
        for pattern in self.blocked_patterns:
            if pattern.search(keyword):
                return False
        
        return True
    
    def _check_language(self, keyword: str) -> bool:
        """Check if keyword is in English (basic check)"""
        # Allow keywords that contain mostly English characters
        return bool(self.english_pattern.match(keyword))
    
    def _check_region(self, region: str) -> bool:
        """Check if region is allowed"""
        if not self.allowed_regions:
            return True  # No restrictions
        return region in self.allowed_regions
    
    def _check_category(self, category: Optional[str]) -> bool:
        """Check if category is allowed"""
        if not self.allowed_categories:
            return True  # No restrictions
        if not category:
            return True  # Allow trends without category
        return category in self.allowed_categories
    
    def _check_metrics(self, trend: TrendData) -> bool:
        """Check if trend metrics meet minimum requirements"""
        # Search volume check
        if trend.search_volume is not None and trend.search_volume < self.min_search_volume:
            return False
        
        # Growth rate check
        if trend.growth_rate is not None and trend.growth_rate < self.min_growth_rate:
            return False
        
        return True
    
    def _check_quality(self, trend: TrendData) -> bool:
        """Check trend quality indicators"""
        keyword = trend.keyword.strip()
        
        # Reject very short keywords
        if len(keyword) < 2:
            return False
        
        # Reject keywords that are just numbers
        if keyword.isdigit():
            return False
        
        # Reject keywords with too many special characters
        special_char_count = sum(1 for c in keyword if not c.isalnum() and c != ' ')
        if special_char_count > len(keyword) * 0.3:  # More than 30% special chars
            return False
        
        # Reject keywords with excessive repetition
        if self._has_excessive_repetition(keyword):
            return False
        
        # Reject obvious spam patterns
        if self._is_spam_pattern(keyword):
            return False
        
        return True
    
    def _has_excessive_repetition(self, keyword: str) -> bool:
        """Check for excessive character or word repetition"""
        # Check for repeated characters (more than 3 in a row)
        if re.search(r'(.)\1{3,}', keyword):
            return True
        
        # Check for repeated words
        words = keyword.lower().split()
        if len(words) > 1:
            word_counts = {}
            for word in words:
                word_counts[word] = word_counts.get(word, 0) + 1
                if word_counts[word] > 2:  # Same word appears more than twice
                    return True
        
        return False
    
    def _is_spam_pattern(self, keyword: str) -> bool:
        """Check for common spam patterns"""
        spam_patterns = [
            r'\b(click|visit|check|see|watch)\s+(here|this|now|link)\b',
            r'\b(free|win|get|earn)\s+(money|cash|prize|gift)\b',
            r'\b(buy|order|purchase)\s+(now|today|online)\b',
            r'\b(limited|special|exclusive)\s+(offer|deal|discount)\b'
        ]
        
        for pattern in spam_patterns:
            if re.search(pattern, keyword, re.IGNORECASE):
                return True
        
        return False
    
    def filter_by_region(self, trends: List[TrendData], regions: List[str]) -> List[TrendData]:
        """Filter trends by specific regions"""
        return [trend for trend in trends if trend.region in regions]
    
    def filter_by_category(self, trends: List[TrendData], categories: List[str]) -> List[TrendData]:
        """Filter trends by specific categories"""
        return [trend for trend in trends if trend.category in categories]
    
    def filter_by_source(self, trends: List[TrendData], sources: List[str]) -> List[TrendData]:
        """Filter trends by specific sources"""
        return [trend for trend in trends if trend.source in sources]
    
    def filter_by_time_range(
        self, 
        trends: List[TrendData], 
        start_time: datetime, 
        end_time: datetime
    ) -> List[TrendData]:
        """Filter trends by time range"""
        return [
            trend for trend in trends 
            if start_time <= trend.scraped_at <= end_time
        ]
    
    def get_filter_stats(self, original_trends: List[TrendData], filtered_trends: List[TrendData]) -> Dict[str, Any]:
        """Get statistics about filtering results"""
        total_original = len(original_trends)
        total_filtered = len(filtered_trends)
        filtered_out = total_original - total_filtered
        
        stats = {
            'original_count': total_original,
            'filtered_count': total_filtered,
            'filtered_out_count': filtered_out,
            'filter_rate': (filtered_out / total_original * 100) if total_original > 0 else 0,
            'by_source': {},
            'by_region': {},
            'by_category': {}
        }
        
        # Count by source
        for trend in filtered_trends:
            stats['by_source'][trend.source] = stats['by_source'].get(trend.source, 0) + 1
            stats['by_region'][trend.region] = stats['by_region'].get(trend.region, 0) + 1
            if trend.category:
                stats['by_category'][trend.category] = stats['by_category'].get(trend.category, 0) + 1
        
        return stats

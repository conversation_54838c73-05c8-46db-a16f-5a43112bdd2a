"""
Trend scoring module
Calculates comprehensive scores for trends based on multiple factors
"""
import math
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from .sources.base_scraper import TrendData
from shared.config import SCRAPER_CONFIG
from monitoring.logger import get_logger


class TrendScorer:
    """Calculates comprehensive scores for trends"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = get_logger('scraper.scorer')
        self.config = config or SCRAPER_CONFIG.get('scoring', {})
        
        # Scoring weights
        self.search_volume_weight = self.config.get('search_volume_weight', 0.4)
        self.growth_rate_weight = self.config.get('growth_rate_weight', 0.3)
        self.confidence_weight = self.config.get('confidence_weight', 0.1)
        self.recency_weight = self.config.get('recency_weight', 0.1)
        self.source_weight = self.config.get('source_weight', 0.1)
        
        # Normalization parameters
        self.max_search_volume = self.config.get('max_search_volume_for_scoring', 10000)
        self.max_growth_rate = self.config.get('max_growth_rate_for_scoring', 500)
        
        # Source reliability scores
        self.source_scores = self.config.get('source_scores', {
            'google_trends': 1.0,
            'trends24': 0.8,
            'custom': 0.6
        })
        
        # Category multipliers
        self.category_multipliers = self.config.get('category_multipliers', {
            'Technology': 1.2,
            'Health': 1.1,
            'Entertainment': 1.0,
            'Sports': 0.9,
            'Politics': 0.8,
            'Business': 1.1
        })
    
    def calculate_score(self, trend: TrendData) -> float:
        """Calculate comprehensive score for a trend"""
        try:
            # Calculate individual component scores
            volume_score = self._calculate_volume_score(trend.search_volume)
            growth_score = self._calculate_growth_score(trend.growth_rate)
            confidence_score = self._calculate_confidence_score(trend.confidence_score)
            recency_score = self._calculate_recency_score(trend.scraped_at)
            source_score = self._calculate_source_score(trend.source)
            
            # Calculate weighted total
            total_score = (
                volume_score * self.search_volume_weight +
                growth_score * self.growth_rate_weight +
                confidence_score * self.confidence_weight +
                recency_score * self.recency_weight +
                source_score * self.source_weight
            )
            
            # Apply category multiplier
            category_multiplier = self.category_multipliers.get(trend.category, 1.0)
            total_score *= category_multiplier
            
            # Apply quality adjustments
            quality_adjustment = self._calculate_quality_adjustment(trend)
            total_score *= quality_adjustment
            
            # Normalize to 0-100 scale
            final_score = min(100, max(0, total_score * 100))
            
            self.logger.debug(
                f"Calculated score for trend: {trend.keyword}",
                keyword=trend.keyword,
                volume_score=volume_score,
                growth_score=growth_score,
                confidence_score=confidence_score,
                recency_score=recency_score,
                source_score=source_score,
                category_multiplier=category_multiplier,
                quality_adjustment=quality_adjustment,
                final_score=final_score
            )
            
            return round(final_score, 2)
            
        except Exception as e:
            self.logger.error(f"Error calculating score for trend {trend.keyword}: {str(e)}")
            return 0.0
    
    def _calculate_volume_score(self, search_volume: Optional[int]) -> float:
        """Calculate normalized score for search volume"""
        if search_volume is None or search_volume <= 0:
            return 0.0
        
        # Use logarithmic scaling for search volume
        normalized_volume = min(search_volume / self.max_search_volume, 1.0)
        
        # Apply logarithmic transformation to reduce impact of very high volumes
        if normalized_volume > 0:
            score = math.log10(normalized_volume * 9 + 1)  # log10(1) to log10(10)
        else:
            score = 0.0
        
        return min(1.0, score)
    
    def _calculate_growth_score(self, growth_rate: Optional[float]) -> float:
        """Calculate normalized score for growth rate"""
        if growth_rate is None:
            return 0.5  # Neutral score for missing data
        
        # Handle negative growth (decline)
        if growth_rate < 0:
            # Penalize decline but not completely
            return max(0.0, 0.5 + (growth_rate / 200))  # -100% growth = 0, 0% growth = 0.5
        
        # Positive growth
        normalized_growth = min(growth_rate / self.max_growth_rate, 1.0)
        
        # Use square root to reduce impact of extreme growth rates
        score = math.sqrt(normalized_growth)
        
        return min(1.0, score)
    
    def _calculate_confidence_score(self, confidence: Optional[float]) -> float:
        """Calculate score based on confidence level"""
        if confidence is None:
            return 0.5  # Neutral score for missing confidence
        
        # Confidence should already be 0-1, but ensure it's normalized
        return max(0.0, min(1.0, confidence))
    
    def _calculate_recency_score(self, scraped_at: Optional[datetime]) -> float:
        """Calculate score based on how recent the trend is"""
        if scraped_at is None:
            return 0.5  # Neutral score for missing timestamp
        
        now = datetime.utcnow()
        age_hours = (now - scraped_at).total_seconds() / 3600
        
        # Trends are most valuable when fresh
        if age_hours <= 1:
            return 1.0  # Perfect score for very recent trends
        elif age_hours <= 6:
            return 0.9  # High score for recent trends
        elif age_hours <= 24:
            return 0.7  # Good score for trends within a day
        elif age_hours <= 72:
            return 0.5  # Moderate score for trends within 3 days
        else:
            # Exponential decay for older trends
            decay_factor = math.exp(-(age_hours - 72) / 168)  # 168 hours = 1 week
            return max(0.1, 0.5 * decay_factor)
    
    def _calculate_source_score(self, source: str) -> float:
        """Calculate score based on source reliability"""
        return self.source_scores.get(source, 0.5)  # Default to moderate score
    
    def _calculate_quality_adjustment(self, trend: TrendData) -> float:
        """Calculate quality adjustment factor"""
        adjustment = 1.0
        
        keyword = trend.keyword.strip() if trend.keyword else ""
        
        # Keyword length adjustment
        if len(keyword) < 5:
            adjustment *= 0.9  # Slight penalty for very short keywords
        elif len(keyword) > 50:
            adjustment *= 0.95  # Slight penalty for very long keywords
        
        # Check for special characters (hashtags, mentions)
        if keyword.startswith('#'):
            adjustment *= 1.1  # Bonus for hashtags (likely trending topics)
        elif keyword.startswith('@'):
            adjustment *= 0.9  # Slight penalty for mentions
        
        # Check for numbers (might indicate specific events)
        if any(char.isdigit() for char in keyword):
            adjustment *= 1.05  # Small bonus for keywords with numbers
        
        # Check for all caps (might be spam or very trending)
        if keyword.isupper() and len(keyword) > 3:
            adjustment *= 0.8  # Penalty for all caps (often spam)
        
        # Check metadata quality
        if trend.metadata:
            metadata_richness = len(trend.metadata)
            if metadata_richness > 5:
                adjustment *= 1.05  # Bonus for rich metadata
            elif metadata_richness < 2:
                adjustment *= 0.95  # Penalty for poor metadata
        
        return max(0.1, min(2.0, adjustment))  # Clamp between 0.1 and 2.0
    
    def calculate_batch_scores(self, trends: List[TrendData]) -> Dict[str, Any]:
        """Calculate scores for a batch of trends and return statistics"""
        scores = []
        score_distribution = {
            'excellent': 0,    # 80-100
            'good': 0,         # 60-79
            'moderate': 0,     # 40-59
            'poor': 0,         # 20-39
            'very_poor': 0     # 0-19
        }
        
        for trend in trends:
            score = self.calculate_score(trend)
            scores.append(score)
            
            # Categorize score
            if score >= 80:
                score_distribution['excellent'] += 1
            elif score >= 60:
                score_distribution['good'] += 1
            elif score >= 40:
                score_distribution['moderate'] += 1
            elif score >= 20:
                score_distribution['poor'] += 1
            else:
                score_distribution['very_poor'] += 1
        
        if scores:
            avg_score = sum(scores) / len(scores)
            max_score = max(scores)
            min_score = min(scores)
        else:
            avg_score = max_score = min_score = 0
        
        return {
            'total_trends': len(trends),
            'average_score': round(avg_score, 2),
            'max_score': round(max_score, 2),
            'min_score': round(min_score, 2),
            'score_distribution': score_distribution,
            'scores': scores
        }
    
    def get_top_trends(self, trends: List[TrendData], limit: int = 10) -> List[Dict[str, Any]]:
        """Get top-scoring trends with their scores"""
        trend_scores = []
        
        for trend in trends:
            score = self.calculate_score(trend)
            trend_scores.append({
                'trend': trend,
                'score': score,
                'keyword': trend.keyword,
                'region': trend.region,
                'category': trend.category,
                'source': trend.source
            })
        
        # Sort by score descending
        trend_scores.sort(key=lambda x: x['score'], reverse=True)
        
        return trend_scores[:limit]
    
    def get_scoring_config(self) -> Dict[str, Any]:
        """Get current scoring configuration"""
        return {
            'weights': {
                'search_volume': self.search_volume_weight,
                'growth_rate': self.growth_rate_weight,
                'confidence': self.confidence_weight,
                'recency': self.recency_weight,
                'source': self.source_weight
            },
            'normalization': {
                'max_search_volume': self.max_search_volume,
                'max_growth_rate': self.max_growth_rate
            },
            'source_scores': self.source_scores,
            'category_multipliers': self.category_multipliers
        }
    
    def update_scoring_config(self, new_config: Dict[str, Any]):
        """Update scoring configuration"""
        if 'weights' in new_config:
            weights = new_config['weights']
            for key, value in weights.items():
                if hasattr(self, f"{key}_weight"):
                    setattr(self, f"{key}_weight", value)
                    self.logger.info(f"Updated weight: {key}_weight = {value}")
        
        if 'source_scores' in new_config:
            self.source_scores.update(new_config['source_scores'])
            self.logger.info("Updated source scores")
        
        if 'category_multipliers' in new_config:
            self.category_multipliers.update(new_config['category_multipliers'])
            self.logger.info("Updated category multipliers")

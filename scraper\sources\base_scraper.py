"""
Base scraper abstraction for trend data sources
Provides common interface and functionality for all scrapers
"""
import asyncio
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from monitoring.logger import get_logger
from shared.exceptions import ScrapingError


class ScrapingStatus(str, Enum):
    """Scraping operation status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RATE_LIMITED = "rate_limited"


@dataclass
class TrendData:
    """Standardized trend data structure"""
    keyword: str
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    region: str = "US"
    category: Optional[str] = None
    source: str = "unknown"
    confidence_score: float = 0.0
    metadata: Dict[str, Any] = None
    scraped_at: datetime = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.scraped_at is None:
            self.scraped_at = datetime.utcnow()


@dataclass
class ScrapingResult:
    """Result of a scraping operation"""
    status: ScrapingStatus
    trends: List[TrendData]
    total_found: int
    processing_time: float
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseScraper(ABC):
    """Abstract base class for all trend scrapers"""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.logger = get_logger(f'scraper.{name}')
        self.rate_limit_delay = self.config.get('rate_limit_delay', 1.0)
        self.max_retries = self.config.get('max_retries', 3)
        self.timeout = self.config.get('timeout', 30)
        self._last_request_time = None
        self._request_count = 0
        self._session = None
    
    @abstractmethod
    async def scrape_trends(
        self, 
        region: str = "US", 
        category: Optional[str] = None,
        limit: int = 50
    ) -> ScrapingResult:
        """
        Scrape trends from the data source
        
        Args:
            region: Geographic region code (e.g., "US", "UK", "CA")
            category: Trend category filter
            limit: Maximum number of trends to return
            
        Returns:
            ScrapingResult with trends and metadata
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """
        Validate connection to the data source
        
        Returns:
            True if connection is valid, False otherwise
        """
        pass
    
    @abstractmethod
    def get_supported_regions(self) -> List[str]:
        """
        Get list of supported regions
        
        Returns:
            List of region codes supported by this scraper
        """
        pass
    
    @abstractmethod
    def get_supported_categories(self) -> List[str]:
        """
        Get list of supported categories
        
        Returns:
            List of categories supported by this scraper
        """
        pass
    
    async def _enforce_rate_limit(self):
        """Enforce rate limiting between requests"""
        if self._last_request_time:
            elapsed = (datetime.utcnow() - self._last_request_time).total_seconds()
            if elapsed < self.rate_limit_delay:
                sleep_time = self.rate_limit_delay - elapsed
                self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f}s")
                await asyncio.sleep(sleep_time)
        
        self._last_request_time = datetime.utcnow()
        self._request_count += 1
    
    async def _retry_on_failure(self, operation, *args, **kwargs):
        """Retry operation on failure with exponential backoff"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return await operation(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    backoff_time = 2 ** attempt
                    self.logger.warning(
                        f"Attempt {attempt + 1} failed, retrying in {backoff_time}s: {str(e)}"
                    )
                    await asyncio.sleep(backoff_time)
                else:
                    self.logger.error(f"All {self.max_retries + 1} attempts failed")
        
        raise last_exception
    
    def _normalize_keyword(self, keyword: str) -> str:
        """Normalize keyword for consistency"""
        if not keyword:
            return ""
        
        # Basic normalization
        normalized = keyword.strip().lower()
        
        # Remove extra whitespace
        normalized = ' '.join(normalized.split())
        
        # Remove special characters that might cause issues
        import re
        normalized = re.sub(r'[^\w\s-]', '', normalized)
        
        return normalized
    
    def _calculate_confidence_score(self, trend_data: Dict[str, Any]) -> float:
        """Calculate confidence score for trend data"""
        score = 0.0
        
        # Base score for having data
        if trend_data.get('keyword'):
            score += 0.3
        
        # Score for search volume data
        if trend_data.get('search_volume') is not None:
            score += 0.3
        
        # Score for growth rate data
        if trend_data.get('growth_rate') is not None:
            score += 0.2
        
        # Score for category information
        if trend_data.get('category'):
            score += 0.1
        
        # Score for metadata richness
        metadata_count = len(trend_data.get('metadata', {}))
        score += min(0.1, metadata_count * 0.02)
        
        return min(1.0, score)
    
    def _create_trend_data(
        self,
        keyword: str,
        search_volume: Optional[int] = None,
        growth_rate: Optional[float] = None,
        region: str = "US",
        category: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> TrendData:
        """Create standardized TrendData object"""
        
        normalized_keyword = self._normalize_keyword(keyword)
        if not normalized_keyword:
            raise ValueError("Invalid keyword provided")
        
        trend_metadata = metadata or {}
        trend_metadata.update({
            'scraper': self.name,
            'scraped_at': datetime.utcnow().isoformat(),
            'request_count': self._request_count
        })
        
        confidence_score = self._calculate_confidence_score({
            'keyword': normalized_keyword,
            'search_volume': search_volume,
            'growth_rate': growth_rate,
            'category': category,
            'metadata': trend_metadata
        })
        
        return TrendData(
            keyword=normalized_keyword,
            search_volume=search_volume,
            growth_rate=growth_rate,
            region=region,
            category=category,
            source=self.name,
            confidence_score=confidence_score,
            metadata=trend_metadata,
            scraped_at=datetime.utcnow()
        )
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the scraper"""
        try:
            connection_valid = await self.validate_connection()
            
            return {
                'name': self.name,
                'status': 'healthy' if connection_valid else 'unhealthy',
                'connection_valid': connection_valid,
                'request_count': self._request_count,
                'last_request_time': self._last_request_time.isoformat() if self._last_request_time else None,
                'supported_regions': self.get_supported_regions(),
                'supported_categories': self.get_supported_categories(),
                'config': {
                    'rate_limit_delay': self.rate_limit_delay,
                    'max_retries': self.max_retries,
                    'timeout': self.timeout
                }
            }
        except Exception as e:
            return {
                'name': self.name,
                'status': 'error',
                'error': str(e),
                'connection_valid': False
            }
    
    async def cleanup(self):
        """Cleanup resources"""
        if self._session:
            await self._session.close()
            self._session = None
        
        self.logger.info(f"Scraper {self.name} cleaned up")
    
    def __str__(self):
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self):
        return f"{self.__class__.__name__}(name='{self.name}', config={self.config})"


class ScraperRegistry:
    """Registry for managing multiple scrapers"""
    
    def __init__(self):
        self._scrapers: Dict[str, BaseScraper] = {}
        self.logger = get_logger('scraper.registry')
    
    def register(self, scraper: BaseScraper):
        """Register a scraper"""
        self._scrapers[scraper.name] = scraper
        self.logger.info(f"Registered scraper: {scraper.name}")
    
    def unregister(self, name: str):
        """Unregister a scraper"""
        if name in self._scrapers:
            del self._scrapers[name]
            self.logger.info(f"Unregistered scraper: {name}")
    
    def get_scraper(self, name: str) -> Optional[BaseScraper]:
        """Get scraper by name"""
        return self._scrapers.get(name)
    
    def get_all_scrapers(self) -> List[BaseScraper]:
        """Get all registered scrapers"""
        return list(self._scrapers.values())
    
    def get_scraper_names(self) -> List[str]:
        """Get names of all registered scrapers"""
        return list(self._scrapers.keys())
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all scrapers"""
        status = {
            'total_scrapers': len(self._scrapers),
            'scrapers': {}
        }
        
        for name, scraper in self._scrapers.items():
            try:
                scraper_status = await scraper.get_health_status()
                status['scrapers'][name] = scraper_status
            except Exception as e:
                status['scrapers'][name] = {
                    'name': name,
                    'status': 'error',
                    'error': str(e)
                }
        
        return status
    
    async def cleanup_all(self):
        """Cleanup all scrapers"""
        for scraper in self._scrapers.values():
            try:
                await scraper.cleanup()
            except Exception as e:
                self.logger.error(f"Error cleaning up scraper {scraper.name}: {str(e)}")


# Global scraper registry
scraper_registry = ScraperRegistry()

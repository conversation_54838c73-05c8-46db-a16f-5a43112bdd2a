"""
Custom scrapers for additional trend data sources
Implements specific scrapers for various trend platforms
"""
import asyncio
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import aiohttp
from bs4 import BeautifulSoup

from scraper.sources.base_scraper import BaseScraper, TrendData, ScrapingResult, ScrapingStatus
from scraper.utils.proxy_rotation import ProxySession, ProxyRotator
from monitoring.logger import get_logger
from shared.exceptions import ScrapingError


class Trends24Scraper(BaseScraper):
    """Scraper for Trends24.in website"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__('trends24', config)
        self.base_url = "https://trends24.in"
        self.proxy_session = None
        
        if self.config.get('proxy_required') and self.config.get('proxy_rotator'):
            self.proxy_session = ProxySession(self.config['proxy_rotator'])
    
    async def scrape_trends(
        self, 
        region: str = "US", 
        category: Optional[str] = None,
        limit: int = 50
    ) -> ScrapingResult:
        """Scrape trends from Trends24"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self._enforce_rate_limit()
            
            # Map region to Trends24 format
            region_map = {
                'US': 'united-states',
                'UK': 'united-kingdom',
                'CA': 'canada',
                'AU': 'australia',
                'DE': 'germany',
                'FR': 'france'
            }
            
            region_slug = region_map.get(region, 'united-states')
            url = f"{self.base_url}/{region_slug}"
            
            trends = await self._fetch_trends_from_page(url, region, category, limit)
            
            processing_time = asyncio.get_event_loop().time() - start_time
            
            self.logger.info(
                f"Successfully scraped {len(trends)} trends from Trends24",
                region=region,
                category=category,
                processing_time=processing_time
            )
            
            return ScrapingResult(
                status=ScrapingStatus.COMPLETED,
                trends=trends,
                total_found=len(trends),
                processing_time=processing_time,
                metadata={
                    'source': 'trends24',
                    'region': region,
                    'category': category,
                    'url': url
                }
            )
            
        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"Failed to scrape Trends24: {str(e)}")
            
            return ScrapingResult(
                status=ScrapingStatus.FAILED,
                trends=[],
                total_found=0,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    async def _fetch_trends_from_page(
        self, 
        url: str, 
        region: str, 
        category: Optional[str], 
        limit: int
    ) -> List[TrendData]:
        """Fetch and parse trends from Trends24 page"""
        trends = []
        
        try:
            if self.proxy_session:
                response = await self.proxy_session.get(url)
            else:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=self.timeout)) as response:
                        if response.status != 200:
                            raise ScrapingError(f"HTTP {response.status} from Trends24")
                        
                        html_content = await response.text()
            
            # Parse HTML content
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find trend elements (this would need to be updated based on actual HTML structure)
            trend_elements = soup.find_all('div', class_='trend-item')  # Example selector
            
            for element in trend_elements[:limit]:
                try:
                    keyword = self._extract_keyword(element)
                    if not keyword:
                        continue
                    
                    # Extract additional data if available
                    search_volume = self._extract_search_volume(element)
                    growth_rate = self._extract_growth_rate(element)
                    
                    # Create trend data
                    trend = self._create_trend_data(
                        keyword=keyword,
                        search_volume=search_volume,
                        growth_rate=growth_rate,
                        region=region,
                        category=category,
                        metadata={
                            'source_url': url,
                            'element_html': str(element)[:200]  # First 200 chars for debugging
                        }
                    )
                    
                    trends.append(trend)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to parse trend element: {str(e)}")
                    continue
            
        except Exception as e:
            raise ScrapingError(f"Failed to fetch trends from {url}: {str(e)}")
        
        return trends
    
    def _extract_keyword(self, element) -> Optional[str]:
        """Extract keyword from HTML element"""
        # This would need to be implemented based on actual HTML structure
        keyword_elem = element.find('span', class_='trend-keyword')
        return keyword_elem.get_text(strip=True) if keyword_elem else None
    
    def _extract_search_volume(self, element) -> Optional[int]:
        """Extract search volume from HTML element"""
        # This would need to be implemented based on actual HTML structure
        volume_elem = element.find('span', class_='search-volume')
        if volume_elem:
            try:
                volume_text = volume_elem.get_text(strip=True)
                # Parse volume (e.g., "100K" -> 100000)
                return self._parse_volume_string(volume_text)
            except:
                pass
        return None
    
    def _extract_growth_rate(self, element) -> Optional[float]:
        """Extract growth rate from HTML element"""
        # This would need to be implemented based on actual HTML structure
        growth_elem = element.find('span', class_='growth-rate')
        if growth_elem:
            try:
                growth_text = growth_elem.get_text(strip=True)
                # Parse growth rate (e.g., "+15%" -> 0.15)
                return self._parse_growth_string(growth_text)
            except:
                pass
        return None
    
    def _parse_volume_string(self, volume_str: str) -> Optional[int]:
        """Parse volume string to integer"""
        if not volume_str:
            return None
        
        volume_str = volume_str.upper().replace(',', '')
        
        try:
            if 'K' in volume_str:
                return int(float(volume_str.replace('K', '')) * 1000)
            elif 'M' in volume_str:
                return int(float(volume_str.replace('M', '')) * 1000000)
            else:
                return int(volume_str)
        except:
            return None
    
    def _parse_growth_string(self, growth_str: str) -> Optional[float]:
        """Parse growth string to float"""
        if not growth_str:
            return None
        
        try:
            # Remove % and + signs
            clean_str = growth_str.replace('%', '').replace('+', '')
            return float(clean_str) / 100.0
        except:
            return None
    
    async def validate_connection(self) -> bool:
        """Validate connection to Trends24"""
        try:
            if self.proxy_session:
                response = await self.proxy_session.get(self.base_url)
                return response.status == 200
            else:
                async with aiohttp.ClientSession() as session:
                    async with session.get(self.base_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        return response.status == 200
        except:
            return False
    
    def get_supported_regions(self) -> List[str]:
        """Get supported regions"""
        return ['US', 'UK', 'CA', 'AU', 'DE', 'FR']
    
    def get_supported_categories(self) -> List[str]:
        """Get supported categories"""
        return ['Technology', 'Entertainment', 'Sports', 'Business']


class TwitterTrendsScraper(BaseScraper):
    """Scraper for Twitter Trends API"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__('twitter', config)
        self.api_key = self.config.get('api_key')
        self.base_url = "https://api.twitter.com/1.1/trends"
        
        if not self.api_key:
            raise ScrapingError("Twitter API key is required")
    
    async def scrape_trends(
        self, 
        region: str = "US", 
        category: Optional[str] = None,
        limit: int = 50
    ) -> ScrapingResult:
        """Scrape trends from Twitter API"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self._enforce_rate_limit()
            
            # Map region to Twitter WOEID (Where On Earth ID)
            woeid_map = {
                'US': 23424977,
                'UK': 23424975,
                'CA': 23424775,
                'AU': 23424748
            }
            
            woeid = woeid_map.get(region, 23424977)  # Default to US
            
            trends = await self._fetch_trends_from_api(woeid, region, category, limit)
            
            processing_time = asyncio.get_event_loop().time() - start_time
            
            self.logger.info(
                f"Successfully scraped {len(trends)} trends from Twitter",
                region=region,
                category=category,
                processing_time=processing_time
            )
            
            return ScrapingResult(
                status=ScrapingStatus.COMPLETED,
                trends=trends,
                total_found=len(trends),
                processing_time=processing_time,
                metadata={
                    'source': 'twitter',
                    'region': region,
                    'category': category,
                    'woeid': woeid
                }
            )
            
        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"Failed to scrape Twitter trends: {str(e)}")
            
            return ScrapingResult(
                status=ScrapingStatus.FAILED,
                trends=[],
                total_found=0,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    async def _fetch_trends_from_api(
        self, 
        woeid: int, 
        region: str, 
        category: Optional[str], 
        limit: int
    ) -> List[TrendData]:
        """Fetch trends from Twitter API"""
        trends = []
        
        try:
            url = f"{self.base_url}/place.json"
            params = {'id': woeid}
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, 
                    params=params, 
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    if response.status != 200:
                        raise ScrapingError(f"Twitter API returned HTTP {response.status}")
                    
                    data = await response.json()
            
            # Parse Twitter trends response
            if data and len(data) > 0 and 'trends' in data[0]:
                trend_list = data[0]['trends']
                
                for trend_item in trend_list[:limit]:
                    try:
                        keyword = trend_item.get('name', '').strip()
                        if not keyword or keyword.startswith('#'):
                            continue  # Skip hashtags for now
                        
                        # Extract volume if available
                        search_volume = trend_item.get('tweet_volume')
                        
                        # Create trend data
                        trend = self._create_trend_data(
                            keyword=keyword,
                            search_volume=search_volume,
                            region=region,
                            category=category,
                            metadata={
                                'twitter_url': trend_item.get('url'),
                                'promoted_content': trend_item.get('promoted_content'),
                                'query': trend_item.get('query')
                            }
                        )
                        
                        trends.append(trend)
                        
                    except Exception as e:
                        self.logger.warning(f"Failed to parse Twitter trend: {str(e)}")
                        continue
            
        except Exception as e:
            raise ScrapingError(f"Failed to fetch Twitter trends: {str(e)}")
        
        return trends
    
    async def validate_connection(self) -> bool:
        """Validate connection to Twitter API"""
        try:
            url = f"{self.base_url}/available.json"
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    return response.status == 200
        except:
            return False
    
    def get_supported_regions(self) -> List[str]:
        """Get supported regions"""
        return ['US', 'UK', 'CA', 'AU']
    
    def get_supported_categories(self) -> List[str]:
        """Get supported categories"""
        return ['Technology', 'Entertainment', 'Sports']


class RedditTrendsScraper(BaseScraper):
    """Scraper for Reddit trending topics"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__('reddit', config)
        self.base_url = "https://www.reddit.com"
    
    async def scrape_trends(
        self, 
        region: str = "US", 
        category: Optional[str] = None,
        limit: int = 50
    ) -> ScrapingResult:
        """Scrape trending topics from Reddit"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self._enforce_rate_limit()
            
            # Reddit doesn't have region-specific trends, but we can scrape popular subreddits
            subreddits = self._get_category_subreddits(category)
            trends = []
            
            for subreddit in subreddits:
                subreddit_trends = await self._fetch_subreddit_trends(subreddit, region, category)
                trends.extend(subreddit_trends)
                
                if len(trends) >= limit:
                    break
            
            # Limit results
            trends = trends[:limit]
            
            processing_time = asyncio.get_event_loop().time() - start_time
            
            self.logger.info(
                f"Successfully scraped {len(trends)} trends from Reddit",
                region=region,
                category=category,
                processing_time=processing_time
            )
            
            return ScrapingResult(
                status=ScrapingStatus.COMPLETED,
                trends=trends,
                total_found=len(trends),
                processing_time=processing_time,
                metadata={
                    'source': 'reddit',
                    'region': region,
                    'category': category,
                    'subreddits': subreddits
                }
            )
            
        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"Failed to scrape Reddit trends: {str(e)}")
            
            return ScrapingResult(
                status=ScrapingStatus.FAILED,
                trends=[],
                total_found=0,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    def _get_category_subreddits(self, category: Optional[str]) -> List[str]:
        """Get relevant subreddits for category"""
        category_map = {
            'Technology': ['technology', 'programming', 'artificial', 'gadgets'],
            'Entertainment': ['movies', 'television', 'music', 'gaming'],
            'Sports': ['sports', 'nfl', 'nba', 'soccer'],
            'Business': ['business', 'investing', 'entrepreneur', 'stocks']
        }
        
        if category and category in category_map:
            return category_map[category]
        
        # Default popular subreddits
        return ['popular', 'all', 'worldnews', 'technology']
    
    async def _fetch_subreddit_trends(
        self, 
        subreddit: str, 
        region: str, 
        category: Optional[str]
    ) -> List[TrendData]:
        """Fetch trending posts from a subreddit"""
        trends = []
        
        try:
            url = f"{self.base_url}/r/{subreddit}/hot.json"
            params = {'limit': 10}
            
            headers = {
                'User-Agent': 'TrendSite-Bot/1.0'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, 
                    params=params, 
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    if response.status != 200:
                        self.logger.warning(f"Reddit returned HTTP {response.status} for r/{subreddit}")
                        return trends
                    
                    data = await response.json()
            
            # Parse Reddit response
            if 'data' in data and 'children' in data['data']:
                posts = data['data']['children']
                
                for post in posts:
                    try:
                        post_data = post.get('data', {})
                        title = post_data.get('title', '').strip()
                        
                        if not title or len(title) < 10:
                            continue
                        
                        # Extract keywords from title
                        keywords = self._extract_keywords_from_title(title)
                        
                        for keyword in keywords:
                            if len(keyword) >= 3:  # Minimum keyword length
                                trend = self._create_trend_data(
                                    keyword=keyword,
                                    search_volume=post_data.get('score'),  # Use upvotes as proxy
                                    region=region,
                                    category=category,
                                    metadata={
                                        'reddit_url': post_data.get('permalink'),
                                        'subreddit': subreddit,
                                        'post_title': title,
                                        'upvotes': post_data.get('score'),
                                        'comments': post_data.get('num_comments')
                                    }
                                )
                                trends.append(trend)
                        
                    except Exception as e:
                        self.logger.warning(f"Failed to parse Reddit post: {str(e)}")
                        continue
            
        except Exception as e:
            self.logger.warning(f"Failed to fetch from r/{subreddit}: {str(e)}")
        
        return trends
    
    def _extract_keywords_from_title(self, title: str) -> List[str]:
        """Extract potential trending keywords from post title"""
        # Simple keyword extraction - could be enhanced with NLP
        import re
        
        # Remove common words and extract meaningful terms
        words = re.findall(r'\b[A-Za-z]{3,}\b', title.lower())
        
        # Filter out common words
        stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'what', 'when', 'with', 'have', 'this', 'will', 'your', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'}
        
        keywords = [word for word in words if word not in stop_words and len(word) >= 3]
        
        return keywords[:3]  # Return top 3 keywords
    
    async def validate_connection(self) -> bool:
        """Validate connection to Reddit"""
        try:
            url = f"{self.base_url}/r/popular.json"
            headers = {'User-Agent': 'TrendSite-Bot/1.0'}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    return response.status == 200
        except:
            return False
    
    def get_supported_regions(self) -> List[str]:
        """Get supported regions"""
        return ['US']  # Reddit is primarily US-focused
    
    def get_supported_categories(self) -> List[str]:
        """Get supported categories"""
        return ['Technology', 'Entertainment', 'Sports', 'Business']

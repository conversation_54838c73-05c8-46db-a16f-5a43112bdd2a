"""
Google Trends scraper implementation
Extracts trending topics from Google Trends using pytrends library
"""
import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from pytrends.request import TrendReq

from .base_scraper import BaseScraper, TrendData, ScrapingResult, ScrapingStatus
from shared.config import SCRAPER_CONFIG
from shared.exceptions import ScrapingError
from monitoring.logger import get_logger


class GoogleTrendsScraper(BaseScraper):
    """Google Trends scraper using pytrends library"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("google_trends", config)
        self.pytrends = None
        self.last_request_time = 0
        
        # Get rate limiting config
        rate_config = SCRAPER_CONFIG['rate_limiting'].get('google_trends', {})
        self.requests_per_minute = rate_config.get('requests_per_minute', 10)
        self.request_delay = 60 / self.requests_per_minute
        
        # Category mapping
        self.category_map = {
            'Technology': 5,
            'Health': 45,
            'Entertainment': 16,
            'Sports': 20,
            'Business': 12,
            'Science': 19,
            'Politics': 396
        }
        
        # Supported regions
        self.supported_regions = ['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'JP', 'IN']
        self.supported_categories = list(self.category_map.keys())
    
    def _init_pytrends(self):
        """Initialize pytrends client"""
        if not self.pytrends:
            try:
                self.pytrends = TrendReq(hl='en-US', tz=360)
                self.logger.info("Initialized pytrends client")
            except Exception as e:
                self.logger.error(f"Failed to initialize pytrends: {str(e)}")
                raise ScrapingError(f"Failed to initialize Google Trends client: {str(e)}")
    
    async def _rate_limit(self):
        """Implement rate limiting for Google Trends API"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.request_delay:
            sleep_time = self.request_delay - time_since_last
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f}s")
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    async def validate_connection(self) -> bool:
        """Validate connection to Google Trends"""
        try:
            self._init_pytrends()
            
            # Test with a simple query
            await self._rate_limit()
            test_trends = self.pytrends.trending_searches(pn='united_states')
            
            return test_trends is not None and not test_trends.empty
            
        except Exception as e:
            self.logger.error(f"Google Trends connection validation failed: {str(e)}")
            return False
    
    def get_supported_regions(self) -> List[str]:
        """Get list of supported regions"""
        return self.supported_regions.copy()
    
    def get_supported_categories(self) -> List[str]:
        """Get list of supported categories"""
        return self.supported_categories.copy()
    
    async def scrape_trends(
        self, 
        region: str = "US", 
        category: Optional[str] = None,
        limit: int = 50
    ) -> ScrapingResult:
        """Scrape trending topics from Google Trends"""
        start_time = time.time()
        trends = []
        
        try:
            self.logger.info(
                f"Starting Google Trends scraping",
                region=region,
                category=category,
                limit=limit
            )
            
            self._init_pytrends()
            await self._rate_limit()
            
            # Map region to pytrends format
            region_map = {
                'US': 'united_states',
                'UK': 'united_kingdom', 
                'CA': 'canada',
                'AU': 'australia',
                'DE': 'germany',
                'FR': 'france',
                'JP': 'japan',
                'IN': 'india'
            }
            
            pytrends_region = region_map.get(region, 'united_states')
            
            # Get trending searches
            trending_searches = self.pytrends.trending_searches(pn=pytrends_region)
            
            if trending_searches is not None and not trending_searches.empty:
                keywords = trending_searches[0].head(limit).tolist()
                
                for i, keyword in enumerate(keywords):
                    try:
                        await self._rate_limit()
                        
                        # Get category ID
                        cat_id = self.category_map.get(category, 0) if category else 0
                        
                        # Build payload for interest over time
                        self.pytrends.build_payload(
                            [keyword], 
                            cat=cat_id, 
                            timeframe='now 7-d', 
                            geo=region
                        )
                        
                        # Get interest over time data
                        interest_data = self.pytrends.interest_over_time()
                        
                        if not interest_data.empty:
                            # Calculate metrics
                            values = interest_data[keyword].values
                            current_volume = int(values[-1]) if len(values) > 0 else 0
                            
                            # Calculate growth rate (last 7 days)
                            growth_rate = 0.0
                            if len(values) >= 7:
                                week_ago = values[-7]
                                if week_ago > 0:
                                    growth_rate = ((current_volume - week_ago) / week_ago) * 100
                            
                            # Get related queries for additional context
                            try:
                                related_queries = self.pytrends.related_queries()
                            except:
                                related_queries = {}
                            
                            # Create trend data
                            trend = self._create_trend_data(
                                keyword=keyword,
                                search_volume=current_volume,
                                growth_rate=growth_rate,
                                region=region,
                                category=category,
                                metadata={
                                    'interest_over_time': interest_data.to_dict(),
                                    'related_queries': related_queries,
                                    'category_id': cat_id,
                                    'timeframe': 'now 7-d',
                                    'position': i + 1,
                                    'pytrends_region': pytrends_region
                                }
                            )
                            
                            trends.append(trend)
                            
                    except Exception as e:
                        self.logger.warning(
                            f"Failed to get data for keyword '{keyword}': {str(e)}",
                            keyword=keyword,
                            region=region
                        )
                        continue
            
            processing_time = time.time() - start_time
            
            # Record metrics
            await self.record_metrics(region, category or 'all', len(trends), processing_time)
            
            return ScrapingResult(
                status=ScrapingStatus.COMPLETED,
                trends=trends,
                total_found=len(trends),
                processing_time=processing_time,
                metadata={
                    'source': 'google_trends',
                    'region': region,
                    'category': category,
                    'limit': limit,
                    'pytrends_region': pytrends_region
                }
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            await self.record_error(e, region, category)
            
            return ScrapingResult(
                status=ScrapingStatus.FAILED,
                trends=[],
                total_found=0,
                processing_time=processing_time,
                error_message=str(e),
                metadata={
                    'source': 'google_trends',
                    'region': region,
                    'category': category,
                    'error': str(e)
                }
            )

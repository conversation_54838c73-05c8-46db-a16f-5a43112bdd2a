"""
Trends24 scraper implementation
Extracts trending topics from trends24.in for real-time Twitter trends
"""
import asyncio
import time
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from bs4 import BeautifulSoup

from .base_scraper import BaseScraper, TrendData, ScrapingResult, ScrapingStatus
from shared.config import SCRAPER_CONFIG
from shared.utils import AsyncHTTPClient
from shared.exceptions import ScrapingError


class Trends24Scraper(BaseScraper):
    """Trends24 scraper for real-time trending topics"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("trends24", config)
        self.base_url = "https://trends24.in"
        
        # Get rate limiting config
        rate_config = SCRAPER_CONFIG['rate_limiting'].get('trends24', {})
        self.requests_per_minute = rate_config.get('requests_per_minute', 30)
        self.request_delay = 60 / self.requests_per_minute
        self.last_request_time = 0
        
        # Region mapping
        self.region_map = {
            'US': 'united-states',
            'UK': 'united-kingdom',
            'CA': 'canada',
            'AU': 'australia',
            'DE': 'germany',
            'FR': 'france',
            'JP': 'japan',
            'IN': 'india',
            'BR': 'brazil',
            'MX': 'mexico'
        }
        
        # Supported regions and categories
        self.supported_regions = list(self.region_map.keys())
        self.supported_categories = ['All', 'Technology', 'Entertainment', 'Sports', 'Politics', 'News']
        
        # Headers for web scraping
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    async def _rate_limit(self):
        """Implement rate limiting for Trends24"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.request_delay:
            sleep_time = self.request_delay - time_since_last
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f}s")
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    async def validate_connection(self) -> bool:
        """Validate connection to Trends24"""
        try:
            await self._rate_limit()
            
            async with AsyncHTTPClient() as client:
                response = await client.get(
                    f"{self.base_url}/united-states/",
                    headers=self.headers
                )
                
                return response['status'] == 200 and 'trends24' in response['data'].lower()
                
        except Exception as e:
            self.logger.error(f"Trends24 connection validation failed: {str(e)}")
            return False
    
    def get_supported_regions(self) -> List[str]:
        """Get list of supported regions"""
        return self.supported_regions.copy()
    
    def get_supported_categories(self) -> List[str]:
        """Get list of supported categories"""
        return self.supported_categories.copy()
    
    def _parse_trends_from_html(self, html_content: str, region: str) -> List[Dict[str, Any]]:
        """Parse trends from HTML content"""
        trends = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find trend elements (this may need adjustment based on actual HTML structure)
            trend_elements = soup.find_all(['a', 'span', 'div'], class_=re.compile(r'trend|hashtag|topic', re.I))
            
            if not trend_elements:
                # Fallback: look for hashtags and trending terms
                hashtag_pattern = r'#\w+'
                hashtags = re.findall(hashtag_pattern, html_content)
                
                # Look for trending terms in common patterns
                trend_patterns = [
                    r'<a[^>]*class="[^"]*trend[^"]*"[^>]*>([^<]+)</a>',
                    r'<span[^>]*class="[^"]*hashtag[^"]*"[^>]*>([^<]+)</span>',
                    r'<div[^>]*class="[^"]*topic[^"]*"[^>]*>([^<]+)</div>'
                ]
                
                for pattern in trend_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    for match in matches:
                        clean_text = re.sub(r'<[^>]+>', '', match).strip()
                        if clean_text and len(clean_text) > 2:
                            trends.append({
                                'keyword': clean_text,
                                'source_element': 'pattern_match',
                                'raw_text': match
                            })
                
                # Add hashtags
                for hashtag in hashtags[:20]:  # Limit to top 20
                    trends.append({
                        'keyword': hashtag,
                        'source_element': 'hashtag',
                        'raw_text': hashtag
                    })
            else:
                # Parse from found elements
                for i, element in enumerate(trend_elements[:50]):  # Limit to top 50
                    text = element.get_text(strip=True)
                    if text and len(text) > 2 and not text.startswith('http'):
                        trends.append({
                            'keyword': text,
                            'source_element': element.name,
                            'element_class': element.get('class', []),
                            'position': i + 1
                        })
            
            # Remove duplicates and clean up
            seen = set()
            unique_trends = []
            for trend in trends:
                keyword = trend['keyword'].lower().strip()
                if keyword not in seen and len(keyword) > 2:
                    seen.add(keyword)
                    unique_trends.append(trend)
            
            return unique_trends[:30]  # Return top 30 unique trends
            
        except Exception as e:
            self.logger.error(f"Failed to parse trends from HTML: {str(e)}")
            return []
    
    async def scrape_trends(
        self, 
        region: str = "US", 
        category: Optional[str] = None,
        limit: int = 50
    ) -> ScrapingResult:
        """Scrape trending topics from Trends24"""
        start_time = time.time()
        trends = []
        
        try:
            self.logger.info(
                f"Starting Trends24 scraping",
                region=region,
                category=category,
                limit=limit
            )
            
            await self._rate_limit()
            
            # Map region to Trends24 format
            country_code = self.region_map.get(region, 'united-states')
            url = f"{self.base_url}/{country_code}/"
            
            async with AsyncHTTPClient() as client:
                response = await client.get(url, headers=self.headers)
                
                if response['status'] != 200:
                    raise ScrapingError(f"Trends24 API returned status {response['status']}")
                
                html_content = response['data']
                
                # Parse trends from HTML
                parsed_trends = self._parse_trends_from_html(html_content, region)
                
                for i, trend_data in enumerate(parsed_trends[:limit]):
                    # Estimate metrics based on position (since Trends24 doesn't provide exact numbers)
                    search_volume = max(100 - (i * 3), 10)  # Decreasing volume by position
                    growth_rate = max(50 - (i * 1.5), 5)   # Decreasing growth by position
                    
                    # Create trend data
                    trend = self._create_trend_data(
                        keyword=trend_data['keyword'],
                        search_volume=search_volume,
                        growth_rate=growth_rate,
                        region=region,
                        category=category or 'All',
                        metadata={
                            'position': i + 1,
                            'url': url,
                            'country_code': country_code,
                            'source_element': trend_data.get('source_element'),
                            'element_class': trend_data.get('element_class'),
                            'scraped_at': datetime.utcnow().isoformat(),
                            'estimated_metrics': True  # Flag to indicate these are estimates
                        }
                    )
                    
                    trends.append(trend)
            
            processing_time = time.time() - start_time
            
            # Record metrics
            await self.record_metrics(region, category or 'all', len(trends), processing_time)
            
            return ScrapingResult(
                status=ScrapingStatus.COMPLETED,
                trends=trends,
                total_found=len(trends),
                processing_time=processing_time,
                metadata={
                    'source': 'trends24',
                    'region': region,
                    'category': category,
                    'limit': limit,
                    'country_code': country_code,
                    'url': url
                }
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            await self.record_error(e, region, category)
            
            return ScrapingResult(
                status=ScrapingStatus.FAILED,
                trends=[],
                total_found=0,
                processing_time=processing_time,
                error_message=str(e),
                metadata={
                    'source': 'trends24',
                    'region': region,
                    'category': category,
                    'error': str(e)
                }
            )

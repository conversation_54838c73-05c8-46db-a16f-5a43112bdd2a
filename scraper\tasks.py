"""
Celery tasks for trend scraping operations
"""
from celery import Celery
from typing import Dict, Any
from shared.config import settings
from shared.celery_app import celery_app
from scraper.trend_scraper import trend_orchestrator
from monitoring.logger import get_logger, set_correlation_id
from monitoring.metrics import app_metrics

logger = get_logger('scraper.tasks')


@celery_app.task(bind=True, max_retries=3)
def scrape_trends(self, correlation_id: str = None):
    """
    Celery task to scrape trends from all sources
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info("Starting trend scraping task", task_id=self.request.id)
    
    try:
        # Run the scraping orchestrator
        import asyncio
        results = asyncio.run(trend_orchestrator.scrape_all_trends())
        
        # Record metrics
        app_metrics.record_celery_task('scrape_trends', 'success')
        
        logger.info(
            "Trend scraping task completed successfully",
            task_id=self.request.id,
            total_scraped=results['total_scraped'],
            total_saved=results['total_saved']
        )
        
        return results
        
    except Exception as exc:
        logger.error(
            f"Trend scraping task failed: {str(exc)}",
            task_id=self.request.id,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('scrape_trends', 'failed')
        
        # Retry with exponential backoff
        raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)


@celery_app.task(bind=True)
def scrape_trends_for_region(self, region: str, correlation_id: str = None):
    """
    Celery task to scrape trends for a specific region
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info(f"Starting trend scraping for region: {region}", task_id=self.request.id, region=region)
    
    try:
        import asyncio
        
        # Create a temporary orchestrator for single region
        from scraper.trend_scraper import GoogleTrendsScraper, Trends24Scraper
        from shared.config import SCRAPER_CONFIG
        
        results = {
            'region': region,
            'total_scraped': 0,
            'total_saved': 0,
            'sources': {},
            'errors': []
        }
        
        scrapers = {
            'google_trends': GoogleTrendsScraper(),
            'trends24': Trends24Scraper()
        }
        
        categories = SCRAPER_CONFIG['categories']
        
        async def scrape_region():
            for source_name, scraper in scrapers.items():
                source_results = {'scraped': 0, 'saved': 0, 'errors': []}
                
                for category in categories:
                    try:
                        trends = await scraper.scrape_trends(region, category)
                        source_results['scraped'] += len(trends)
                        
                        for trend in trends:
                            try:
                                score = scraper.calculate_score(trend)
                                if score >= SCRAPER_CONFIG['scoring']['minimum_score']:
                                    trend_id = await scraper.save_trend(trend)
                                    if trend_id:
                                        source_results['saved'] += 1
                            except Exception as e:
                                source_results['errors'].append(str(e))
                    
                    except Exception as e:
                        source_results['errors'].append(str(e))
                
                results['sources'][source_name] = source_results
                results['total_scraped'] += source_results['scraped']
                results['total_saved'] += source_results['saved']
                results['errors'].extend(source_results['errors'])
        
        asyncio.run(scrape_region())
        
        app_metrics.record_celery_task('scrape_trends_for_region', 'success')
        
        logger.info(
            f"Region scraping completed: {region}",
            task_id=self.request.id,
            region=region,
            total_scraped=results['total_scraped'],
            total_saved=results['total_saved']
        )
        
        return results
        
    except Exception as exc:
        logger.error(
            f"Region scraping failed for {region}: {str(exc)}",
            task_id=self.request.id,
            region=region,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('scrape_trends_for_region', 'failed')
        raise exc


@celery_app.task(bind=True)
def get_scraping_statistics(self, correlation_id: str = None):
    """
    Celery task to get scraping statistics
    """
    if correlation_id:
        set_correlation_id(correlation_id)
    
    logger.info("Getting scraping statistics", task_id=self.request.id)
    
    try:
        import asyncio
        stats = asyncio.run(trend_orchestrator.get_scraping_statistics())
        
        app_metrics.record_celery_task('get_scraping_statistics', 'success')
        
        return stats
        
    except Exception as exc:
        logger.error(
            f"Failed to get scraping statistics: {str(exc)}",
            task_id=self.request.id,
            error=str(exc)
        )
        
        app_metrics.record_celery_task('get_scraping_statistics', 'failed')
        raise exc

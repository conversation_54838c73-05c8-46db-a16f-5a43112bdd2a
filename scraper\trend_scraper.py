"""
Trend scraping system for collecting trending topics from multiple sources
This module provides backward compatibility and convenience imports
"""

# Import the new modular components
from .core import TrendScrapingOrchestrator, trend_orchestrator
from .sources.base_scraper import TrendData, ScrapingResult, ScrapingStatus, scraper_registry
from .sources.google_trends import GoogleTrendsScraper
from .sources.trends24_scraper import Trends24Scraper
from .deduplication import Fuzzy<PERSON>atcher, TrendDeduplicator
from .filters import TrendFilter
from .validators import TrendValidator
from .scoring import TrendScorer

# Backward compatibility exports
__all__ = [
    'TrendData',
    'TrendScrapingOrchestrator',
    'trend_orchestrator',
    'GoogleTrendsScraper',
    'Trends24Scraper',
    'FuzzyMatcher',
    'TrendDeduplicator',
    'TrendFilter',
    'TrendValidator',
    'TrendScorer',
    'ScrapingResult',
    'ScrapingStatus',
    'scraper_registry'
]

# For backward compatibility, create convenience functions
async def scrape_all_trends():
    """Convenience function for backward compatibility"""
    return await trend_orchestrator.scrape_all_sources()

async def get_scraping_statistics():
    """Convenience function for backward compatibility"""
    return await trend_orchestrator.get_scraper_health()

"""
Error handling utilities for scrapers
Provides retry mechanisms, error classification, and recovery strategies
"""
import asyncio
import random
import time
from typing import Any, Callable, Dict, List, Optional, Type, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from shared.exceptions import ScrapingError
from monitoring.logger import get_logger


class ErrorSeverity(str, Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """Error categories for classification"""
    NETWORK = "network"
    RATE_LIMIT = "rate_limit"
    AUTHENTICATION = "authentication"
    PARSING = "parsing"
    DATA_QUALITY = "data_quality"
    CONFIGURATION = "configuration"
    UNKNOWN = "unknown"


@dataclass
class ErrorInfo:
    """Information about an error"""
    exception: Exception
    category: ErrorCategory
    severity: ErrorSeverity
    is_retryable: bool
    suggested_delay: float
    context: Dict[str, Any]
    timestamp: datetime


class ErrorClassifier:
    """Classifies errors and determines retry strategies"""
    
    def __init__(self):
        self.logger = get_logger('scraper.error_classifier')
        
        # Error patterns for classification
        self.error_patterns = {
            ErrorCategory.NETWORK: [
                'connection', 'timeout', 'network', 'dns', 'socket',
                'unreachable', 'refused', 'reset', 'broken pipe'
            ],
            ErrorCategory.RATE_LIMIT: [
                'rate limit', 'too many requests', '429', 'quota exceeded',
                'throttled', 'rate exceeded', 'limit reached'
            ],
            ErrorCategory.AUTHENTICATION: [
                'unauthorized', '401', '403', 'forbidden', 'authentication',
                'invalid key', 'access denied', 'permission denied'
            ],
            ErrorCategory.PARSING: [
                'parse', 'json', 'xml', 'html', 'decode', 'format',
                'invalid response', 'malformed', 'syntax error'
            ],
            ErrorCategory.DATA_QUALITY: [
                'validation', 'invalid data', 'missing field', 'empty response',
                'no data', 'corrupt data', 'unexpected format'
            ],
            ErrorCategory.CONFIGURATION: [
                'configuration', 'config', 'setting', 'parameter',
                'missing config', 'invalid config'
            ]
        }
        
        # Retry strategies by category
        self.retry_strategies = {
            ErrorCategory.NETWORK: {
                'is_retryable': True,
                'max_retries': 3,
                'base_delay': 1.0,
                'backoff_factor': 2.0,
                'jitter': True
            },
            ErrorCategory.RATE_LIMIT: {
                'is_retryable': True,
                'max_retries': 5,
                'base_delay': 60.0,
                'backoff_factor': 1.5,
                'jitter': True
            },
            ErrorCategory.AUTHENTICATION: {
                'is_retryable': False,
                'max_retries': 0,
                'base_delay': 0.0,
                'backoff_factor': 1.0,
                'jitter': False
            },
            ErrorCategory.PARSING: {
                'is_retryable': True,
                'max_retries': 2,
                'base_delay': 0.5,
                'backoff_factor': 1.0,
                'jitter': False
            },
            ErrorCategory.DATA_QUALITY: {
                'is_retryable': False,
                'max_retries': 0,
                'base_delay': 0.0,
                'backoff_factor': 1.0,
                'jitter': False
            },
            ErrorCategory.CONFIGURATION: {
                'is_retryable': False,
                'max_retries': 0,
                'base_delay': 0.0,
                'backoff_factor': 1.0,
                'jitter': False
            },
            ErrorCategory.UNKNOWN: {
                'is_retryable': True,
                'max_retries': 2,
                'base_delay': 2.0,
                'backoff_factor': 2.0,
                'jitter': True
            }
        }
    
    def classify_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorInfo:
        """Classify an error and determine handling strategy"""
        error_message = str(exception).lower()
        error_type = type(exception).__name__.lower()
        
        # Determine category
        category = self._determine_category(error_message, error_type)
        
        # Determine severity
        severity = self._determine_severity(exception, category)
        
        # Get retry strategy
        strategy = self.retry_strategies.get(category, self.retry_strategies[ErrorCategory.UNKNOWN])
        
        return ErrorInfo(
            exception=exception,
            category=category,
            severity=severity,
            is_retryable=strategy['is_retryable'],
            suggested_delay=strategy['base_delay'],
            context=context or {},
            timestamp=datetime.utcnow()
        )
    
    def _determine_category(self, error_message: str, error_type: str) -> ErrorCategory:
        """Determine error category based on message and type"""
        combined_text = f"{error_message} {error_type}"
        
        for category, patterns in self.error_patterns.items():
            for pattern in patterns:
                if pattern in combined_text:
                    return category
        
        return ErrorCategory.UNKNOWN
    
    def _determine_severity(self, exception: Exception, category: ErrorCategory) -> ErrorSeverity:
        """Determine error severity"""
        # Critical errors that should stop processing
        if category == ErrorCategory.AUTHENTICATION:
            return ErrorSeverity.CRITICAL
        
        if category == ErrorCategory.CONFIGURATION:
            return ErrorSeverity.HIGH
        
        # Check for specific exception types
        if isinstance(exception, (MemoryError, SystemError)):
            return ErrorSeverity.CRITICAL
        
        if isinstance(exception, (ValueError, TypeError)) and category == ErrorCategory.DATA_QUALITY:
            return ErrorSeverity.MEDIUM
        
        if category == ErrorCategory.NETWORK:
            return ErrorSeverity.MEDIUM
        
        if category == ErrorCategory.RATE_LIMIT:
            return ErrorSeverity.LOW
        
        return ErrorSeverity.MEDIUM


class RetryHandler:
    """Handles retry logic with various backoff strategies"""
    
    def __init__(self, classifier: ErrorClassifier = None):
        self.classifier = classifier or ErrorClassifier()
        self.logger = get_logger('scraper.retry_handler')
    
    async def retry_with_backoff(
        self,
        operation: Callable,
        *args,
        max_retries: Optional[int] = None,
        base_delay: Optional[float] = None,
        backoff_factor: Optional[float] = None,
        jitter: bool = True,
        context: Dict[str, Any] = None,
        **kwargs
    ) -> Any:
        """Retry an operation with exponential backoff"""
        
        last_error_info = None
        
        for attempt in range((max_retries or 3) + 1):
            try:
                result = await operation(*args, **kwargs)
                
                if attempt > 0:
                    self.logger.info(
                        f"Operation succeeded after {attempt} retries",
                        operation=operation.__name__,
                        attempts=attempt,
                        context=context
                    )
                
                return result
                
            except Exception as e:
                error_info = self.classifier.classify_error(e, context)
                last_error_info = error_info
                
                self.logger.warning(
                    f"Operation failed (attempt {attempt + 1}): {str(e)}",
                    operation=operation.__name__,
                    attempt=attempt + 1,
                    error_category=error_info.category,
                    error_severity=error_info.severity,
                    is_retryable=error_info.is_retryable,
                    context=context
                )
                
                # Check if we should retry
                if not error_info.is_retryable or attempt >= (max_retries or 3):
                    self.logger.error(
                        f"Operation failed permanently after {attempt + 1} attempts",
                        operation=operation.__name__,
                        total_attempts=attempt + 1,
                        final_error=str(e),
                        context=context
                    )
                    raise e
                
                # Calculate delay for next attempt
                delay = self._calculate_delay(
                    attempt,
                    base_delay or error_info.suggested_delay,
                    backoff_factor or 2.0,
                    jitter
                )
                
                self.logger.debug(
                    f"Waiting {delay:.2f}s before retry {attempt + 2}",
                    delay=delay,
                    next_attempt=attempt + 2
                )
                
                await asyncio.sleep(delay)
        
        # This should never be reached, but just in case
        if last_error_info:
            raise last_error_info.exception
        else:
            raise RuntimeError("Retry loop completed without result or error")
    
    def _calculate_delay(
        self,
        attempt: int,
        base_delay: float,
        backoff_factor: float,
        jitter: bool
    ) -> float:
        """Calculate delay for next retry attempt"""
        # Exponential backoff
        delay = base_delay * (backoff_factor ** attempt)
        
        # Add jitter to avoid thundering herd
        if jitter:
            jitter_factor = random.uniform(0.5, 1.5)
            delay *= jitter_factor
        
        # Cap maximum delay at 5 minutes
        delay = min(delay, 300.0)
        
        return delay


class CircuitBreaker:
    """Circuit breaker pattern for failing operations"""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: Type[Exception] = Exception,
        name: str = "circuit_breaker"
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.name = name
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open
        
        self.logger = get_logger(f'scraper.circuit_breaker.{name}')
    
    async def call(self, operation: Callable, *args, **kwargs) -> Any:
        """Call operation through circuit breaker"""
        
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half_open"
                self.logger.info(f"Circuit breaker {self.name} entering half-open state")
            else:
                raise ScrapingError(f"Circuit breaker {self.name} is open")
        
        try:
            result = await operation(*args, **kwargs)
            
            if self.state == "half_open":
                self._reset()
            
            return result
            
        except self.expected_exception as e:
            self._record_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if self.last_failure_time is None:
            return True
        
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _record_failure(self):
        """Record a failure and potentially open the circuit"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            self.logger.warning(
                f"Circuit breaker {self.name} opened after {self.failure_count} failures"
            )
    
    def _reset(self):
        """Reset the circuit breaker"""
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"
        self.logger.info(f"Circuit breaker {self.name} reset to closed state")
    
    def get_status(self) -> Dict[str, Any]:
        """Get circuit breaker status"""
        return {
            'name': self.name,
            'state': self.state,
            'failure_count': self.failure_count,
            'failure_threshold': self.failure_threshold,
            'last_failure_time': self.last_failure_time,
            'recovery_timeout': self.recovery_timeout
        }


# Global instances
error_classifier = ErrorClassifier()
retry_handler = RetryHandler(error_classifier)

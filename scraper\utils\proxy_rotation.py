"""
Proxy rotation system for web scraping
Manages proxy pools and rotation to avoid rate limiting and IP blocking
"""
import asyncio
import random
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum
import aiohttp

from monitoring.logger import get_logger
from shared.exceptions import ProxyError


class ProxyStatus(str, Enum):
    """Proxy status enumeration"""
    ACTIVE = "active"
    FAILED = "failed"
    RATE_LIMITED = "rate_limited"
    BANNED = "banned"
    TESTING = "testing"


@dataclass
class ProxyInfo:
    """Proxy information and statistics"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"
    status: ProxyStatus = ProxyStatus.ACTIVE
    success_count: int = 0
    failure_count: int = 0
    last_used: Optional[datetime] = None
    last_success: Optional[datetime] = None
    last_failure: Optional[datetime] = None
    response_time: Optional[float] = None
    ban_until: Optional[datetime] = None
    
    @property
    def url(self) -> str:
        """Get proxy URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0
    
    @property
    def is_available(self) -> bool:
        """Check if proxy is available for use"""
        if self.status in [ProxyStatus.BANNED, ProxyStatus.FAILED]:
            return False
        
        if self.ban_until and datetime.utcnow() < self.ban_until:
            return False
        
        return True
    
    def __str__(self):
        return f"{self.host}:{self.port}"


class ProxyRotator:
    """Manages proxy rotation and health monitoring"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = get_logger('scraper.proxy_rotator')
        
        # Configuration
        self.max_failures = self.config.get('max_failures', 3)
        self.ban_duration = self.config.get('ban_duration', 300)  # 5 minutes
        self.health_check_interval = self.config.get('health_check_interval', 60)  # 1 minute
        self.test_url = self.config.get('test_url', 'http://httpbin.org/ip')
        self.request_timeout = self.config.get('request_timeout', 10)
        
        # Proxy pool
        self.proxies: List[ProxyInfo] = []
        self.current_index = 0
        self._health_check_task = None
        self._lock = asyncio.Lock()
    
    def add_proxy(
        self,
        host: str,
        port: int,
        username: Optional[str] = None,
        password: Optional[str] = None,
        protocol: str = "http"
    ):
        """Add a proxy to the pool"""
        proxy = ProxyInfo(
            host=host,
            port=port,
            username=username,
            password=password,
            protocol=protocol
        )
        
        self.proxies.append(proxy)
        self.logger.info(f"Added proxy: {proxy}")
    
    def add_proxies_from_list(self, proxy_list: List[str]):
        """Add proxies from a list of strings"""
        for proxy_str in proxy_list:
            try:
                self._parse_and_add_proxy(proxy_str)
            except Exception as e:
                self.logger.error(f"Failed to parse proxy '{proxy_str}': {str(e)}")
    
    def _parse_and_add_proxy(self, proxy_str: str):
        """Parse proxy string and add to pool"""
        # Format: protocol://username:password@host:port or host:port
        if '://' in proxy_str:
            protocol, rest = proxy_str.split('://', 1)
        else:
            protocol = 'http'
            rest = proxy_str
        
        if '@' in rest:
            auth, host_port = rest.split('@', 1)
            if ':' in auth:
                username, password = auth.split(':', 1)
            else:
                username, password = auth, None
        else:
            username, password = None, None
            host_port = rest
        
        if ':' in host_port:
            host, port_str = host_port.rsplit(':', 1)
            port = int(port_str)
        else:
            raise ValueError("Port not specified")
        
        self.add_proxy(host, port, username, password, protocol)
    
    async def get_next_proxy(self) -> Optional[ProxyInfo]:
        """Get next available proxy from the pool"""
        async with self._lock:
            if not self.proxies:
                return None
            
            # Filter available proxies
            available_proxies = [p for p in self.proxies if p.is_available]
            
            if not available_proxies:
                self.logger.warning("No available proxies in pool")
                return None
            
            # Sort by success rate and response time
            available_proxies.sort(
                key=lambda p: (p.success_rate, -(p.response_time or float('inf'))),
                reverse=True
            )
            
            # Use weighted random selection favoring better proxies
            weights = [max(0.1, p.success_rate) for p in available_proxies]
            proxy = random.choices(available_proxies, weights=weights)[0]
            
            proxy.last_used = datetime.utcnow()
            return proxy
    
    async def mark_proxy_success(self, proxy: ProxyInfo, response_time: float):
        """Mark proxy as successful"""
        async with self._lock:
            proxy.success_count += 1
            proxy.last_success = datetime.utcnow()
            proxy.response_time = response_time
            proxy.status = ProxyStatus.ACTIVE
            
            # Reset ban if it was temporary
            if proxy.ban_until and datetime.utcnow() > proxy.ban_until:
                proxy.ban_until = None
            
            self.logger.debug(f"Proxy success: {proxy} (response_time: {response_time:.2f}s)")
    
    async def mark_proxy_failure(self, proxy: ProxyInfo, error: str):
        """Mark proxy as failed"""
        async with self._lock:
            proxy.failure_count += 1
            proxy.last_failure = datetime.utcnow()
            
            self.logger.warning(f"Proxy failure: {proxy} - {error}")
            
            # Check if proxy should be banned
            if proxy.failure_count >= self.max_failures:
                proxy.status = ProxyStatus.BANNED
                proxy.ban_until = datetime.utcnow() + timedelta(seconds=self.ban_duration)
                self.logger.warning(f"Proxy banned until {proxy.ban_until}: {proxy}")
            else:
                proxy.status = ProxyStatus.FAILED
    
    async def test_proxy(self, proxy: ProxyInfo) -> bool:
        """Test if a proxy is working"""
        try:
            proxy.status = ProxyStatus.TESTING
            start_time = asyncio.get_event_loop().time()
            
            connector = aiohttp.TCPConnector()
            timeout = aiohttp.ClientTimeout(total=self.request_timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                async with session.get(
                    self.test_url,
                    proxy=proxy.url
                ) as response:
                    if response.status == 200:
                        response_time = asyncio.get_event_loop().time() - start_time
                        await self.mark_proxy_success(proxy, response_time)
                        return True
                    else:
                        await self.mark_proxy_failure(proxy, f"HTTP {response.status}")
                        return False
        
        except Exception as e:
            await self.mark_proxy_failure(proxy, str(e))
            return False
    
    async def health_check_all_proxies(self):
        """Perform health check on all proxies"""
        self.logger.info("Starting proxy health check")
        
        tasks = []
        for proxy in self.proxies:
            if proxy.status != ProxyStatus.TESTING:
                tasks.append(self.test_proxy(proxy))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful = sum(1 for r in results if r is True)
            self.logger.info(f"Health check completed: {successful}/{len(tasks)} proxies healthy")
    
    async def start_health_monitoring(self):
        """Start background health monitoring"""
        if self._health_check_task:
            return
        
        async def health_check_loop():
            while True:
                try:
                    await asyncio.sleep(self.health_check_interval)
                    await self.health_check_all_proxies()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Health check error: {str(e)}")
        
        self._health_check_task = asyncio.create_task(health_check_loop())
        self.logger.info("Started proxy health monitoring")
    
    async def stop_health_monitoring(self):
        """Stop background health monitoring"""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
            self.logger.info("Stopped proxy health monitoring")
    
    def get_proxy_stats(self) -> Dict[str, Any]:
        """Get proxy pool statistics"""
        if not self.proxies:
            return {
                'total_proxies': 0,
                'available_proxies': 0,
                'banned_proxies': 0,
                'failed_proxies': 0,
                'average_success_rate': 0.0,
                'average_response_time': 0.0
            }
        
        available = len([p for p in self.proxies if p.is_available])
        banned = len([p for p in self.proxies if p.status == ProxyStatus.BANNED])
        failed = len([p for p in self.proxies if p.status == ProxyStatus.FAILED])
        
        success_rates = [p.success_rate for p in self.proxies if p.success_count + p.failure_count > 0]
        avg_success_rate = sum(success_rates) / len(success_rates) if success_rates else 0.0
        
        response_times = [p.response_time for p in self.proxies if p.response_time is not None]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0.0
        
        return {
            'total_proxies': len(self.proxies),
            'available_proxies': available,
            'banned_proxies': banned,
            'failed_proxies': failed,
            'average_success_rate': avg_success_rate,
            'average_response_time': avg_response_time,
            'proxies': [
                {
                    'host': p.host,
                    'port': p.port,
                    'status': p.status,
                    'success_rate': p.success_rate,
                    'response_time': p.response_time,
                    'last_used': p.last_used.isoformat() if p.last_used else None
                }
                for p in self.proxies
            ]
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.stop_health_monitoring()
        self.logger.info("Proxy rotator cleaned up")


class ProxySession:
    """HTTP session with automatic proxy rotation"""
    
    def __init__(self, proxy_rotator: ProxyRotator, max_retries: int = 3):
        self.proxy_rotator = proxy_rotator
        self.max_retries = max_retries
        self.logger = get_logger('scraper.proxy_session')
    
    async def request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> aiohttp.ClientResponse:
        """Make HTTP request with proxy rotation"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            proxy = await self.proxy_rotator.get_next_proxy()
            
            if not proxy:
                raise ProxyError("No available proxies")
            
            try:
                start_time = asyncio.get_event_loop().time()
                
                connector = aiohttp.TCPConnector()
                timeout = aiohttp.ClientTimeout(total=30)
                
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout
                ) as session:
                    async with session.request(
                        method,
                        url,
                        proxy=proxy.url,
                        **kwargs
                    ) as response:
                        response_time = asyncio.get_event_loop().time() - start_time
                        await self.proxy_rotator.mark_proxy_success(proxy, response_time)
                        return response
            
            except Exception as e:
                last_exception = e
                await self.proxy_rotator.mark_proxy_failure(proxy, str(e))
                
                if attempt < self.max_retries:
                    self.logger.warning(f"Request failed with proxy {proxy}, retrying: {str(e)}")
                    await asyncio.sleep(1)  # Brief delay before retry
        
        raise ProxyError(f"All proxy attempts failed: {str(last_exception)}")
    
    async def get(self, url: str, **kwargs) -> aiohttp.ClientResponse:
        """Make GET request with proxy rotation"""
        return await self.request('GET', url, **kwargs)
    
    async def post(self, url: str, **kwargs) -> aiohttp.ClientResponse:
        """Make POST request with proxy rotation"""
        return await self.request('POST', url, **kwargs)

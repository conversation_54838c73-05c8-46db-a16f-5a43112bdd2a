"""
Rate limiting utilities for scrapers
Provides various rate limiting strategies and implementations
"""
import asyncio
import time
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from collections import deque
from dataclasses import dataclass

from monitoring.logger import get_logger


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting"""
    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    burst_limit: int = 10
    backoff_factor: float = 2.0
    max_backoff: float = 300.0  # 5 minutes max backoff


class TokenBucketRateLimiter:
    """Token bucket rate limiter implementation"""
    
    def __init__(self, capacity: int, refill_rate: float, name: str = "rate_limiter"):
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate  # tokens per second
        self.last_refill = time.time()
        self.name = name
        self.logger = get_logger(f'scraper.rate_limiter.{name}')
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int = 1) -> bool:
        """Acquire tokens from the bucket"""
        async with self._lock:
            now = time.time()
            
            # Refill tokens based on elapsed time
            elapsed = now - self.last_refill
            tokens_to_add = elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill = now
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            
            return False
    
    async def wait_for_tokens(self, tokens: int = 1) -> float:
        """Wait until tokens are available and acquire them"""
        start_time = time.time()
        
        while True:
            if await self.acquire(tokens):
                wait_time = time.time() - start_time
                if wait_time > 0:
                    self.logger.debug(f"Waited {wait_time:.2f}s for {tokens} tokens")
                return wait_time
            
            # Calculate how long to wait for next token
            wait_time = 1.0 / self.refill_rate
            await asyncio.sleep(min(wait_time, 1.0))
    
    def get_status(self) -> Dict[str, Any]:
        """Get current rate limiter status"""
        return {
            'name': self.name,
            'capacity': self.capacity,
            'current_tokens': self.tokens,
            'refill_rate': self.refill_rate,
            'last_refill': self.last_refill
        }


class SlidingWindowRateLimiter:
    """Sliding window rate limiter implementation"""
    
    def __init__(self, max_requests: int, window_seconds: int, name: str = "sliding_window"):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.name = name
        self.requests = deque()
        self.logger = get_logger(f'scraper.rate_limiter.{name}')
        self._lock = asyncio.Lock()
    
    async def can_proceed(self) -> bool:
        """Check if request can proceed without waiting"""
        async with self._lock:
            now = time.time()
            cutoff = now - self.window_seconds
            
            # Remove old requests outside the window
            while self.requests and self.requests[0] < cutoff:
                self.requests.popleft()
            
            return len(self.requests) < self.max_requests
    
    async def acquire(self) -> bool:
        """Try to acquire permission for a request"""
        if await self.can_proceed():
            async with self._lock:
                self.requests.append(time.time())
                return True
        return False
    
    async def wait_for_slot(self) -> float:
        """Wait until a slot is available"""
        start_time = time.time()
        
        while not await self.can_proceed():
            # Calculate wait time until oldest request expires
            async with self._lock:
                if self.requests:
                    oldest_request = self.requests[0]
                    wait_time = (oldest_request + self.window_seconds) - time.time()
                    wait_time = max(0.1, min(wait_time, 1.0))
                else:
                    wait_time = 0.1
            
            await asyncio.sleep(wait_time)
        
        # Acquire the slot
        await self.acquire()
        
        total_wait = time.time() - start_time
        if total_wait > 0:
            self.logger.debug(f"Waited {total_wait:.2f}s for request slot")
        
        return total_wait
    
    def get_status(self) -> Dict[str, Any]:
        """Get current rate limiter status"""
        return {
            'name': self.name,
            'max_requests': self.max_requests,
            'window_seconds': self.window_seconds,
            'current_requests': len(self.requests),
            'requests_available': self.max_requests - len(self.requests)
        }


class AdaptiveRateLimiter:
    """Adaptive rate limiter that adjusts based on success/failure rates"""
    
    def __init__(self, initial_rate: float, min_rate: float = 0.1, max_rate: float = 10.0, name: str = "adaptive"):
        self.current_rate = initial_rate
        self.min_rate = min_rate
        self.max_rate = max_rate
        self.name = name
        self.logger = get_logger(f'scraper.rate_limiter.{name}')
        
        # Success/failure tracking
        self.success_count = 0
        self.failure_count = 0
        self.last_adjustment = time.time()
        self.adjustment_interval = 60  # Adjust every minute
        
        # Rate adjustment factors
        self.increase_factor = 1.1
        self.decrease_factor = 0.8
        
        self._lock = asyncio.Lock()
        self.last_request = 0
    
    async def acquire(self) -> float:
        """Acquire permission and return wait time"""
        async with self._lock:
            now = time.time()
            
            # Calculate required delay
            min_interval = 1.0 / self.current_rate
            elapsed = now - self.last_request
            
            if elapsed < min_interval:
                wait_time = min_interval - elapsed
                await asyncio.sleep(wait_time)
                self.last_request = time.time()
                return wait_time
            else:
                self.last_request = now
                return 0.0
    
    async def record_success(self):
        """Record a successful request"""
        async with self._lock:
            self.success_count += 1
            await self._maybe_adjust_rate()
    
    async def record_failure(self):
        """Record a failed request"""
        async with self._lock:
            self.failure_count += 1
            await self._maybe_adjust_rate()
    
    async def _maybe_adjust_rate(self):
        """Adjust rate based on success/failure ratio"""
        now = time.time()
        
        if now - self.last_adjustment < self.adjustment_interval:
            return
        
        total_requests = self.success_count + self.failure_count
        
        if total_requests >= 10:  # Need minimum sample size
            success_rate = self.success_count / total_requests
            
            if success_rate > 0.9:  # High success rate, can increase
                new_rate = min(self.max_rate, self.current_rate * self.increase_factor)
            elif success_rate < 0.7:  # Low success rate, decrease
                new_rate = max(self.min_rate, self.current_rate * self.decrease_factor)
            else:
                new_rate = self.current_rate  # No change
            
            if new_rate != self.current_rate:
                self.logger.info(
                    f"Adjusting rate: {self.current_rate:.2f} -> {new_rate:.2f} "
                    f"(success rate: {success_rate:.2f})"
                )
                self.current_rate = new_rate
            
            # Reset counters
            self.success_count = 0
            self.failure_count = 0
            self.last_adjustment = now
    
    def get_status(self) -> Dict[str, Any]:
        """Get current rate limiter status"""
        total_requests = self.success_count + self.failure_count
        success_rate = self.success_count / total_requests if total_requests > 0 else 0
        
        return {
            'name': self.name,
            'current_rate': self.current_rate,
            'min_rate': self.min_rate,
            'max_rate': self.max_rate,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'success_rate': success_rate,
            'last_adjustment': self.last_adjustment
        }


class CompositeRateLimiter:
    """Combines multiple rate limiters"""
    
    def __init__(self, limiters: Dict[str, Any], name: str = "composite"):
        self.limiters = limiters
        self.name = name
        self.logger = get_logger(f'scraper.rate_limiter.{name}')
    
    async def acquire(self) -> float:
        """Acquire from all limiters and return total wait time"""
        total_wait = 0.0
        
        for limiter_name, limiter in self.limiters.items():
            if hasattr(limiter, 'acquire'):
                wait_time = await limiter.acquire()
                total_wait += wait_time
            elif hasattr(limiter, 'wait_for_slot'):
                wait_time = await limiter.wait_for_slot()
                total_wait += wait_time
        
        return total_wait
    
    async def record_success(self):
        """Record success in adaptive limiters"""
        for limiter in self.limiters.values():
            if hasattr(limiter, 'record_success'):
                await limiter.record_success()
    
    async def record_failure(self):
        """Record failure in adaptive limiters"""
        for limiter in self.limiters.values():
            if hasattr(limiter, 'record_failure'):
                await limiter.record_failure()
    
    def get_status(self) -> Dict[str, Any]:
        """Get status of all limiters"""
        status = {'name': self.name, 'limiters': {}}
        
        for limiter_name, limiter in self.limiters.items():
            if hasattr(limiter, 'get_status'):
                status['limiters'][limiter_name] = limiter.get_status()
        
        return status


def create_rate_limiter(config: RateLimitConfig, name: str = "default") -> CompositeRateLimiter:
    """Create a composite rate limiter from configuration"""
    limiters = {}
    
    # Per-minute limiter
    if config.requests_per_minute > 0:
        limiters['per_minute'] = SlidingWindowRateLimiter(
            max_requests=config.requests_per_minute,
            window_seconds=60,
            name=f"{name}_per_minute"
        )
    
    # Per-hour limiter
    if config.requests_per_hour > 0:
        limiters['per_hour'] = SlidingWindowRateLimiter(
            max_requests=config.requests_per_hour,
            window_seconds=3600,
            name=f"{name}_per_hour"
        )
    
    # Burst limiter
    if config.burst_limit > 0:
        limiters['burst'] = TokenBucketRateLimiter(
            capacity=config.burst_limit,
            refill_rate=config.requests_per_minute / 60.0,  # Convert to per-second
            name=f"{name}_burst"
        )
    
    return CompositeRateLimiter(limiters, name)

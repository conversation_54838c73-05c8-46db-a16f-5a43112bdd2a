"""
Trend validation module
Provides validation rules and data quality checks for scraped trends
"""
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from .sources.base_scraper import TrendData
from shared.config import SCRAPER_CONFIG
from monitoring.logger import get_logger


class ValidationResult:
    """Result of trend validation"""
    
    def __init__(self, is_valid: bool, errors: List[str] = None, warnings: List[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
    
    def add_error(self, error: str):
        """Add validation error"""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        """Add validation warning"""
        self.warnings.append(warning)


class TrendValidator:
    """Validates trend data quality and consistency"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = get_logger('scraper.validator')
        self.config = config or SCRAPER_CONFIG.get('validation', {})
        
        # Validation thresholds
        self.max_search_volume = self.config.get('max_search_volume', 1000000)
        self.max_growth_rate = self.config.get('max_growth_rate', 10000)  # 10000% max growth
        self.min_confidence_score = self.config.get('min_confidence_score', 0.1)
        
        # Known valid regions and categories
        self.valid_regions = set(SCRAPER_CONFIG.get('regions', []))
        self.valid_categories = set(SCRAPER_CONFIG.get('categories', []))
        
        # Keyword validation patterns
        self.valid_keyword_pattern = re.compile(r'^[a-zA-Z0-9\s\-_#@.,!?&()]+$')
        self.suspicious_patterns = [
            re.compile(r'^[^a-zA-Z]*$'),  # No letters at all
            re.compile(r'^.{1,2}$'),      # Too short
            re.compile(r'^.{200,}$'),     # Too long
            re.compile(r'(.)\1{5,}'),     # Excessive repetition
        ]
    
    def validate_trend(self, trend: TrendData) -> bool:
        """Validate a single trend (returns boolean for simple filtering)"""
        result = self.validate_trend_detailed(trend)
        return result.is_valid
    
    def validate_trend_detailed(self, trend: TrendData) -> ValidationResult:
        """Validate a single trend with detailed results"""
        result = ValidationResult(True)
        
        # Validate required fields
        self._validate_required_fields(trend, result)
        
        # Validate keyword
        self._validate_keyword(trend.keyword, result)
        
        # Validate metrics
        self._validate_metrics(trend, result)
        
        # Validate region and category
        self._validate_region_category(trend, result)
        
        # Validate timestamps
        self._validate_timestamps(trend, result)
        
        # Validate confidence score
        self._validate_confidence_score(trend, result)
        
        # Validate metadata
        self._validate_metadata(trend, result)
        
        if result.errors:
            self.logger.debug(
                f"Validation failed for trend: {trend.keyword}",
                keyword=trend.keyword,
                errors=result.errors,
                warnings=result.warnings
            )
        
        return result
    
    def _validate_required_fields(self, trend: TrendData, result: ValidationResult):
        """Validate that required fields are present"""
        if not trend.keyword or not trend.keyword.strip():
            result.add_error("Keyword is required and cannot be empty")
        
        if not trend.source:
            result.add_error("Source is required")
        
        if not trend.region:
            result.add_error("Region is required")
        
        if trend.scraped_at is None:
            result.add_error("Scraped timestamp is required")
    
    def _validate_keyword(self, keyword: str, result: ValidationResult):
        """Validate keyword format and content"""
        if not keyword:
            return
        
        keyword = keyword.strip()
        
        # Check basic format
        if not self.valid_keyword_pattern.match(keyword):
            result.add_error("Keyword contains invalid characters")
        
        # Check for suspicious patterns
        for pattern in self.suspicious_patterns:
            if pattern.match(keyword):
                result.add_error(f"Keyword matches suspicious pattern: {pattern.pattern}")
        
        # Check length
        if len(keyword) < 2:
            result.add_error("Keyword is too short (minimum 2 characters)")
        elif len(keyword) > 150:
            result.add_error("Keyword is too long (maximum 150 characters)")
        
        # Check for excessive whitespace
        if '  ' in keyword:  # Multiple spaces
            result.add_warning("Keyword contains excessive whitespace")
        
        # Check for all caps (might be spam)
        if keyword.isupper() and len(keyword) > 5:
            result.add_warning("Keyword is all uppercase (possible spam)")
    
    def _validate_metrics(self, trend: TrendData, result: ValidationResult):
        """Validate search volume and growth rate"""
        # Validate search volume
        if trend.search_volume is not None:
            if trend.search_volume < 0:
                result.add_error("Search volume cannot be negative")
            elif trend.search_volume > self.max_search_volume:
                result.add_error(f"Search volume exceeds maximum ({self.max_search_volume})")
            elif trend.search_volume == 0:
                result.add_warning("Search volume is zero")
        
        # Validate growth rate
        if trend.growth_rate is not None:
            if abs(trend.growth_rate) > self.max_growth_rate:
                result.add_error(f"Growth rate exceeds maximum ({self.max_growth_rate}%)")
            elif trend.growth_rate < -100:
                result.add_warning("Growth rate indicates complete decline")
        
        # Check for unrealistic combinations
        if (trend.search_volume is not None and trend.growth_rate is not None):
            if trend.search_volume > 1000 and trend.growth_rate > 1000:
                result.add_warning("High volume with very high growth rate (suspicious)")
            elif trend.search_volume < 10 and trend.growth_rate > 500:
                result.add_warning("Low volume with extreme growth rate (suspicious)")
    
    def _validate_region_category(self, trend: TrendData, result: ValidationResult):
        """Validate region and category values"""
        # Validate region
        if trend.region and self.valid_regions:
            if trend.region not in self.valid_regions:
                result.add_error(f"Invalid region: {trend.region}")
        
        # Validate category
        if trend.category and self.valid_categories:
            if trend.category not in self.valid_categories:
                result.add_warning(f"Unknown category: {trend.category}")
    
    def _validate_timestamps(self, trend: TrendData, result: ValidationResult):
        """Validate timestamp fields"""
        now = datetime.utcnow()
        
        if trend.scraped_at:
            # Check if scraped_at is in the future
            if trend.scraped_at > now + timedelta(minutes=5):  # Allow 5 min clock skew
                result.add_error("Scraped timestamp is in the future")
            
            # Check if scraped_at is too old
            if trend.scraped_at < now - timedelta(days=7):
                result.add_warning("Scraped timestamp is more than 7 days old")
    
    def _validate_confidence_score(self, trend: TrendData, result: ValidationResult):
        """Validate confidence score"""
        if trend.confidence_score is not None:
            if not 0 <= trend.confidence_score <= 1:
                result.add_error("Confidence score must be between 0 and 1")
            elif trend.confidence_score < self.min_confidence_score:
                result.add_warning(f"Low confidence score: {trend.confidence_score}")
    
    def _validate_metadata(self, trend: TrendData, result: ValidationResult):
        """Validate metadata structure and content"""
        if trend.metadata is None:
            result.add_warning("No metadata provided")
            return
        
        if not isinstance(trend.metadata, dict):
            result.add_error("Metadata must be a dictionary")
            return
        
        # Check for required metadata fields
        required_fields = ['scraper', 'scraped_at']
        for field in required_fields:
            if field not in trend.metadata:
                result.add_warning(f"Missing metadata field: {field}")
        
        # Validate metadata size (prevent excessive data)
        try:
            import json
            metadata_size = len(json.dumps(trend.metadata))
            if metadata_size > 10000:  # 10KB limit
                result.add_warning("Metadata is very large (>10KB)")
        except Exception:
            result.add_warning("Metadata is not JSON serializable")
    
    def validate_trends_batch(self, trends: List[TrendData]) -> Dict[str, Any]:
        """Validate a batch of trends and return summary statistics"""
        total_trends = len(trends)
        valid_trends = 0
        validation_results = []
        
        error_counts = {}
        warning_counts = {}
        
        for trend in trends:
            result = self.validate_trend_detailed(trend)
            validation_results.append(result)
            
            if result.is_valid:
                valid_trends += 1
            
            # Count errors and warnings
            for error in result.errors:
                error_counts[error] = error_counts.get(error, 0) + 1
            
            for warning in result.warnings:
                warning_counts[warning] = warning_counts.get(warning, 0) + 1
        
        validation_summary = {
            'total_trends': total_trends,
            'valid_trends': valid_trends,
            'invalid_trends': total_trends - valid_trends,
            'validation_rate': (valid_trends / total_trends * 100) if total_trends > 0 else 0,
            'common_errors': dict(sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            'common_warnings': dict(sorted(warning_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            'validation_results': validation_results
        }
        
        self.logger.info(
            f"Batch validation completed",
            total_trends=total_trends,
            valid_trends=valid_trends,
            validation_rate=f"{validation_summary['validation_rate']:.1f}%"
        )
        
        return validation_summary
    
    def get_validation_rules(self) -> Dict[str, Any]:
        """Get current validation rules and thresholds"""
        return {
            'max_search_volume': self.max_search_volume,
            'max_growth_rate': self.max_growth_rate,
            'min_confidence_score': self.min_confidence_score,
            'valid_regions': list(self.valid_regions),
            'valid_categories': list(self.valid_categories),
            'keyword_pattern': self.valid_keyword_pattern.pattern,
            'suspicious_patterns': [p.pattern for p in self.suspicious_patterns]
        }
    
    def update_validation_rules(self, new_rules: Dict[str, Any]):
        """Update validation rules"""
        for key, value in new_rules.items():
            if hasattr(self, key):
                setattr(self, key, value)
                self.logger.info(f"Updated validation rule: {key} = {value}")
            else:
                self.logger.warning(f"Unknown validation rule: {key}")

"""
Authentication and authorization system for the Trend Platform
Integrates with Supa<PERSON> Auth and provides JWT token management
"""
import jwt
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import aiohttp
from shared.config import settings, SECURITY_CONFIG
from shared.exceptions import AuthenticationError, AuthorizationError
from shared.utils import get_utc_timestamp
import logging

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


class User(BaseModel):
    """User model"""
    id: str
    email: str
    role: str = "viewer"
    permissions: List[str] = []
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: bool = True
    created_at: Optional[datetime] = None


class TokenData(BaseModel):
    """Token payload data"""
    user_id: str
    email: str
    role: str
    permissions: List[str]
    exp: datetime
    iat: datetime


class AuthService:
    """Authentication service"""
    
    def __init__(self):
        self.supabase_url = settings.supabase_url
        self.supabase_anon_key = settings.supabase_anon_key
        self.supabase_service_key = settings.supabase_service_role_key
        self.jwt_secret = settings.jwt_secret
        self.jwt_algorithm = "HS256"
        self.jwt_expiry = SECURITY_CONFIG['authentication']['jwt_expiry']
        self.refresh_expiry = SECURITY_CONFIG['authentication']['refresh_token_expiry']
    
    def create_access_token(self, user: User) -> str:
        """Create JWT access token"""
        now = get_utc_timestamp()
        expire = now + timedelta(seconds=self.jwt_expiry)
        
        payload = {
            "user_id": user.id,
            "email": user.email,
            "role": user.role,
            "permissions": user.permissions,
            "exp": expire,
            "iat": now,
            "type": "access"
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    def create_refresh_token(self, user: User) -> str:
        """Create JWT refresh token"""
        now = get_utc_timestamp()
        expire = now + timedelta(seconds=self.refresh_expiry)
        
        payload = {
            "user_id": user.id,
            "email": user.email,
            "exp": expire,
            "iat": now,
            "type": "refresh"
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    def verify_token(self, token: str) -> TokenData:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            # Check token type
            if payload.get("type") != "access":
                raise AuthenticationError("Invalid token type")
            
            # Check expiration
            exp = datetime.fromtimestamp(payload["exp"])
            if exp < get_utc_timestamp():
                raise AuthenticationError("Token has expired")
            
            return TokenData(
                user_id=payload["user_id"],
                email=payload["email"],
                role=payload["role"],
                permissions=payload["permissions"],
                exp=exp,
                iat=datetime.fromtimestamp(payload["iat"])
            )
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")
        except KeyError as e:
            raise AuthenticationError(f"Missing token field: {str(e)}")
    
    async def get_user_from_supabase(self, user_id: str) -> Optional[User]:
        """Get user details from Supabase"""
        try:
            headers = {
                "Authorization": f"Bearer {self.supabase_service_key}",
                "apikey": self.supabase_service_key,
                "Content-Type": "application/json"
            }
            
            # Get user from auth.users
            async with aiohttp.ClientSession() as session:
                # Get basic user info
                user_url = f"{self.supabase_url}/auth/v1/admin/users/{user_id}"
                async with session.get(user_url, headers=headers) as response:
                    if response.status != 200:
                        return None
                    
                    user_data = await response.json()
                
                # Get user profile with role and permissions
                profile_url = f"{self.supabase_url}/rest/v1/user_profiles"
                params = {"id": f"eq.{user_id}", "select": "*"}
                async with session.get(profile_url, headers=headers, params=params) as response:
                    profile_data = {}
                    if response.status == 200:
                        profiles = await response.json()
                        if profiles:
                            profile_data = profiles[0]
                
                return User(
                    id=user_data["id"],
                    email=user_data["email"],
                    role=profile_data.get("role", "viewer"),
                    permissions=profile_data.get("permissions", []),
                    full_name=profile_data.get("full_name"),
                    avatar_url=profile_data.get("avatar_url"),
                    is_active=not user_data.get("banned_until"),
                    created_at=datetime.fromisoformat(user_data["created_at"].replace("Z", "+00:00"))
                )
                
        except Exception as e:
            logger.error(f"Failed to get user from Supabase: {str(e)}")
            return None
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password"""
        try:
            headers = {
                "apikey": self.supabase_anon_key,
                "Content-Type": "application/json"
            }
            
            payload = {
                "email": email,
                "password": password
            }
            
            async with aiohttp.ClientSession() as session:
                auth_url = f"{self.supabase_url}/auth/v1/token?grant_type=password"
                async with session.post(auth_url, headers=headers, json=payload) as response:
                    if response.status != 200:
                        return None
                    
                    auth_data = await response.json()
                    user_id = auth_data["user"]["id"]
                    
                    return await self.get_user_from_supabase(user_id)
                    
        except Exception as e:
            logger.error(f"Authentication failed: {str(e)}")
            return None
    
    def has_permission(self, user: User, permission: str) -> bool:
        """Check if user has specific permission"""
        if user.role == "admin":
            return True
        
        return permission in user.permissions
    
    def has_role(self, user: User, role: str) -> bool:
        """Check if user has specific role"""
        role_hierarchy = {
            "admin": ["admin", "editor", "viewer"],
            "editor": ["editor", "viewer"],
            "viewer": ["viewer"]
        }
        
        return role in role_hierarchy.get(user.role, [])


# Global auth service instance
auth_service = AuthService()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """FastAPI dependency to get current authenticated user"""
    try:
        token = credentials.credentials
        token_data = auth_service.verify_token(token)
        
        # Get fresh user data from Supabase
        user = await auth_service.get_user_from_supabase(token_data.user_id)
        if not user:
            raise AuthenticationError("User not found")
        
        if not user.is_active:
            raise AuthenticationError("User account is disabled")
        
        return user
        
    except AuthenticationError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


def require_permission(permission: str):
    """Decorator to require specific permission"""
    def permission_checker(current_user: User = Depends(get_current_user)) -> User:
        if not auth_service.has_permission(current_user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    
    return permission_checker


def require_role(role: str):
    """Decorator to require specific role"""
    def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if not auth_service.has_role(current_user, role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{role}' required"
            )
        return current_user
    
    return role_checker


# Common permission dependencies
require_admin = require_role("admin")
require_editor = require_role("editor")
require_viewer = require_role("viewer")

# Specific permission dependencies
require_manage_trends = require_permission("manage_trends")
require_manage_content = require_permission("manage_content")
require_manage_deployments = require_permission("manage_deployments")
require_manage_dns = require_permission("manage_dns")
require_view_analytics = require_permission("view_analytics")
require_manage_users = require_permission("manage_users")
require_system_admin = require_permission("system_admin")

"""
Security middleware for request validation, rate limiting, and security headers
"""
import time
import hashlib
import re
from typing import Dict, Any, Optional
from collections import defaultdict, deque
from fastapi import Request, HTTPException, status
from fastapi.responses import Response
from starlette.middleware.base import BaseHTTPMiddleware
from shared.config import SECURITY_CONFIG
from shared.exceptions import RateLimitError, SecurityError, ValidationError
from shared.utils import rate_limiter
import logging

logger = logging.getLogger(__name__)


class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware"""
    
    def __init__(self, app, config: Dict[str, Any] = None):
        super().__init__(app)
        self.config = config or SECURITY_CONFIG
        self.rate_limiter = RateLimiter(self.config.get('rate_limiting', {}))
        self.request_validator = RequestValidator(self.config.get('input_validation', {}))
        self.security_headers = SecurityHeaders(self.config.get('security_headers', {}))
        
    async def dispatch(self, request: Request, call_next):
        """Process request through security layers"""
        try:
            # Rate limiting
            if self.config.get('rate_limiting', {}).get('enabled', True):
                await self.rate_limiter.check_rate_limit(request)
            
            # Request validation
            await self.request_validator.validate_request(request)
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            response = self.security_headers.add_headers(response)
            
            return response
            
        except RateLimitError:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded"
            )
        except ValidationError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        except SecurityError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )


class RateLimiter:
    """Rate limiting implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.requests = defaultdict(deque)
        self.blocked_ips = set()
        self.default_limits = config.get('default_limits', {'requests': 100, 'window': 3600})
        self.endpoint_limits = config.get('endpoint_limits', {})
        
    async def check_rate_limit(self, request: Request):
        """Check if request is within rate limits"""
        client_ip = self.get_client_ip(request)
        
        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            logger.warning(f"Blocked IP attempted request: {client_ip}")
            raise RateLimitError("IP address is temporarily blocked")
        
        current_time = time.time()
        endpoint = f"{request.method}:{request.url.path}"
        
        # Get rate limit for endpoint
        limit_config = self.get_limit_config(endpoint)
        requests_per_window = limit_config['requests']
        window_seconds = limit_config['window']
        
        # Clean old requests
        client_requests = self.requests[client_ip]
        while client_requests and client_requests[0] < current_time - window_seconds:
            client_requests.popleft()
        
        # Check if limit exceeded
        if len(client_requests) >= requests_per_window:
            # Block IP if severely exceeding limits
            if len(client_requests) > requests_per_window * 2:
                self.blocked_ips.add(client_ip)
                logger.warning(f"IP blocked for excessive requests: {client_ip}")
            
            logger.warning(f"Rate limit exceeded for {client_ip} on {endpoint}")
            raise RateLimitError("Rate limit exceeded")
        
        # Add current request
        client_requests.append(current_time)
    
    def get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        # Check for forwarded headers
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def get_limit_config(self, endpoint: str) -> Dict[str, int]:
        """Get rate limit configuration for endpoint"""
        return self.endpoint_limits.get(endpoint, self.default_limits)


class RequestValidator:
    """Request validation and security checks"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_request_size = config.get('max_request_size', 10 * 1024 * 1024)  # 10MB
        self.allowed_content_types = config.get('allowed_content_types', [
            'application/json',
            'application/x-www-form-urlencoded',
            'multipart/form-data',
            'text/plain'
        ])
        
        # Security patterns to detect
        self.suspicious_patterns = [
            # Path traversal
            r'\.\./',
            r'\.\.\\',
            
            # SQL injection
            r'\b(union|select|insert|update|delete|drop|create|alter|exec)\b',
            r'(\b(or|and)\s+\d+\s*=\s*\d+)',
            r'(--|#|/\*|\*/)',
            
            # XSS patterns
            r'<script[^>]*>',
            r'javascript:',
            r'vbscript:',
            r'on\w+\s*=',
            
            # Command injection
            r'[;&|`$]',
            r'\b(cmd|powershell|bash|sh)\b',
            
            # File inclusion
            r'/etc/passwd',
            r'/proc/',
            r'cmd\.exe',
        ]
        
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.suspicious_patterns]
    
    async def validate_request(self, request: Request):
        """Validate incoming request"""
        # Check request size
        content_length = request.headers.get('content-length')
        if content_length and int(content_length) > self.max_request_size:
            raise ValidationError("Request too large")
        
        # Check content type for POST/PUT requests
        if request.method in ['POST', 'PUT', 'PATCH']:
            content_type = request.headers.get('content-type', '').split(';')[0]
            if content_type and content_type not in self.allowed_content_types:
                raise ValidationError("Unsupported media type")
        
        # Validate headers
        await self.validate_headers(request)
        
        # Check for suspicious patterns in URL
        await self.check_suspicious_patterns(str(request.url))
        
        # Validate query parameters
        for key, value in request.query_params.items():
            await self.check_suspicious_patterns(f"{key}={value}")
    
    async def validate_headers(self, request: Request):
        """Validate request headers"""
        # Check for required headers
        if request.method in ['POST', 'PUT', 'PATCH']:
            if not request.headers.get('content-type'):
                raise ValidationError("Content-Type header required")
        
        # Check for suspicious headers
        suspicious_headers = ['x-forwarded-host', 'x-original-url', 'x-rewrite-url']
        for header in suspicious_headers:
            if header in request.headers:
                logger.warning(f"Suspicious header detected: {header}")
        
        # Validate User-Agent
        user_agent = request.headers.get('user-agent', '')
        if len(user_agent) > 500:  # Unusually long user agent
            raise ValidationError("Invalid User-Agent header")
    
    async def check_suspicious_patterns(self, text: str):
        """Check for suspicious patterns in text"""
        for pattern in self.compiled_patterns:
            if pattern.search(text):
                logger.warning(f"Suspicious pattern detected: {text[:100]}")
                raise SecurityError("Suspicious request pattern detected")


class SecurityHeaders:
    """Security headers management"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.default_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'X-Permitted-Cross-Domain-Policies': 'none',
            'X-Download-Options': 'noopen',
        }
        
        # Add HSTS if enabled
        if config.get('hsts_enabled', True):
            self.default_headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # Add CSP if enabled
        if config.get('csp_enabled', True):
            self.default_headers['Content-Security-Policy'] = self.build_csp_header()
    
    def add_headers(self, response: Response) -> Response:
        """Add security headers to response"""
        for header, value in self.default_headers.items():
            response.headers[header] = value
        
        return response
    
    def build_csp_header(self) -> str:
        """Build Content Security Policy header"""
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self'",
            "connect-src 'self' https:",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ]
        
        return '; '.join(csp_directives)


class InputSanitizer:
    """Input sanitization utilities"""
    
    @staticmethod
    def sanitize_html(content: str) -> str:
        """Sanitize HTML content"""
        if not content:
            return ''
        
        # Basic HTML escaping
        content = content.replace('&', '&amp;')
        content = content.replace('<', '&lt;')
        content = content.replace('>', '&gt;')
        content = content.replace('"', '&quot;')
        content = content.replace("'", '&#x27;')
        
        return content
    
    @staticmethod
    def sanitize_sql(content: str) -> str:
        """Sanitize content for SQL safety"""
        if not content:
            return ''
        
        # Remove or escape dangerous SQL characters
        content = content.replace("'", "''")  # Escape single quotes
        content = content.replace(';', '')    # Remove semicolons
        content = content.replace('--', '')   # Remove SQL comments
        content = content.replace('/*', '')   # Remove block comments
        content = content.replace('*/', '')
        
        return content
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename for safe file system usage"""
        if not filename:
            return ''
        
        # Remove or replace unsafe characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'\s+', '_', filename)
        filename = filename.strip('._')
        
        # Limit length
        if len(filename) > 255:
            filename = filename[:255]
        
        return filename

"""
Celery application configuration for the Trend Platform
Handles background task processing and scheduling
"""
from celery import Celery
from celery.schedules import crontab
from shared.config import settings
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Celery app
celery_app = Celery(
    'trend_platform',
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=[
        'scraper.tasks',
        'generator.tasks',
        'deploy.tasks',
        'dns.tasks',
        'housekeeping.tasks',
        'monitoring.tasks'
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Task routing
    task_routes={
        'scraper.*': {'queue': 'scraper'},
        'generator.*': {'queue': 'generator'},
        'deploy.*': {'queue': 'deploy'},
        'dns.*': {'queue': 'dns'},
        'housekeeping.*': {'queue': 'housekeeping'},
        'monitoring.*': {'queue': 'monitoring'},
    },
    
    # Worker settings
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        'master_name': 'mymaster',
        'visibility_timeout': 3600,
    },
    
    # Task execution settings
    task_soft_time_limit=300,  # 5 minutes
    task_time_limit=600,       # 10 minutes
    task_reject_on_worker_lost=True,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        # Scrape trends every 15 minutes
        'scrape-trends': {
            'task': 'scraper.tasks.scrape_trends',
            'schedule': crontab(minute='*/15'),
            'options': {'queue': 'scraper'}
        },
        
        # Generate content for approved trends every 30 minutes
        'generate-content': {
            'task': 'generator.tasks.generate_content_for_approved_trends',
            'schedule': crontab(minute='*/30'),
            'options': {'queue': 'generator'}
        },
        
        # Deploy ready content every hour
        'deploy-content': {
            'task': 'deploy.tasks.deploy_ready_content',
            'schedule': crontab(minute=0),
            'options': {'queue': 'deploy'}
        },
        
        # Cleanup expired content daily at 2 AM
        'cleanup-expired-content': {
            'task': 'housekeeping.tasks.cleanup_expired_content',
            'schedule': crontab(hour=2, minute=0),
            'options': {'queue': 'housekeeping'}
        },
        
        # DNS cleanup daily at 2:30 AM
        'dns-cleanup': {
            'task': 'housekeeping.tasks.dns_cleanup',
            'schedule': crontab(hour=2, minute=30),
            'options': {'queue': 'housekeeping'}
        },
        
        # Database maintenance weekly on Sunday at 1 AM
        'database-maintenance': {
            'task': 'housekeeping.tasks.database_maintenance',
            'schedule': crontab(hour=1, minute=0, day_of_week=0),
            'options': {'queue': 'housekeeping'}
        },
        
        # Sync analytics hourly
        'sync-analytics': {
            'task': 'monitoring.tasks.sync_analytics',
            'schedule': crontab(minute=0),
            'options': {'queue': 'monitoring'}
        },
        
        # Update system metrics every 5 minutes
        'update-system-metrics': {
            'task': 'monitoring.tasks.update_system_metrics',
            'schedule': crontab(minute='*/5'),
            'options': {'queue': 'monitoring'}
        },
        
        # Security key rotation monthly on 1st at 3 AM
        'rotate-api-keys': {
            'task': 'security.tasks.rotate_api_keys',
            'schedule': crontab(hour=3, minute=0, day_of_month=1),
            'options': {'queue': 'security'}
        }
    },
    
    # Error handling
    task_annotations={
        '*': {
            'rate_limit': '100/m',
            'time_limit': 600,
            'soft_time_limit': 300,
        },
        'scraper.tasks.scrape_trends': {
            'rate_limit': '4/h',  # Every 15 minutes
            'time_limit': 1800,   # 30 minutes
            'soft_time_limit': 1500,
        },
        'generator.tasks.generate_content': {
            'rate_limit': '10/m',
            'time_limit': 900,    # 15 minutes
            'soft_time_limit': 600,
        },
        'deploy.tasks.deploy_site': {
            'rate_limit': '5/m',
            'time_limit': 1200,   # 20 minutes
            'soft_time_limit': 900,
        }
    }
)

# Task failure handling
@celery_app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup"""
    print(f'Request: {self.request!r}')
    return 'Debug task completed'


# Error handlers
@celery_app.task(bind=True)
def handle_task_failure(self, task_id, error, traceback):
    """Handle task failures"""
    logger.error(f"Task {task_id} failed: {error}")
    # Could send notifications, update metrics, etc.


# Celery signals
from celery.signals import task_prerun, task_postrun, task_failure

@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """Handle task pre-run"""
    logger.info(f"Task {task.name} ({task_id}) starting")


@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    """Handle task post-run"""
    logger.info(f"Task {task.name} ({task_id}) completed with state: {state}")


@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    """Handle task failures"""
    logger.error(f"Task {sender.name} ({task_id}) failed: {exception}")


if __name__ == '__main__':
    celery_app.start()

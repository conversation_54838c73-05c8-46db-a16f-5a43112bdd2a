"""
Custom exceptions used across all modules
"""
from typing import Any, Dict, Optional


class TrendPlatformException(Exception):
    """Base exception for all platform-specific errors"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class ConfigurationError(TrendPlatformException):
    """Raised when there's a configuration issue"""
    pass


class DatabaseError(TrendPlatformException):
    """Raised when there's a database-related error"""
    pass


class AuthenticationError(TrendPlatformException):
    """Raised when authentication fails"""
    pass


class AuthorizationError(TrendPlatformException):
    """Raised when authorization fails"""
    pass


class ValidationError(TrendPlatformException):
    """Raised when input validation fails"""
    pass


class RateLimitError(TrendPlatformException):
    """Raised when rate limit is exceeded"""
    pass


class ExternalServiceError(TrendPlatformException):
    """Raised when external service call fails"""
    pass


class ScrapingError(TrendPlatformException):
    """Raised when trend scraping fails"""
    pass


class ContentGenerationError(TrendPlatformException):
    """Raised when content generation fails"""
    pass


class DeploymentError(TrendPlatformException):
    """Raised when deployment fails"""
    pass


class DNSError(TrendPlatformException):
    """Raised when DNS operations fail"""
    pass


class MonitoringError(TrendPlatformException):
    """Raised when monitoring operations fail"""
    pass


class SecurityError(TrendPlatformException):
    """Raised when security violations occur"""
    pass


class HousekeepingError(TrendPlatformException):
    """Raised when cleanup operations fail"""
    pass

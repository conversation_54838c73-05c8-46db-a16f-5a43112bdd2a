"""
Tests for AI service abstraction layer
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from generator.ai.base_ai_client import (
    BaseAIClient, AIRequest, AIResponse, AIServiceType, 
    AIServiceError, AIUsageStats
)
from generator.ai.openai_client import OpenAICompatibleClient
from generator.ai.anthropic_client import AnthropicClient
from generator.ai.ai_service_factory import (
    AIServiceFactory, AIProvider, get_ai_client, create_ai_client_from_config
)


class MockAIClient(BaseAIClient):
    """Mock AI client for testing"""
    
    def __init__(self, api_key: str, base_url: str, **kwargs):
        super().__init__(api_key, base_url, **kwargs)
        self.mock_responses = {}
    
    async def generate_text(self, request: AIRequest) -> AIResponse:
        if 'error' in request.prompt.lower():
            raise AIServiceError("Mock error for testing")
        
        return AIResponse(
            content="Mock generated text content",
            service_type=AIServiceType.TEXT_GENERATION,
            model_used="mock-model",
            tokens_used=100,
            response_time=0.5,
            cost=0.01
        )
    
    async def generate_image(self, request: AIRequest) -> AIResponse:
        return AIResponse(
            content="https://mock-image-url.com/image.jpg",
            service_type=AIServiceType.IMAGE_GENERATION,
            model_used="mock-image-model",
            tokens_used=0,
            response_time=1.0,
            cost=0.04
        )
    
    async def moderate_content(self, content: str) -> dict:
        return {
            'flagged': False,
            'categories': {},
            'category_scores': {}
        }
    
    def get_supported_models(self) -> dict:
        return {
            AIServiceType.TEXT_GENERATION: ['mock-text-model'],
            AIServiceType.IMAGE_GENERATION: ['mock-image-model']
        }
    
    async def validate_api_key(self) -> bool:
        return self.api_key == "valid-key"


class TestBaseAIClient:
    """Test cases for base AI client functionality"""
    
    def test_ai_usage_stats(self):
        """Test AI usage statistics tracking"""
        stats = AIUsageStats()
        
        # Test initial state
        assert stats.total_requests == 0
        assert stats.success_rate == 0.0
        assert stats.error_rate == 100.0
        
        # Test with some data
        stats.total_requests = 10
        stats.successful_requests = 8
        stats.failed_requests = 2
        
        assert stats.success_rate == 80.0
        assert stats.error_rate == 20.0
    
    def test_ai_request_validation(self):
        """Test AI request structure"""
        request = AIRequest(
            prompt="Test prompt",
            service_type=AIServiceType.TEXT_GENERATION,
            max_tokens=100,
            temperature=0.7
        )
        
        assert request.prompt == "Test prompt"
        assert request.service_type == AIServiceType.TEXT_GENERATION
        assert request.max_tokens == 100
        assert request.temperature == 0.7
        assert request.additional_params == {}
    
    def test_ai_response_structure(self):
        """Test AI response structure"""
        response = AIResponse(
            content="Generated content",
            service_type=AIServiceType.TEXT_GENERATION,
            model_used="test-model",
            tokens_used=50,
            response_time=1.5,
            cost=0.02
        )
        
        assert response.content == "Generated content"
        assert response.service_type == AIServiceType.TEXT_GENERATION
        assert response.model_used == "test-model"
        assert response.tokens_used == 50
        assert response.response_time == 1.5
        assert response.cost == 0.02
        assert response.metadata == {}


class TestMockAIClient:
    """Test cases using mock AI client"""
    
    def setup_method(self):
        """Setup test environment"""
        self.client = MockAIClient("test-key", "https://mock-api.com")
    
    @pytest.mark.asyncio
    async def test_text_generation(self):
        """Test text generation functionality"""
        request = AIRequest(
            prompt="Generate an article about AI",
            service_type=AIServiceType.TEXT_GENERATION,
            max_tokens=500
        )
        
        response = await self.client.generate_text(request)
        
        assert response.content == "Mock generated text content"
        assert response.service_type == AIServiceType.TEXT_GENERATION
        assert response.model_used == "mock-model"
        assert response.tokens_used == 100
    
    @pytest.mark.asyncio
    async def test_image_generation(self):
        """Test image generation functionality"""
        request = AIRequest(
            prompt="Generate an image of AI technology",
            service_type=AIServiceType.IMAGE_GENERATION
        )
        
        response = await self.client.generate_image(request)
        
        assert response.content == "https://mock-image-url.com/image.jpg"
        assert response.service_type == AIServiceType.IMAGE_GENERATION
        assert response.model_used == "mock-image-model"
    
    @pytest.mark.asyncio
    async def test_content_moderation(self):
        """Test content moderation functionality"""
        result = await self.client.moderate_content("This is safe content")
        
        assert result['flagged'] is False
        assert 'categories' in result
        assert 'category_scores' in result
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in AI client"""
        request = AIRequest(
            prompt="This should trigger an error",
            service_type=AIServiceType.TEXT_GENERATION
        )
        
        with pytest.raises(AIServiceError):
            await self.client.generate_text(request)
    
    @pytest.mark.asyncio
    async def test_api_key_validation(self):
        """Test API key validation"""
        valid_client = MockAIClient("valid-key", "https://mock-api.com")
        invalid_client = MockAIClient("invalid-key", "https://mock-api.com")
        
        assert await valid_client.validate_api_key() is True
        assert await invalid_client.validate_api_key() is False
    
    def test_supported_models(self):
        """Test getting supported models"""
        models = self.client.get_supported_models()
        
        assert AIServiceType.TEXT_GENERATION in models
        assert AIServiceType.IMAGE_GENERATION in models
        assert 'mock-text-model' in models[AIServiceType.TEXT_GENERATION]
        assert 'mock-image-model' in models[AIServiceType.IMAGE_GENERATION]
    
    @pytest.mark.asyncio
    async def test_usage_stats_tracking(self):
        """Test usage statistics tracking"""
        initial_stats = self.client.get_usage_stats()
        assert initial_stats.total_requests == 0
        
        # Make a successful request
        request = AIRequest(
            prompt="Test prompt",
            service_type=AIServiceType.TEXT_GENERATION
        )
        
        response = await self.client.generate_text(request)
        
        # Check updated stats
        updated_stats = self.client.get_usage_stats()
        assert updated_stats.total_requests == 1
        assert updated_stats.successful_requests == 1
        assert updated_stats.total_tokens_used == 100
        assert updated_stats.total_cost == 0.01
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test health check functionality"""
        health = await self.client.health_check()
        
        assert 'status' in health
        assert 'api_key_valid' in health
        assert 'base_url' in health
        assert 'usage_stats' in health
        assert 'timestamp' in health


class TestAIServiceFactory:
    """Test cases for AI service factory"""
    
    def setup_method(self):
        """Setup test environment"""
        self.factory = AIServiceFactory()
        # Register mock client for testing
        self.factory.register_provider(AIProvider.CUSTOM, MockAIClient)
    
    def test_create_openai_client(self):
        """Test creating OpenAI client"""
        client = self.factory.create_client(
            provider=AIProvider.OPENAI,
            api_key="test-key",
            base_url="https://api.openai.com/v1"
        )
        
        assert isinstance(client, OpenAICompatibleClient)
        assert client.api_key == "test-key"
        assert client.base_url == "https://api.openai.com/v1"
    
    def test_create_anthropic_client(self):
        """Test creating Anthropic client"""
        client = self.factory.create_client(
            provider=AIProvider.ANTHROPIC,
            api_key="test-key",
            base_url="https://api.anthropic.com"
        )
        
        assert isinstance(client, AnthropicClient)
        assert client.api_key == "test-key"
        assert client.base_url == "https://api.anthropic.com"
    
    def test_create_custom_client(self):
        """Test creating custom client"""
        client = self.factory.create_client(
            provider=AIProvider.CUSTOM,
            api_key="test-key",
            base_url="https://custom-api.com"
        )
        
        assert isinstance(client, MockAIClient)
        assert client.api_key == "test-key"
        assert client.base_url == "https://custom-api.com"
    
    def test_unsupported_provider(self):
        """Test error handling for unsupported provider"""
        with pytest.raises(AIServiceError):
            self.factory.create_client(
                provider="unsupported",  # Invalid provider
                api_key="test-key"
            )
    
    def test_missing_api_key(self):
        """Test error handling for missing API key"""
        with pytest.raises(AIServiceError):
            self.factory.create_client(
                provider=AIProvider.OPENAI,
                api_key=""  # Empty API key
            )
    
    def test_client_caching(self):
        """Test client caching functionality"""
        # Create first client
        client1 = self.factory.get_or_create_client(
            provider=AIProvider.CUSTOM,
            api_key="test-key",
            base_url="https://test-api.com",
            cache_key="test-client"
        )
        
        # Get same client (should be cached)
        client2 = self.factory.get_or_create_client(
            provider=AIProvider.CUSTOM,
            api_key="test-key",
            base_url="https://test-api.com",
            cache_key="test-client"
        )
        
        # Should be the same instance
        assert client1 is client2
    
    def test_create_from_config(self):
        """Test creating client from configuration"""
        config = {
            "provider": "custom",
            "api_key": "test-key",
            "base_url": "https://config-api.com",
            "text_model": "custom-model"
        }
        
        client = self.factory.create_from_config(config)
        
        assert isinstance(client, MockAIClient)
        assert client.api_key == "test-key"
        assert client.base_url == "https://config-api.com"
    
    def test_supported_providers(self):
        """Test getting supported providers"""
        providers = self.factory.get_supported_providers()
        
        assert AIProvider.OPENAI in providers
        assert AIProvider.ANTHROPIC in providers
        assert AIProvider.CUSTOM in providers
    
    def test_clear_cache(self):
        """Test clearing client cache"""
        # Create a client
        self.factory.get_or_create_client(
            provider=AIProvider.CUSTOM,
            api_key="test-key",
            cache_key="test-client"
        )
        
        # Verify client is cached
        assert len(self.factory._clients) > 0
        
        # Clear cache
        self.factory.clear_cache()
        
        # Verify cache is empty
        assert len(self.factory._clients) == 0
    
    @pytest.mark.asyncio
    async def test_health_check_all(self):
        """Test health check for all cached clients"""
        # Create a client
        self.factory.get_or_create_client(
            provider=AIProvider.CUSTOM,
            api_key="valid-key",
            cache_key="test-client"
        )
        
        # Perform health check
        health_results = await self.factory.health_check_all()
        
        assert "test-client" in health_results
        assert health_results["test-client"]["status"] == "healthy"
    
    def test_client_stats(self):
        """Test getting client statistics"""
        # Create a client
        self.factory.get_or_create_client(
            provider=AIProvider.CUSTOM,
            api_key="test-key",
            cache_key="test-client"
        )
        
        # Get stats
        stats = self.factory.get_client_stats()
        
        assert stats["total_clients"] == 1
        assert "test-client" in stats["clients"]
        assert "client_type" in stats["clients"]["test-client"]
        assert "usage_stats" in stats["clients"]["test-client"]


class TestConvenienceFunctions:
    """Test convenience functions"""
    
    @patch('generator.ai.ai_service_factory.settings')
    def test_get_ai_client_default(self, mock_settings):
        """Test getting default AI client"""
        mock_settings.openai_api_key = "test-key"
        mock_settings.openai_base_url = "https://api.openai.com/v1"
        
        client = get_ai_client()
        
        assert isinstance(client, OpenAICompatibleClient)
        assert client.api_key == "test-key"
    
    def test_create_ai_client_from_config(self):
        """Test creating client from config"""
        config = {
            "provider": "openai",
            "api_key": "test-key",
            "base_url": "https://api.openai.com/v1"
        }
        
        client = create_ai_client_from_config(config)
        
        assert isinstance(client, OpenAICompatibleClient)
        assert client.api_key == "test-key"


if __name__ == "__main__":
    pytest.main([__file__])

"""
Tests for asset management system
"""

import pytest
import tempfile
import shutil
import os
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from PIL import Image
import io

from generator.assets import (
    AssetManager, ImageProcessingOptions, AssetProcessingResult, AssetProcessingError
)
from generator.asset_integration import ContentAssetProcessor, AssetIntegrationError
from generator.models import GeneratedContent, ContentAsset


class TestImageProcessingOptions:
    """Test image processing options"""
    
    def test_default_options(self):
        """Test default processing options"""
        options = ImageProcessingOptions()
        
        assert options.max_width == 1200
        assert options.max_height == 800
        assert options.quality == 85
        assert options.format == "JPEG"
        assert options.optimize is True
        assert options.progressive is True
        assert options.strip_metadata is True
    
    def test_custom_options(self):
        """Test custom processing options"""
        options = ImageProcessingOptions(
            max_width=800,
            max_height=600,
            quality=90,
            format="PNG",
            optimize=False
        )
        
        assert options.max_width == 800
        assert options.max_height == 600
        assert options.quality == 90
        assert options.format == "PNG"
        assert options.optimize is False
    
    @patch('generator.assets.GENERATOR_CONFIG')
    def test_from_config(self, mock_config):
        """Test creating options from configuration"""
        mock_config.get.return_value = {
            'max_image_width': 1000,
            'image_quality': 80,
            'image_optimization': False
        }
        
        options = ImageProcessingOptions.from_config()
        
        assert options.max_width == 1000
        assert options.quality == 80
        assert options.optimize is False


class TestAssetManager:
    """Test asset manager functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.manager = AssetManager(storage_dir=self.temp_dir)
        
        # Create test image
        self.test_image_path = os.path.join(self.temp_dir, "test_image.jpg")
        self._create_test_image(self.test_image_path, (800, 600))
    
    def teardown_method(self):
        """Cleanup test environment"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def _create_test_image(self, path: str, size: tuple = (800, 600), format: str = "JPEG"):
        """Create a test image file"""
        img = Image.new('RGB', size, color='red')
        img.save(path, format=format)
    
    @patch('generator.assets.AsyncHTTPClient')
    @pytest.mark.asyncio
    async def test_download_image_success(self, mock_http_client):
        """Test successful image download"""
        # Mock HTTP response
        mock_client = AsyncMock()
        mock_http_client.return_value.__aenter__.return_value = mock_client
        
        # Read test image data
        with open(self.test_image_path, 'rb') as f:
            image_data = f.read()
        
        mock_client.get.return_value = {
            'status': 200,
            'data': image_data,
            'headers': {'content-type': 'image/jpeg'}
        }
        
        # Download image
        asset = await self.manager.download_image(
            "https://example.com/test.jpg",
            "downloaded_image"
        )
        
        assert asset.filename == "downloaded_image.jpg"
        assert asset.original_url == "https://example.com/test.jpg"
        assert asset.mime_type == "image/jpeg"
        assert asset.width == 800
        assert asset.height == 600
        assert os.path.exists(asset.local_path)
    
    @patch('generator.assets.AsyncHTTPClient')
    @pytest.mark.asyncio
    async def test_download_image_failure(self, mock_http_client):
        """Test image download failure"""
        mock_client = AsyncMock()
        mock_http_client.return_value.__aenter__.return_value = mock_client
        
        mock_client.get.return_value = {
            'status': 404,
            'data': None
        }
        
        with pytest.raises(AssetProcessingError):
            await self.manager.download_image("https://example.com/notfound.jpg")
    
    @pytest.mark.asyncio
    async def test_process_image_resize(self):
        """Test image processing with resizing"""
        # Create large test image
        large_image_path = os.path.join(self.temp_dir, "large_image.jpg")
        self._create_test_image(large_image_path, (2000, 1500))
        
        asset = ContentAsset(
            filename="large_image.jpg",
            local_path=large_image_path,
            file_size=os.path.getsize(large_image_path),
            mime_type="image/jpeg",
            width=2000,
            height=1500
        )
        
        options = ImageProcessingOptions(max_width=1200, max_height=800)
        result = await self.manager.process_image(asset, options)
        
        assert result.success is True
        assert result.processed_asset is not None
        assert result.processed_asset.width <= 1200
        assert result.processed_asset.height <= 800
        assert result.size_reduction > 0
    
    @pytest.mark.asyncio
    async def test_process_image_webp_conversion(self):
        """Test WebP conversion during processing"""
        asset = ContentAsset(
            filename="test_image.jpg",
            local_path=self.test_image_path,
            file_size=os.path.getsize(self.test_image_path),
            mime_type="image/jpeg",
            width=800,
            height=600
        )
        
        with patch('generator.assets.GENERATOR_CONFIG') as mock_config:
            mock_config.get.return_value = {'webp_conversion': True}
            
            result = await self.manager.process_image(asset)
            
            assert result.success is True
            assert result.webp_asset is not None
            assert result.webp_asset.mime_type == "image/webp"
            assert result.webp_asset.filename.endswith(".webp")
    
    @pytest.mark.asyncio
    async def test_process_image_no_webp(self):
        """Test processing without WebP conversion"""
        asset = ContentAsset(
            filename="test_image.jpg",
            local_path=self.test_image_path,
            file_size=os.path.getsize(self.test_image_path),
            mime_type="image/jpeg",
            width=800,
            height=600
        )
        
        with patch('generator.assets.GENERATOR_CONFIG') as mock_config:
            mock_config.get.return_value = {'webp_conversion': False}
            
            result = await self.manager.process_image(asset)
            
            assert result.success is True
            assert result.webp_asset is None
            assert result.processed_asset is not None
    
    @pytest.mark.asyncio
    async def test_process_image_file_not_found(self):
        """Test processing non-existent image file"""
        asset = ContentAsset(
            filename="nonexistent.jpg",
            local_path="/nonexistent/path.jpg",
            file_size=0,
            mime_type="image/jpeg"
        )
        
        result = await self.manager.process_image(asset)
        
        assert result.success is False
        assert "not found" in result.error_message.lower()
    
    @patch('generator.assets.AsyncHTTPClient')
    @pytest.mark.asyncio
    async def test_download_and_process(self, mock_http_client):
        """Test combined download and process operation"""
        mock_client = AsyncMock()
        mock_http_client.return_value.__aenter__.return_value = mock_client
        
        with open(self.test_image_path, 'rb') as f:
            image_data = f.read()
        
        mock_client.get.return_value = {
            'status': 200,
            'data': image_data,
            'headers': {'content-type': 'image/jpeg'}
        }
        
        result = await self.manager.download_and_process(
            "https://example.com/test.jpg",
            "processed_image"
        )
        
        assert result.success is True
        assert result.original_asset is not None
        assert result.processed_asset is not None
        assert result.original_asset.filename == "processed_image.jpg"
    
    def test_get_extension_from_content_type(self):
        """Test content type to extension mapping"""
        assert self.manager._get_extension_from_content_type("image/jpeg") == ".jpg"
        assert self.manager._get_extension_from_content_type("image/png") == ".png"
        assert self.manager._get_extension_from_content_type("image/webp") == ".webp"
        assert self.manager._get_extension_from_content_type("text/plain") == ""
    
    def test_get_extension_from_url(self):
        """Test URL to extension extraction"""
        assert self.manager._get_extension_from_url("https://example.com/image.jpg") == ".jpg"
        assert self.manager._get_extension_from_url("https://example.com/image.png?size=large") == ".png"
        assert self.manager._get_extension_from_url("https://example.com/page.html") == ""
    
    def test_get_mime_type_from_extension(self):
        """Test extension to MIME type mapping"""
        assert self.manager._get_mime_type_from_extension(".jpg") == "image/jpeg"
        assert self.manager._get_mime_type_from_extension(".png") == "image/png"
        assert self.manager._get_mime_type_from_extension(".webp") == "image/webp"
        assert self.manager._get_mime_type_from_extension(".txt") == "application/octet-stream"
    
    def test_get_image_dimensions(self):
        """Test image dimension extraction"""
        width, height = self.manager._get_image_dimensions(self.test_image_path)
        assert width == 800
        assert height == 600
        
        # Test with non-existent file
        width, height = self.manager._get_image_dimensions("/nonexistent.jpg")
        assert width == 0
        assert height == 0
    
    @pytest.mark.asyncio
    async def test_cleanup_temp_files(self):
        """Test temporary file cleanup"""
        # Create some test files with different ages
        old_file = os.path.join(self.temp_dir, "old_file.jpg")
        new_file = os.path.join(self.temp_dir, "new_file.jpg")
        
        Path(old_file).touch()
        Path(new_file).touch()
        
        # Modify old file timestamp
        import time
        old_time = time.time() - (25 * 3600)  # 25 hours ago
        os.utime(old_file, (old_time, old_time))
        
        await self.manager.cleanup_temp_files(max_age_hours=24)
        
        # Old file should be deleted, new file should remain
        assert not os.path.exists(old_file)
        assert os.path.exists(new_file)
    
    def test_get_storage_stats(self):
        """Test storage statistics"""
        # Create some test files
        for i in range(3):
            test_file = os.path.join(self.temp_dir, f"test_{i}.jpg")
            self._create_test_image(test_file)
        
        stats = self.manager.get_storage_stats()
        
        assert stats['exists'] is True
        assert stats['file_count'] >= 3  # At least our test files
        assert stats['total_size_bytes'] > 0
        assert stats['total_size_mb'] > 0


class TestContentAssetProcessor:
    """Test content asset processor"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock asset manager
        self.mock_asset_manager = Mock()
        self.processor = ContentAssetProcessor(self.mock_asset_manager)
    
    def teardown_method(self):
        """Cleanup test environment"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @pytest.mark.asyncio
    async def test_process_content_assets_with_hero_image(self):
        """Test processing content with hero image"""
        content = GeneratedContent(
            trend_id="test-trend",
            title="Test Article",
            description="Test description",
            body="Test content body",
            slug="test-article",
            hero_image_url="https://example.com/hero.jpg",
            hero_image_alt="Hero image"
        )
        
        # Mock asset processing result
        mock_result = AssetProcessingResult(
            success=True,
            processed_asset=ContentAsset(
                filename="hero_test-article_optimized.jpg",
                local_path="/tmp/hero_optimized.jpg",
                mime_type="image/jpeg",
                width=1200,
                height=800
            ),
            webp_asset=ContentAsset(
                filename="hero_test-article_optimized.webp",
                local_path="/tmp/hero_optimized.webp",
                mime_type="image/webp",
                width=1200,
                height=800
            )
        )
        
        self.mock_asset_manager.download_and_process.return_value = mock_result
        
        updated_content, assets = await self.processor.process_content_assets(content)
        
        assert len(assets) == 2  # JPEG and WebP versions
        assert updated_content.hero_image_url != content.hero_image_url  # Should be updated
        self.mock_asset_manager.download_and_process.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_content_assets_no_hero_image(self):
        """Test processing content without hero image"""
        content = GeneratedContent(
            trend_id="test-trend",
            title="Test Article",
            description="Test description",
            body="Test content body",
            slug="test-article"
        )
        
        updated_content, assets = await self.processor.process_content_assets(content)
        
        assert len(assets) == 0
        assert updated_content == content  # Should be unchanged
        self.mock_asset_manager.download_and_process.assert_not_called()
    
    def test_extract_image_urls_from_content(self):
        """Test image URL extraction from content"""
        content_body = """
        # Test Article
        
        Here's an image: ![Test](https://example.com/image1.jpg)
        
        And another: <img src="https://example.com/image2.png" alt="Test">
        
        Direct URL: https://example.com/image3.webp
        """
        
        urls = self.processor._extract_image_urls_from_content(content_body)
        
        assert "https://example.com/image1.jpg" in urls
        assert "https://example.com/image2.png" in urls
        assert "https://example.com/image3.webp" in urls
    
    def test_is_ai_generated_image(self):
        """Test AI-generated image detection"""
        ai_urls = [
            "https://oaidalleapiprodscus.blob.core.windows.net/private/image.jpg",
            "https://cdn.openai.com/image.png",
            "https://images.unsplash.com/photo-123"
        ]
        
        non_ai_urls = [
            "https://example.com/image.jpg",
            "https://mysite.com/photo.png"
        ]
        
        for url in ai_urls:
            assert self.processor._is_ai_generated_image(url) is True
        
        for url in non_ai_urls:
            assert self.processor._is_ai_generated_image(url) is False
    
    def test_is_valid_image_url(self):
        """Test image URL validation"""
        valid_urls = [
            "https://example.com/image.jpg",
            "http://site.com/photo.png",
            "https://cdn.example.com/pic.webp"
        ]
        
        invalid_urls = [
            "not-a-url",
            "https://example.com/page.html",
            "ftp://example.com/image.jpg"
        ]
        
        for url in valid_urls:
            assert self.processor._is_valid_image_url(url) is True
        
        for url in invalid_urls:
            assert self.processor._is_valid_image_url(url) is False
    
    @pytest.mark.asyncio
    async def test_prepare_assets_for_deployment(self):
        """Test preparing assets for deployment"""
        # Create test asset files
        test_file1 = os.path.join(self.temp_dir, "asset1.jpg")
        test_file2 = os.path.join(self.temp_dir, "asset2.webp")
        
        Path(test_file1).touch()
        Path(test_file2).touch()
        
        assets = [
            ContentAsset(
                filename="asset1.jpg",
                local_path=test_file1,
                optimized_path=test_file1,
                mime_type="image/jpeg",
                file_size=1000
            ),
            ContentAsset(
                filename="asset2.webp",
                local_path=test_file2,
                mime_type="image/webp",
                file_size=800
            )
        ]
        
        deployment_assets = await self.processor.prepare_assets_for_deployment(assets)
        
        assert len(deployment_assets) == 2
        assert all(isinstance(asset, ContentAsset) for asset in deployment_assets)
        assert deployment_assets[0].filename == "asset1.jpg"
        assert deployment_assets[1].filename == "asset2.webp"
    
    @pytest.mark.asyncio
    async def test_cleanup_processed_assets(self):
        """Test asset cleanup"""
        # Create test files
        original_file = os.path.join(self.temp_dir, "original.jpg")
        optimized_file = os.path.join(self.temp_dir, "optimized.jpg")
        
        Path(original_file).touch()
        Path(optimized_file).touch()
        
        assets = [
            ContentAsset(
                filename="test.jpg",
                local_path=original_file,
                optimized_path=optimized_file,
                mime_type="image/jpeg"
            )
        ]
        
        # Cleanup with keep_optimized=True
        await self.processor.cleanup_processed_assets(assets, keep_optimized=True)
        
        # Original should be deleted, optimized should remain
        assert not os.path.exists(original_file)
        assert os.path.exists(optimized_file)
    
    @pytest.mark.asyncio
    async def test_get_asset_stats(self):
        """Test getting asset statistics"""
        self.mock_asset_manager.get_storage_stats.return_value = {
            'exists': True,
            'file_count': 5,
            'total_size_mb': 10.5
        }
        
        stats = await self.processor.get_asset_stats()
        
        assert 'storage' in stats
        assert 'processing_options' in stats
        assert 'supported_formats' in stats
        assert 'timestamp' in stats
        assert stats['storage']['file_count'] == 5


if __name__ == "__main__":
    pytest.main([__file__])

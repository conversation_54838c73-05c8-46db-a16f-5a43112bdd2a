"""
Tests for git operations and deployment functionality
"""

import pytest
import tempfile
import shutil
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from generator.git_ops import (
    GitContentManager, GitRepositoryConfig, GitDeploymentResult, GitOperationError
)
from generator.deployment_service import ContentDeploymentService, DeploymentResult
from generator.webhook_handler import GitWebhookHandler, WebhookPayload
from generator.models import GeneratedContent, ContentAsset, TemplateType
from database.models.trend_model import TrendEntity


class TestGitRepositoryConfig:
    """Test git repository configuration"""
    
    def test_basic_config(self):
        """Test basic repository configuration"""
        config = GitRepositoryConfig(
            url="https://github.com/user/repo.git",
            branch="main",
            content_directory="content"
        )
        
        assert config.url == "https://github.com/user/repo.git"
        assert config.branch == "main"
        assert config.content_directory == "content"
    
    def test_authenticated_url_https(self):
        """Test authenticated URL generation for HTTPS"""
        config = GitRepositoryConfig(
            url="https://github.com/user/repo.git",
            username="testuser",
            token="testtoken"
        )
        
        expected = "https://testuser:<EMAIL>/user/repo.git"
        assert config.authenticated_url == expected
    
    def test_authenticated_url_ssh(self):
        """Test authenticated URL for SSH (no modification)"""
        config = GitRepositoryConfig(
            url="**************:user/repo.git",
            username="testuser",
            token="testtoken"
        )
        
        # SSH URLs should remain unchanged
        assert config.authenticated_url == "**************:user/repo.git"
    
    def test_authenticated_url_no_credentials(self):
        """Test URL without credentials"""
        config = GitRepositoryConfig(
            url="https://github.com/user/repo.git"
        )
        
        assert config.authenticated_url == "https://github.com/user/repo.git"


class TestGitContentManager:
    """Test git content manager functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = GitRepositoryConfig(
            url="https://github.com/test/repo.git",
            branch="main",
            content_directory="content",
            assets_directory="assets"
        )
        self.manager = GitContentManager(self.config)
    
    def teardown_method(self):
        """Cleanup test environment"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        self.manager.cleanup()
    
    @patch('generator.git_ops.Repo')
    @pytest.mark.asyncio
    async def test_clone_repository_success(self, mock_repo_class):
        """Test successful repository cloning"""
        mock_repo = Mock()
        mock_config_writer = Mock()
        mock_repo.config_writer.return_value.__enter__.return_value = mock_config_writer
        mock_repo_class.clone_from.return_value = mock_repo
        
        result_path = await self.manager.clone_repository(self.temp_dir)
        
        assert result_path == self.temp_dir
        mock_repo_class.clone_from.assert_called_once()
        mock_config_writer.set_value.assert_called()
    
    @patch('generator.git_ops.Repo')
    @pytest.mark.asyncio
    async def test_clone_repository_failure(self, mock_repo_class):
        """Test repository cloning failure"""
        from git import GitCommandError
        mock_repo_class.clone_from.side_effect = GitCommandError("clone", "error")
        
        with pytest.raises(GitOperationError):
            await self.manager.clone_repository(self.temp_dir)
    
    @pytest.mark.asyncio
    async def test_create_content_file(self):
        """Test content file creation"""
        # Create test content
        content = GeneratedContent(
            trend_id="test-trend",
            title="Test Article",
            description="Test description",
            body="# Test Article\n\nThis is test content.",
            slug="test-article"
        )
        
        # Create content directory
        content_dir = Path(self.temp_dir) / "content"
        content_dir.mkdir(parents=True)
        
        # Create content file
        file_path = await self.manager.create_content_file(self.temp_dir, content)
        
        assert file_path == "content/test-article.mdx"
        
        # Verify file exists and has correct content
        full_path = Path(self.temp_dir) / file_path
        assert full_path.exists()
        
        with open(full_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        assert "# Test Article" in file_content
        assert "This is test content." in file_content
    
    @pytest.mark.asyncio
    async def test_create_content_file_custom_filename(self):
        """Test content file creation with custom filename"""
        content = GeneratedContent(
            trend_id="test-trend",
            title="Test Article",
            description="Test description",
            body="Test content",
            slug="test-article"
        )
        
        content_dir = Path(self.temp_dir) / "content"
        content_dir.mkdir(parents=True)
        
        file_path = await self.manager.create_content_file(
            self.temp_dir, content, "custom-name.mdx"
        )
        
        assert file_path == "content/custom-name.mdx"
        assert Path(self.temp_dir, file_path).exists()
    
    @pytest.mark.asyncio
    async def test_create_asset_files(self):
        """Test asset file creation"""
        # Create test asset files
        asset1_path = Path(self.temp_dir) / "test_asset1.jpg"
        asset2_path = Path(self.temp_dir) / "test_asset2.png"
        
        asset1_path.write_text("fake image data 1")
        asset2_path.write_text("fake image data 2")
        
        assets = [
            ContentAsset(
                filename="image1.jpg",
                local_path=str(asset1_path),
                mime_type="image/jpeg"
            ),
            ContentAsset(
                filename="image2.png",
                local_path=str(asset2_path),
                mime_type="image/png"
            )
        ]
        
        # Create assets directory
        assets_dir = Path(self.temp_dir) / "repo" / "assets"
        assets_dir.mkdir(parents=True)
        
        repo_path = Path(self.temp_dir) / "repo"
        created_files = await self.manager.create_asset_files(str(repo_path), assets)
        
        assert len(created_files) == 2
        assert "assets/image1.jpg" in created_files
        assert "assets/image2.png" in created_files
        
        # Verify files exist
        assert (repo_path / "assets" / "image1.jpg").exists()
        assert (repo_path / "assets" / "image2.png").exists()
    
    @patch('generator.git_ops.Repo')
    @pytest.mark.asyncio
    async def test_commit_and_push_success(self, mock_repo_class):
        """Test successful commit and push"""
        mock_repo = Mock()
        mock_index = Mock()
        mock_commit = Mock()
        mock_commit.hexsha = "abc123def456"
        mock_remote = Mock()
        
        mock_repo.index = mock_index
        mock_index.diff.return_value = ["some_change"]  # Simulate changes
        mock_index.commit.return_value = mock_commit
        mock_repo.head.commit = mock_commit
        mock_repo.remote.return_value = mock_remote
        
        mock_repo_class.return_value = mock_repo
        
        commit_hash = await self.manager.commit_and_push(
            self.temp_dir,
            ["content/test.mdx"],
            "Test commit message"
        )
        
        assert commit_hash == "abc123def456"
        mock_index.add.assert_called()
        mock_index.commit.assert_called_with("Test commit message")
        mock_remote.push.assert_called()
    
    @patch('generator.git_ops.Repo')
    @pytest.mark.asyncio
    async def test_commit_no_changes(self, mock_repo_class):
        """Test commit when no changes exist"""
        mock_repo = Mock()
        mock_index = Mock()
        mock_commit = Mock()
        mock_commit.hexsha = "existing123"
        
        mock_repo.index = mock_index
        mock_index.diff.return_value = []  # No changes
        mock_repo.head.commit = mock_commit
        
        mock_repo_class.return_value = mock_repo
        
        commit_hash = await self.manager.commit_and_push(
            self.temp_dir,
            ["content/test.mdx"],
            "Test commit message",
            push=False
        )
        
        assert commit_hash == "existing123"
        mock_index.commit.assert_not_called()  # Should not commit if no changes
    
    @patch('generator.git_ops.GitContentManager.clone_repository')
    @patch('generator.git_ops.GitContentManager.create_content_file')
    @patch('generator.git_ops.GitContentManager.commit_and_push')
    @pytest.mark.asyncio
    async def test_deploy_content_success(self, mock_commit, mock_create_file, mock_clone):
        """Test successful content deployment"""
        mock_clone.return_value = "/tmp/repo"
        mock_create_file.return_value = "content/test-article.mdx"
        mock_commit.return_value = "abc123def456"
        
        content = GeneratedContent(
            trend_id="test-trend",
            title="Test Article",
            description="Test description",
            body="Test content",
            slug="test-article"
        )
        
        result = await self.manager.deploy_content(content)
        
        assert result.success is True
        assert result.commit_hash == "abc123def456"
        assert "content/test-article.mdx" in result.files_created
        
        mock_clone.assert_called_once()
        mock_create_file.assert_called_once()
        mock_commit.assert_called_once()
    
    @patch('generator.git_ops.GitContentManager.clone_repository')
    @pytest.mark.asyncio
    async def test_deploy_content_failure(self, mock_clone):
        """Test content deployment failure"""
        mock_clone.side_effect = GitOperationError("Clone failed")
        
        content = GeneratedContent(
            trend_id="test-trend",
            title="Test Article",
            description="Test description",
            body="Test content",
            slug="test-article"
        )
        
        result = await self.manager.deploy_content(content)
        
        assert result.success is False
        assert "Clone failed" in result.error_message
    
    @patch('generator.git_ops.GitContentManager.clone_repository')
    @pytest.mark.asyncio
    async def test_check_repository_status_success(self, mock_clone):
        """Test repository status check success"""
        mock_clone.return_value = "/tmp/repo"
        
        with patch('generator.git_ops.Repo') as mock_repo_class:
            mock_repo = Mock()
            mock_commit = Mock()
            mock_commit.hexsha = "abc123"
            mock_commit.committed_datetime = datetime(2024, 1, 1, 12, 0, 0)
            mock_repo.head.commit = mock_commit
            mock_repo_class.return_value = mock_repo
            
            status = await self.manager.check_repository_status()
            
            assert status['accessible'] is True
            assert status['latest_commit'] == "abc123"
            assert status['url'] == self.config.url
    
    @patch('generator.git_ops.GitContentManager.clone_repository')
    @pytest.mark.asyncio
    async def test_check_repository_status_failure(self, mock_clone):
        """Test repository status check failure"""
        mock_clone.side_effect = GitOperationError("Access denied")
        
        status = await self.manager.check_repository_status()
        
        assert status['accessible'] is False
        assert "Access denied" in status['error']


class TestWebhookHandler:
    """Test webhook handler functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.handler = GitWebhookHandler()
    
    def test_parse_github_payload(self):
        """Test GitHub webhook payload parsing"""
        payload = {
            'repository': {
                'clone_url': 'https://github.com/user/repo.git'
            },
            'ref': 'refs/heads/main',
            'head_commit': {
                'id': 'abc123def456',
                'message': 'Add new content',
                'author': {'name': 'Test User'},
                'added': ['content/new-article.mdx'],
                'modified': ['content/existing.mdx'],
                'removed': []
            }
        }
        
        parsed = self.handler._parse_github_payload(payload)
        
        assert parsed.event_type == "push"
        assert parsed.repository_url == 'https://github.com/user/repo.git'
        assert parsed.branch == "main"
        assert parsed.commit_hash == 'abc123def456'
        assert parsed.commit_message == 'Add new content'
        assert parsed.author == 'Test User'
        assert 'content/new-article.mdx' in parsed.files_changed
        assert 'content/existing.mdx' in parsed.files_changed
    
    def test_should_trigger_deployment(self):
        """Test deployment trigger logic"""
        # Should trigger for push to main
        payload = WebhookPayload(
            event_type="push",
            repository_url="https://github.com/user/repo.git",
            branch="main",
            commit_hash="abc123",
            commit_message="Add new content",
            author="Test User",
            timestamp=datetime.utcnow(),
            files_changed=[],
            raw_payload={}
        )
        
        assert self.handler._should_trigger_deployment(payload) is True
        
        # Should not trigger for feature branch
        payload.branch = "feature/test"
        assert self.handler._should_trigger_deployment(payload) is False
        
        # Should not trigger for non-push events
        payload.branch = "main"
        payload.event_type = "pull_request"
        assert self.handler._should_trigger_deployment(payload) is False
        
        # Should not trigger with [skip ci] in message
        payload.event_type = "push"
        payload.commit_message = "Add content [skip ci]"
        assert self.handler._should_trigger_deployment(payload) is False
    
    def test_check_content_files_changed(self):
        """Test content file change detection"""
        # Content files should trigger
        content_files = [
            'content/article.mdx',
            'data/config.yaml',
            'posts/blog-post.md'
        ]
        
        assert self.handler._check_content_files_changed(content_files) is True
        
        # Non-content files should not trigger
        other_files = [
            'src/components/Header.tsx',
            'package.json',
            'README.md'
        ]
        
        assert self.handler._check_content_files_changed(other_files) is False
        
        # Mixed files should trigger if any content files present
        mixed_files = content_files + other_files
        assert self.handler._check_content_files_changed(mixed_files) is True


@pytest.mark.asyncio
class TestContentDeploymentService:
    """Test content deployment service"""
    
    def setup_method(self):
        """Setup test environment"""
        self.service = ContentDeploymentService()
    
    @patch('generator.deployment_service.content_pipeline')
    @patch('generator.deployment_service.git_content_manager')
    async def test_deploy_trend_content_success(self, mock_git_manager, mock_pipeline):
        """Test successful trend content deployment"""
        # Mock trend
        trend = TrendEntity(
            id="test-trend-id",
            keyword="Test Trend",
            category="Technology",
            region="US",
            search_volume=1000,
            growth_rate=25.0,
            score=85.0,
            status="approved"
        )
        
        # Mock generated content
        generated_content = GeneratedContent(
            trend_id=trend.id,
            title="Understanding Test Trend",
            description="A comprehensive guide to test trends",
            body="# Understanding Test Trend\n\nThis is test content.",
            slug="understanding-test-trend"
        )
        
        # Mock pipeline result
        mock_generation_result = Mock()
        mock_generation_result.success = True
        mock_generation_result.content = generated_content
        mock_generation_result.assets = []
        mock_pipeline.generate_content.return_value = mock_generation_result
        
        # Mock git deployment result
        mock_git_result = GitDeploymentResult(
            success=True,
            commit_hash="abc123def456",
            files_created=["content/understanding-test-trend.mdx"]
        )
        mock_git_manager.deploy_content.return_value = mock_git_result
        
        # Mock database operations
        with patch.object(self.service.content_repo, 'get_by_trend_id', return_value=None), \
             patch.object(self.service.content_repo, 'create', return_value="content-id"), \
             patch.object(self.service.trend_repo, 'update', return_value=None):
            
            result = await self.service.deploy_trend_content(trend)
        
        assert result.success is True
        assert result.commit_hash == "abc123def456"
        assert len(result.files_deployed) == 1
        assert "content/understanding-test-trend.mdx" in result.files_deployed


if __name__ == "__main__":
    pytest.main([__file__])

"""
Tests for complete pipeline integration
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from generator.pipeline_orchestrator import (
    ContentPipelineOrchestrator, PipelineConfig, PipelineMode, PipelineStage, PipelineResult
)
from generator.models import (
    ContentRequest, GeneratedContent, ContentGenerationResult, 
    TemplateType, ContentStatus, ContentAsset
)
from database.models.trend_model import TrendEntity


class TestPipelineConfig:
    """Test pipeline configuration"""
    
    def test_default_config(self):
        """Test default pipeline configuration"""
        config = PipelineConfig()
        
        assert config.mode == PipelineMode.FULL_PIPELINE
        assert config.template_type == TemplateType.ARTICLE
        assert config.include_assets is True
        assert config.include_code is True
        assert config.force_regenerate is False
        assert config.auto_deploy is True
        assert config.cleanup_assets is True
        assert config.max_concurrent == 3
        assert config.timeout_seconds == 300
    
    def test_custom_config(self):
        """Test custom pipeline configuration"""
        config = PipelineConfig(
            mode=PipelineMode.GENERATE_ONLY,
            template_type=TemplateType.LANDING,
            include_assets=False,
            auto_deploy=False,
            max_concurrent=5
        )
        
        assert config.mode == PipelineMode.GENERATE_ONLY
        assert config.template_type == TemplateType.LANDING
        assert config.include_assets is False
        assert config.auto_deploy is False
        assert config.max_concurrent == 5


class TestPipelineResult:
    """Test pipeline result"""
    
    def test_pipeline_result_initialization(self):
        """Test pipeline result initialization"""
        result = PipelineResult(
            success=True,
            trend_id="test-trend",
            stage=PipelineStage.COMPLETED
        )
        
        assert result.success is True
        assert result.trend_id == "test-trend"
        assert result.stage == PipelineStage.COMPLETED
        assert result.stage_times == {}
        assert result.warnings == []
    
    def test_pipeline_result_with_data(self):
        """Test pipeline result with data"""
        content_result = ContentGenerationResult(
            success=True,
            content=GeneratedContent(
                trend_id="test-trend",
                title="Test Article",
                description="Test description",
                body="Test content",
                slug="test-article"
            ),
            status=ContentStatus.GENERATED
        )
        
        result = PipelineResult(
            success=True,
            trend_id="test-trend",
            stage=PipelineStage.COMPLETED,
            content_generation_result=content_result,
            total_time=45.5,
            stage_times={"content_generation": 30.0, "deployment": 15.5}
        )
        
        assert result.content_generation_result.success is True
        assert result.total_time == 45.5
        assert len(result.stage_times) == 2


class TestContentPipelineOrchestrator:
    """Test content pipeline orchestrator"""
    
    def setup_method(self):
        """Setup test environment"""
        self.orchestrator = ContentPipelineOrchestrator()
        
        # Mock dependencies
        self.orchestrator.content_pipeline = Mock()
        self.orchestrator.deployment_service = Mock()
        self.orchestrator.template_engine = Mock()
        self.orchestrator.asset_processor = Mock()
        self.orchestrator.git_manager = Mock()
        self.orchestrator.trend_repo = Mock()
        self.orchestrator.content_repo = Mock()
    
    def _create_test_trend(self) -> TrendEntity:
        """Create test trend entity"""
        return TrendEntity(
            id="test-trend-id",
            keyword="Test Trend",
            category="Technology",
            region="US",
            search_volume=1000,
            growth_rate=25.0,
            score=85.0,
            status="approved"
        )
    
    @pytest.mark.asyncio
    async def test_execute_pipeline_full_success(self):
        """Test successful full pipeline execution"""
        trend = self._create_test_trend()
        config = PipelineConfig(mode=PipelineMode.FULL_PIPELINE)
        
        # Mock content generation
        generated_content = GeneratedContent(
            trend_id=trend.id,
            title="Understanding Test Trend",
            description="A comprehensive guide",
            body="# Test Content",
            slug="understanding-test-trend",
            hero_image_url="https://example.com/hero.jpg"
        )
        
        content_result = ContentGenerationResult(
            success=True,
            content=generated_content,
            status=ContentStatus.GENERATED,
            assets=[
                ContentAsset(
                    filename="hero_optimized.jpg",
                    local_path="/tmp/hero.jpg",
                    mime_type="image/jpeg"
                )
            ]
        )
        
        self.orchestrator.content_pipeline.generate_content = AsyncMock(return_value=content_result)
        
        # Mock asset processing
        self.orchestrator.asset_processor.process_content_assets = AsyncMock(
            return_value=(generated_content, content_result.assets)
        )
        
        # Mock template rendering
        self.orchestrator.template_engine.create_mdx_content = Mock(
            return_value="# Rendered MDX Content"
        )
        
        # Mock deployment
        deployment_result = Mock()
        deployment_result.success = True
        deployment_result.commit_hash = "abc123"
        self.orchestrator.deployment_service.deploy_trend_content = AsyncMock(
            return_value=deployment_result
        )
        
        # Mock git status check
        self.orchestrator.git_manager.check_repository_status = AsyncMock(
            return_value={'accessible': True}
        )
        
        # Mock database operations
        self.orchestrator.content_repo.get_by_trend_id = AsyncMock(return_value=None)
        self.orchestrator.trend_repo.update = AsyncMock(return_value=None)
        self.orchestrator.asset_processor.cleanup_processed_assets = AsyncMock(return_value=None)
        
        # Execute pipeline
        result = await self.orchestrator.execute_pipeline(trend, config)
        
        # Verify result
        assert result.success is True
        assert result.trend_id == trend.id
        assert result.stage == PipelineStage.COMPLETED
        assert result.content_generation_result is not None
        assert result.deployment_result is not None
        assert result.total_time > 0
        assert len(result.stage_times) > 0
        
        # Verify method calls
        self.orchestrator.content_pipeline.generate_content.assert_called_once()
        self.orchestrator.asset_processor.process_content_assets.assert_called_once()
        self.orchestrator.deployment_service.deploy_trend_content.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_pipeline_generate_only(self):
        """Test pipeline execution in generate-only mode"""
        trend = self._create_test_trend()
        config = PipelineConfig(mode=PipelineMode.GENERATE_ONLY, auto_deploy=False)
        
        # Mock content generation
        generated_content = GeneratedContent(
            trend_id=trend.id,
            title="Test Article",
            description="Test description",
            body="Test content",
            slug="test-article"
        )
        
        content_result = ContentGenerationResult(
            success=True,
            content=generated_content,
            status=ContentStatus.GENERATED
        )
        
        self.orchestrator.content_pipeline.generate_content = AsyncMock(return_value=content_result)
        self.orchestrator.git_manager.check_repository_status = AsyncMock(
            return_value={'accessible': True}
        )
        self.orchestrator.content_repo.get_by_trend_id = AsyncMock(return_value=None)
        self.orchestrator.trend_repo.update = AsyncMock(return_value=None)
        
        # Execute pipeline
        result = await self.orchestrator.execute_pipeline(trend, config)
        
        # Verify result
        assert result.success is True
        assert result.stage == PipelineStage.COMPLETED
        assert result.content_generation_result is not None
        assert result.deployment_result is None  # No deployment in generate-only mode
        
        # Verify deployment was not called
        self.orchestrator.deployment_service.deploy_trend_content.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_execute_pipeline_content_generation_failure(self):
        """Test pipeline execution with content generation failure"""
        trend = self._create_test_trend()
        config = PipelineConfig()
        
        # Mock content generation failure
        content_result = ContentGenerationResult(
            success=False,
            error_message="AI service unavailable",
            status=ContentStatus.FAILED
        )
        
        self.orchestrator.content_pipeline.generate_content = AsyncMock(return_value=content_result)
        self.orchestrator.git_manager.check_repository_status = AsyncMock(
            return_value={'accessible': True}
        )
        self.orchestrator.content_repo.get_by_trend_id = AsyncMock(return_value=None)
        
        # Execute pipeline
        result = await self.orchestrator.execute_pipeline(trend, config)
        
        # Verify result
        assert result.success is False
        assert result.stage == PipelineStage.FAILED
        assert "Content generation failed" in result.error_message
        assert result.content_generation_result is not None
        assert result.deployment_result is None
    
    @pytest.mark.asyncio
    async def test_execute_pipeline_asset_processing_failure(self):
        """Test pipeline execution with asset processing failure"""
        trend = self._create_test_trend()
        config = PipelineConfig(include_assets=True)
        
        # Mock content generation success
        generated_content = GeneratedContent(
            trend_id=trend.id,
            title="Test Article",
            description="Test description",
            body="Test content",
            slug="test-article",
            hero_image_url="https://example.com/hero.jpg"
        )
        
        content_result = ContentGenerationResult(
            success=True,
            content=generated_content,
            status=ContentStatus.GENERATED
        )
        
        self.orchestrator.content_pipeline.generate_content = AsyncMock(return_value=content_result)
        
        # Mock asset processing failure
        self.orchestrator.asset_processor.process_content_assets = AsyncMock(
            side_effect=Exception("Asset processing failed")
        )
        
        # Mock other components
        self.orchestrator.template_engine.create_mdx_content = Mock(return_value="# Content")
        deployment_result = Mock()
        deployment_result.success = True
        self.orchestrator.deployment_service.deploy_trend_content = AsyncMock(
            return_value=deployment_result
        )
        self.orchestrator.git_manager.check_repository_status = AsyncMock(
            return_value={'accessible': True}
        )
        self.orchestrator.content_repo.get_by_trend_id = AsyncMock(return_value=None)
        self.orchestrator.trend_repo.update = AsyncMock(return_value=None)
        
        # Execute pipeline
        result = await self.orchestrator.execute_pipeline(trend, config)
        
        # Verify result - should succeed despite asset processing failure
        assert result.success is True
        assert result.stage == PipelineStage.COMPLETED
        assert len(result.warnings) > 0
        assert any("Asset processing failed" in warning for warning in result.warnings)
    
    @pytest.mark.asyncio
    async def test_execute_batch_pipeline(self):
        """Test batch pipeline execution"""
        trends = [self._create_test_trend() for _ in range(3)]
        config = PipelineConfig(max_concurrent=2)
        
        # Mock individual pipeline execution
        async def mock_execute_pipeline(trend, config):
            return PipelineResult(
                success=True,
                trend_id=trend.id,
                stage=PipelineStage.COMPLETED,
                total_time=30.0
            )
        
        self.orchestrator.execute_pipeline = AsyncMock(side_effect=mock_execute_pipeline)
        
        # Execute batch pipeline
        result = await self.orchestrator.execute_batch_pipeline(trends, config)
        
        # Verify result
        assert result['total_trends'] == 3
        assert result['successful'] == 3
        assert result['failed'] == 0
        assert len(result['results']) == 3
        assert result['total_time'] > 0
        
        # Verify individual executions
        assert self.orchestrator.execute_pipeline.call_count == 3
    
    @pytest.mark.asyncio
    async def test_execute_batch_pipeline_with_failures(self):
        """Test batch pipeline execution with some failures"""
        trends = [self._create_test_trend() for _ in range(3)]
        config = PipelineConfig()
        
        # Mock mixed results
        async def mock_execute_pipeline(trend, config):
            if trend.id.endswith('0'):  # First trend fails
                return PipelineResult(
                    success=False,
                    trend_id=trend.id,
                    stage=PipelineStage.FAILED,
                    error_message="Test failure"
                )
            else:
                return PipelineResult(
                    success=True,
                    trend_id=trend.id,
                    stage=PipelineStage.COMPLETED
                )
        
        self.orchestrator.execute_pipeline = AsyncMock(side_effect=mock_execute_pipeline)
        
        # Execute batch pipeline
        result = await self.orchestrator.execute_batch_pipeline(trends, config)
        
        # Verify result
        assert result['total_trends'] == 3
        assert result['successful'] == 2
        assert result['failed'] == 1
        assert len(result['errors']) >= 1
    
    @pytest.mark.asyncio
    async def test_get_pipeline_status(self):
        """Test getting pipeline status"""
        trend_id = "test-trend"
        
        # Add active pipeline
        pipeline_result = PipelineResult(
            success=False,  # Still running
            trend_id=trend_id,
            stage=PipelineStage.CONTENT_GENERATION,
            total_time=15.0
        )
        
        self.orchestrator.active_pipelines[trend_id] = pipeline_result
        
        # Get status
        status = await self.orchestrator.get_pipeline_status(trend_id)
        
        assert status is not None
        assert status.trend_id == trend_id
        assert status.stage == PipelineStage.CONTENT_GENERATION
        assert status.total_time == 15.0
    
    @pytest.mark.asyncio
    async def test_get_pipeline_status_not_found(self):
        """Test getting status for non-existent pipeline"""
        status = await self.orchestrator.get_pipeline_status("nonexistent")
        assert status is None
    
    @pytest.mark.asyncio
    async def test_get_active_pipelines(self):
        """Test getting all active pipelines"""
        # Add multiple active pipelines
        for i in range(3):
            trend_id = f"trend-{i}"
            pipeline_result = PipelineResult(
                success=False,
                trend_id=trend_id,
                stage=PipelineStage.CONTENT_GENERATION
            )
            self.orchestrator.active_pipelines[trend_id] = pipeline_result
        
        active = await self.orchestrator.get_active_pipelines()
        
        assert len(active) == 3
        assert all(isinstance(result, PipelineResult) for result in active.values())
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test pipeline orchestrator health check"""
        # Mock component health checks
        self.orchestrator.content_pipeline.health_check = AsyncMock(
            return_value={'status': 'healthy'}
        )
        self.orchestrator.deployment_service.health_check = AsyncMock(
            return_value={'status': 'healthy'}
        )
        self.orchestrator.asset_processor.get_asset_stats = AsyncMock(
            return_value={'storage': {'exists': True}}
        )
        self.orchestrator.template_engine.validate_template = Mock(return_value=True)
        
        health = await self.orchestrator.health_check()
        
        assert health['status'] == 'healthy'
        assert 'components' in health
        assert 'active_pipelines' in health
        assert 'timestamp' in health
        assert health['components']['content_pipeline'] == 'healthy'
        assert health['components']['deployment_service'] == 'healthy'
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy(self):
        """Test health check with unhealthy components"""
        # Mock unhealthy components
        self.orchestrator.content_pipeline.health_check = AsyncMock(
            return_value={'status': 'unhealthy'}
        )
        self.orchestrator.deployment_service.health_check = AsyncMock(
            return_value={'status': 'healthy'}
        )
        self.orchestrator.asset_processor.get_asset_stats = AsyncMock(
            return_value={'storage': {'exists': False}}
        )
        self.orchestrator.template_engine.validate_template = Mock(return_value=False)
        
        health = await self.orchestrator.health_check()
        
        assert health['status'] == 'unhealthy'
        assert health['components']['content_pipeline'] == 'unhealthy'
        assert health['components']['asset_processor'] == 'unhealthy'
        assert health['components']['template_engine'] == 'unhealthy'


if __name__ == "__main__":
    pytest.main([__file__])

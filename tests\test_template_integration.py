"""
Integration tests for template system with content generator
"""

import pytest
import asyncio
import tempfile
import os
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch

from generator.templates.engine import MDXTemplateEngine
from generator.utils.slug_generator import SlugGenerator
from generator.utils.text_processor import TextProcessor
from generator.models import ContentRequest, GeneratedContent, TemplateContext


class TestTemplateIntegration:
    """Integration tests for template system components"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create template directory structure
        templates_dir = Path(self.temp_dir) / "templates"
        templates_dir.mkdir(exist_ok=True)
        
        # Create article template
        article_template = templates_dir / "article.mdx"
        article_template.write_text("""---
title: "{{ title }}"
description: "{{ description }}"
slug: "{{ slug }}"
publishedAt: "{{ frontmatter.publishedAt }}"
tags: {{ frontmatter.tags | to_json }}
category: "{{ frontmatter.category }}"
region: "{{ frontmatter.region }}"
---

# {{ title }}

{% if hero_image_url %}
![{{ hero_image_alt or title }}]({{ hero_image_url }})
{% endif %}

{{ body }}

{% if code_snippet and code_language %}
## Code Example

```{{ code_language }}
{{ code_snippet }}
```
{% endif %}

## Tags
{% for tag in tags %}
- {{ tag }}
{% endfor %}
""")
        
        # Initialize components
        self.engine = MDXTemplateEngine(template_dir=str(templates_dir))
        self.slug_generator = SlugGenerator()
        self.text_processor = TextProcessor()
    
    def test_complete_content_generation_workflow(self):
        """Test complete workflow from content data to MDX file"""
        # Sample trend data
        trend_data = {
            "keyword": "Quantum Computing",
            "category": "Technology", 
            "region": "US",
            "search_volume": 5000,
            "growth_rate": 45.2,
            "score": 92
        }
        
        # Sample content data
        content_data = {
            "title": "The Future of Quantum Computing",
            "description": "Exploring the revolutionary potential of quantum computing technology",
            "body": """
            Quantum computing represents a paradigm shift in computational technology.
            Unlike classical computers that use bits, quantum computers use quantum bits
            or qubits that can exist in multiple states simultaneously. This property,
            known as superposition, allows quantum computers to process vast amounts
            of information in parallel.
            
            The implications for cryptography, drug discovery, and artificial intelligence
            are profound. Major tech companies are investing billions in quantum research.
            """,
            "slug": "future-of-quantum-computing",
            "tags": ["quantum", "computing", "technology", "future"],
            "hero_image_url": "https://example.com/quantum-computer.jpg",
            "hero_image_alt": "Quantum computer in laboratory",
            "code_snippet": """
# Quantum circuit example using Qiskit
from qiskit import QuantumCircuit, execute, Aer

# Create a quantum circuit with 2 qubits
qc = QuantumCircuit(2, 2)

# Apply Hadamard gate to first qubit
qc.h(0)

# Apply CNOT gate
qc.cx(0, 1)

# Measure qubits
qc.measure_all()
            """,
            "code_language": "python",
            "word_count": 150,
            "readability_score": 65.5,
            "ai_model_used": "gpt-3.5-turbo"
        }
        
        # Generate MDX content
        mdx_content = self.engine.create_mdx_content("article.mdx", trend_data, content_data)
        
        # Verify frontmatter is present
        assert mdx_content.startswith("---")
        assert "title: \"The Future of Quantum Computing\"" in mdx_content
        assert "category: \"Technology\"" in mdx_content
        assert "region: \"US\"" in mdx_content
        
        # Verify content body is present
        assert "# The Future of Quantum Computing" in mdx_content
        assert "Quantum computing represents" in mdx_content
        assert "```python" in mdx_content
        assert "from qiskit import" in mdx_content
        
        # Verify tags are rendered
        assert "- quantum" in mdx_content
        assert "- computing" in mdx_content
    
    def test_template_context_creation(self):
        """Test creation of template context from various data sources"""
        # Create template context
        context = TemplateContext(
            keyword="Machine Learning",
            category="Technology",
            region="UK",
            search_volume=3000,
            growth_rate=35.8,
            score=88,
            title="Understanding Machine Learning",
            description="A comprehensive guide to ML concepts",
            body="Machine learning is a subset of artificial intelligence...",
            slug="understanding-machine-learning",
            tags=["ml", "ai", "data-science"],
            hero_image_url="https://example.com/ml-image.jpg",
            word_count=1200,
            readability_score=72.0,
            ai_model_used="gpt-4"
        )
        
        # Verify context data
        assert context.keyword == "Machine Learning"
        assert context.category == "Technology"
        assert context.search_volume == 3000
        assert context.tags == ["ml", "ai", "data-science"]
        assert context.word_count == 1200
    
    def test_slug_generation_integration(self):
        """Test slug generation with text processing"""
        # Test various title formats
        titles = [
            "The Future of Artificial Intelligence in Healthcare",
            "5 Ways Machine Learning is Transforming Business",
            "Understanding Blockchain Technology: A Beginner's Guide",
            "Why Quantum Computing Will Change Everything"
        ]
        
        for title in titles:
            slug = self.slug_generator.create_base_slug(title)
            
            # Verify slug format
            assert self.slug_generator.validate_slug_format(slug)
            assert len(slug) <= 50
            assert not slug.startswith('-')
            assert not slug.endswith('-')
            assert '--' not in slug
    
    def test_text_processing_integration(self):
        """Test text processing with content generation"""
        sample_content = """
        Artificial Intelligence (AI) is revolutionizing industries worldwide.
        Machine learning algorithms enable computers to learn from data without
        explicit programming. Deep learning, a subset of machine learning,
        uses neural networks with multiple layers to process complex patterns.
        
        These technologies are being applied in healthcare for medical diagnosis,
        in finance for fraud detection, and in transportation for autonomous vehicles.
        The future of AI looks promising with continued advances in computational
        power and algorithmic sophistication.
        """
        
        # Extract tags
        tags = self.text_processor.extract_tags(
            sample_content, 
            keyword="Artificial Intelligence",
            category="Technology",
            max_tags=6
        )
        
        assert "artificial intelligence" in [tag.lower() for tag in tags]
        assert "technology" in [tag.lower() for tag in tags]
        assert len(tags) <= 6
        
        # Calculate reading time
        reading_time = self.text_processor.calculate_reading_time(sample_content)
        assert reading_time >= 1
        
        # Calculate readability
        readability = self.text_processor.calculate_readability_score(sample_content)
        assert 0 <= readability <= 100
        
        # Extract keywords
        keywords = self.text_processor.extract_keywords(sample_content, max_keywords=5)
        assert len(keywords) <= 5
        assert any("artificial" in keyword.lower() or "intelligence" in keyword.lower() 
                  for keyword in keywords)
    
    def test_template_rendering_with_missing_data(self):
        """Test template rendering with partial data"""
        # Minimal trend data
        trend_data = {
            "keyword": "Blockchain",
            "category": "Technology",
            "region": "Global"
        }
        
        # Minimal content data
        content_data = {
            "title": "Blockchain Basics",
            "description": "Introduction to blockchain technology",
            "body": "Blockchain is a distributed ledger technology...",
            "slug": "blockchain-basics",
            "tags": ["blockchain", "crypto"]
            # Note: Missing hero_image_url, code_snippet, etc.
        }
        
        # Should still generate valid MDX content
        mdx_content = self.engine.create_mdx_content("article.mdx", trend_data, content_data)
        
        assert "title: \"Blockchain Basics\"" in mdx_content
        assert "# Blockchain Basics" in mdx_content
        assert "Blockchain is a distributed ledger" in mdx_content
        
        # Should handle missing optional fields gracefully
        assert "![" not in mdx_content  # No image since hero_image_url is missing
        assert "```" not in mdx_content  # No code block since code_snippet is missing
    
    def test_template_filter_functionality(self):
        """Test custom template filters in realistic scenarios"""
        # Test slugify filter
        test_titles = [
            "AI & Machine Learning: The Future is Now!",
            "Understanding APIs: REST vs GraphQL",
            "5 Python Tips Every Developer Should Know"
        ]
        
        for title in test_titles:
            slug = self.engine.slugify_filter(title)
            assert self.slug_generator.validate_slug_format(slug)
        
        # Test truncate_words filter
        long_description = """
        This is a very long description that contains many words and should be
        truncated to a reasonable length for use in meta descriptions and excerpts.
        It continues with more content that should be cut off.
        """
        
        truncated = self.engine.truncate_words_filter(long_description, 20)
        word_count = len(truncated.split())
        assert word_count <= 23  # 20 words + "..." counts as 3 more
        
        # Test format_date filter
        test_date = datetime(2024, 3, 15, 14, 30, 0)
        formatted = self.engine.format_date_filter(test_date, "%B %d, %Y")
        assert formatted == "March 15, 2024"
        
        # Test extract_excerpt filter
        long_text = """
        Artificial intelligence is transforming the way we work and live.
        From healthcare to finance, AI applications are becoming increasingly
        sophisticated and widespread. Machine learning algorithms can now
        process vast amounts of data to identify patterns and make predictions
        that were previously impossible for humans to achieve.
        """
        
        excerpt = self.engine.extract_excerpt_filter(long_text, 100)
        assert len(excerpt) <= 100
        assert "Artificial intelligence" in excerpt
    
    def test_error_handling_in_template_rendering(self):
        """Test error handling during template rendering"""
        # Test with invalid template
        with pytest.raises(Exception):  # Should raise TemplateError
            self.engine.render_template("nonexistent.mdx", {})
        
        # Test with missing required variables
        # This should not raise an error but should handle gracefully
        minimal_context = {"title": "Test"}
        try:
            result = self.engine.render_template("article.mdx", minimal_context)
            # Should complete without error, using default values
            assert "Test" in result
        except Exception as e:
            # If it does error, it should be a clear template error
            assert "template" in str(e).lower() or "variable" in str(e).lower()


if __name__ == "__main__":
    pytest.main([__file__])

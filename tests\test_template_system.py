"""
Tests for the template system implementation
"""

import pytest
import asyncio
import tempfile
import os
from datetime import datetime
from pathlib import Path

from generator.templates.engine import MDXTemplateEngine, TemplateError
from generator.utils.slug_generator import SlugGenerator, generate_unique_slug
from generator.utils.text_processor import TextProcessor, extract_tags, calculate_reading_time
from generator.models import ContentRequest, GeneratedContent, TemplateContext


class TestMDXTemplateEngine:
    """Test cases for MDX Template Engine"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.engine = MDXTemplateEngine(template_dir=self.temp_dir)
        
        # Create a simple test template
        self.test_template_content = """---
title: "{{ title }}"
description: "{{ description }}"
slug: "{{ slug }}"
---

# {{ title }}

{{ body }}

{% if code_snippet %}
```{{ code_language }}
{{ code_snippet }}
```
{% endif %}
"""
        
        template_path = Path(self.temp_dir) / "test.mdx"
        template_path.write_text(self.test_template_content)
    
    def test_template_engine_initialization(self):
        """Test template engine initialization"""
        assert self.engine is not None
        assert self.engine.template_dir == self.temp_dir
        assert self.engine.env is not None
    
    def test_custom_filters(self):
        """Test custom Jinja2 filters"""
        # Test slugify filter
        slug = self.engine.slugify_filter("Hello World Test")
        assert slug == "hello-world-test"
        
        # Test truncate words filter
        text = "This is a long text that should be truncated"
        truncated = self.engine.truncate_words_filter(text, 5)
        assert truncated == "This is a long text..."
        
        # Test format date filter
        date_obj = datetime(2024, 1, 15, 12, 30, 0)
        formatted = self.engine.format_date_filter(date_obj, "%Y-%m-%d")
        assert formatted == "2024-01-15"
        
        # Test to_json filter
        data = {"key": "value", "number": 123}
        json_str = self.engine.to_json_filter(data)
        assert '"key": "value"' in json_str
        assert '"number": 123' in json_str
    
    def test_template_rendering(self):
        """Test basic template rendering"""
        context = {
            "title": "Test Article",
            "description": "A test article description",
            "slug": "test-article",
            "body": "This is the main content of the article.",
            "code_snippet": "print('Hello, World!')",
            "code_language": "python"
        }
        
        rendered = self.engine.render_template("test.mdx", context)
        
        assert "# Test Article" in rendered
        assert "This is the main content" in rendered
        assert "print('Hello, World!')" in rendered
        assert "```python" in rendered
    
    def test_frontmatter_generation(self):
        """Test frontmatter generation"""
        trend_data = {
            "keyword": "AI Technology",
            "category": "Technology",
            "region": "US",
            "search_volume": 1000,
            "growth_rate": 25.5,
            "score": 85
        }
        
        content_data = {
            "title": "Understanding AI Technology",
            "description": "A comprehensive guide to AI technology",
            "slug": "understanding-ai-technology",
            "tags": ["ai", "technology", "machine-learning"],
            "hero_image_url": "https://example.com/image.jpg",
            "word_count": 1500,
            "readability_score": 75.0,
            "ai_model_used": "gpt-3.5-turbo"
        }
        
        frontmatter = self.engine.generate_frontmatter(trend_data, content_data)
        
        assert frontmatter["title"] == "Understanding AI Technology"
        assert frontmatter["category"] == "Technology"
        assert frontmatter["region"] == "US"
        assert frontmatter["trending"]["keyword"] == "AI Technology"
        assert frontmatter["trending"]["searchVolume"] == 1000
        assert frontmatter["seo"]["metaTitle"] == "Understanding AI Technology"
    
    def test_mdx_content_creation(self):
        """Test complete MDX content creation"""
        trend_data = {
            "keyword": "Machine Learning",
            "category": "Technology",
            "region": "US"
        }
        
        content_data = {
            "title": "Machine Learning Basics",
            "description": "Learn the fundamentals of machine learning",
            "slug": "machine-learning-basics",
            "body": "Machine learning is a subset of artificial intelligence...",
            "tags": ["ml", "ai", "data-science"]
        }
        
        mdx_content = self.engine.create_mdx_content("test.mdx", trend_data, content_data)
        
        assert "---" in mdx_content  # Frontmatter delimiters
        assert "title: \"Machine Learning Basics\"" in mdx_content
        assert "# Machine Learning Basics" in mdx_content
        assert "Machine learning is a subset" in mdx_content
    
    def test_template_validation(self):
        """Test template validation"""
        # Valid template should return True
        assert self.engine.validate_template("test.mdx") is True
        
        # Non-existent template should return False
        assert self.engine.validate_template("nonexistent.mdx") is False
    
    def test_extract_excerpt_filter(self):
        """Test excerpt extraction filter"""
        long_text = """
        This is a long article about artificial intelligence and machine learning.
        It contains multiple sentences and paragraphs. The content discusses various
        aspects of AI technology and its applications in modern society. This should
        be truncated to a reasonable length for use as an excerpt.
        """
        
        excerpt = self.engine.extract_excerpt_filter(long_text, 100)
        assert len(excerpt) <= 100
        assert "artificial intelligence" in excerpt


class TestSlugGenerator:
    """Test cases for Slug Generator"""
    
    def setup_method(self):
        """Setup test environment"""
        self.generator = SlugGenerator()
    
    def test_create_base_slug(self):
        """Test base slug creation"""
        slug = self.generator.create_base_slug("Hello World Test Article")
        assert slug == "hello-world-test-article"
        
        # Test with special characters
        slug = self.generator.create_base_slug("AI & Machine Learning: A Guide!")
        assert slug == "ai-machine-learning-a-guide"
        
        # Test empty input
        slug = self.generator.create_base_slug("")
        assert slug == "untitled"
    
    def test_slug_format_validation(self):
        """Test slug format validation"""
        # Valid slugs
        assert self.generator.validate_slug_format("valid-slug") is True
        assert self.generator.validate_slug_format("another-valid-slug-123") is True
        
        # Invalid slugs
        assert self.generator.validate_slug_format("") is False
        assert self.generator.validate_slug_format("ab") is False  # Too short
        assert self.generator.validate_slug_format("slug with spaces") is False
        assert self.generator.validate_slug_format("-starts-with-hyphen") is False
        assert self.generator.validate_slug_format("ends-with-hyphen-") is False
        assert self.generator.validate_slug_format("has--double-hyphens") is False
    
    def test_reserved_slug_detection(self):
        """Test reserved slug detection"""
        assert self.generator.is_reserved_slug("admin") is True
        assert self.generator.is_reserved_slug("api") is True
        assert self.generator.is_reserved_slug("www") is True
        assert self.generator.is_reserved_slug("custom-content") is False
    
    def test_keyword_extraction_for_slug(self):
        """Test keyword extraction for slug generation"""
        text = "The future of artificial intelligence and machine learning"
        keywords = self.generator.extract_keywords_for_slug(text, max_keywords=3)
        assert "future" in keywords
        assert "artificial" in keywords
        assert "intelligence" in keywords
        # Stop words should be filtered out
        assert "the" not in keywords
        assert "of" not in keywords


class TestTextProcessor:
    """Test cases for Text Processor"""
    
    def setup_method(self):
        """Setup test environment"""
        self.processor = TextProcessor()
    
    def test_text_cleaning(self):
        """Test text cleaning functionality"""
        dirty_text = "<p>This is <strong>HTML</strong> text with **markdown** and extra   spaces.</p>"
        clean_text = self.processor.clean_text(dirty_text)
        
        assert "<p>" not in clean_text
        assert "<strong>" not in clean_text
        assert "**" not in clean_text
        assert "  " not in clean_text  # No double spaces
    
    def test_keyword_extraction(self):
        """Test keyword extraction"""
        text = """
        Artificial intelligence and machine learning are transforming technology.
        These technologies enable computers to learn and make decisions automatically.
        Machine learning algorithms process data to identify patterns and insights.
        """
        
        keywords = self.processor.extract_keywords(text, max_keywords=5)
        assert "artificial" in keywords or "intelligence" in keywords
        assert "machine" in keywords or "learning" in keywords
        assert "technology" in keywords or "technologies" in keywords
        
        # Stop words should be filtered out
        assert "and" not in keywords
        assert "the" not in keywords
    
    def test_tag_extraction(self):
        """Test tag extraction from content"""
        text = """
        This article discusses artificial intelligence, machine learning, and data science.
        These technologies are revolutionizing various industries including healthcare,
        finance, and transportation. Deep learning and neural networks are key components.
        """
        
        tags = extract_tags(text, keyword="AI Technology", category="Technology", max_tags=6)
        
        assert "ai technology" in [tag.lower() for tag in tags]
        assert "technology" in [tag.lower() for tag in tags]
        assert len(tags) <= 6
    
    def test_reading_time_calculation(self):
        """Test reading time calculation"""
        # Short text (should be minimum 1 minute)
        short_text = "This is a short text."
        reading_time = calculate_reading_time(short_text)
        assert reading_time == 1
        
        # Longer text (approximately 400 words = 2 minutes at 200 WPM)
        long_text = " ".join(["word"] * 400)
        reading_time = calculate_reading_time(long_text)
        assert reading_time == 2
    
    def test_readability_score_calculation(self):
        """Test readability score calculation"""
        # Simple text should have high readability
        simple_text = "This is simple text. It has short sentences. Easy to read."
        score = self.processor.calculate_readability_score(simple_text)
        assert score > 50  # Should be reasonably readable
        
        # Complex text should have lower readability
        complex_text = """
        The implementation of sophisticated algorithmic methodologies in contemporary
        computational frameworks necessitates comprehensive understanding of underlying
        mathematical principles and their practical applications in real-world scenarios.
        """
        complex_score = self.processor.calculate_readability_score(complex_text)
        assert complex_score < score  # Should be less readable than simple text
    
    def test_sentence_extraction(self):
        """Test sentence extraction"""
        text = "First sentence. Second sentence! Third sentence? Fourth sentence."
        sentences = self.processor.extract_sentences(text)
        
        assert len(sentences) == 4
        assert "First sentence" in sentences[0]
        assert "Second sentence" in sentences[1]
    
    def test_key_phrase_extraction(self):
        """Test key phrase extraction"""
        text = """
        Machine learning algorithms are powerful tools for data analysis.
        These algorithms can identify patterns in large datasets automatically.
        Data scientists use machine learning for predictive modeling tasks.
        """
        
        phrases = self.processor.extract_key_phrases(text, max_phrases=3)
        
        # Should contain meaningful 2-3 word phrases
        phrase_text = " ".join(phrases).lower()
        assert "machine learning" in phrase_text or "learning algorithms" in phrase_text
    
    def test_content_summary_generation(self):
        """Test content summary generation"""
        long_text = """
        Artificial intelligence is transforming industries worldwide. Machine learning
        algorithms enable computers to learn from data without explicit programming.
        Deep learning, a subset of machine learning, uses neural networks with multiple
        layers to process complex patterns. These technologies are being applied in
        healthcare, finance, transportation, and many other sectors. The future of AI
        looks promising with continued advances in computational power and algorithms.
        """
        
        summary = self.processor.generate_summary(long_text, max_sentences=2)
        sentences = summary.split(". ")
        assert len(sentences) <= 3  # Including the final period
        assert "Artificial intelligence" in summary


@pytest.mark.asyncio
class TestAsyncFunctionality:
    """Test async functionality of the template system"""
    
    async def test_async_slug_generation(self):
        """Test async slug generation"""
        # Note: This test would require a mock database
        # For now, we'll test the basic functionality
        slug = await generate_unique_slug("Test Article Title")
        assert slug == "test-article-title"
    
    async def test_slug_conflict_resolution(self):
        """Test slug conflict resolution"""
        # This would require mocking the database to simulate conflicts
        # For now, we'll test the basic case
        generator = SlugGenerator()
        slug = await generator.generate_unique_slug("Test Content")
        assert generator.validate_slug_format(slug)


if __name__ == "__main__":
    pytest.main([__file__])
